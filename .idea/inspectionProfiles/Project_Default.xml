<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="HtmlUnknownAttribute" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="myValues">
        <value>
          <list size="4">
            <item index="0" class="java.lang.String" itemvalue="name" />
            <item index="1" class="java.lang.String" itemvalue="src" />
            <item index="2" class="java.lang.String" itemvalue="mode" />
            <item index="3" class="java.lang.String" itemvalue="style" />
          </list>
        </value>
      </option>
      <option name="myCustomValuesEnabled" value="true" />
    </inspection_tool>
    <inspection_tool class="HtmlUnknownTag" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="myValues">
        <value>
          <list size="18">
            <item index="0" class="java.lang.String" itemvalue="nobr" />
            <item index="1" class="java.lang.String" itemvalue="noembed" />
            <item index="2" class="java.lang.String" itemvalue="comment" />
            <item index="3" class="java.lang.String" itemvalue="noscript" />
            <item index="4" class="java.lang.String" itemvalue="embed" />
            <item index="5" class="java.lang.String" itemvalue="script" />
            <item index="6" class="java.lang.String" itemvalue="block" />
            <item index="7" class="java.lang.String" itemvalue="swiper" />
            <item index="8" class="java.lang.String" itemvalue="swiper-item" />
            <item index="9" class="java.lang.String" itemvalue="scroll-view" />
            <item index="10" class="java.lang.String" itemvalue="u-parse" />
            <item index="11" class="java.lang.String" itemvalue="navigator" />
            <item index="12" class="java.lang.String" itemvalue="checkbox-group" />
            <item index="13" class="java.lang.String" itemvalue="checkbox" />
            <item index="14" class="java.lang.String" itemvalue="uni-rate" />
            <item index="15" class="java.lang.String" itemvalue="uni-collapse" />
            <item index="16" class="java.lang.String" itemvalue="uni-collapse-item" />
            <item index="17" class="java.lang.String" itemvalue="web-view" />
          </list>
        </value>
      </option>
      <option name="myCustomValuesEnabled" value="true" />
    </inspection_tool>
    <inspection_tool class="SpellCheckingInspection" enabled="true" level="TYPO" enabled_by_default="true">
      <scope name="All Changed Files" level="TYPO" enabled="true">
        <option name="processCode" value="true" />
        <option name="processLiterals" value="true" />
        <option name="processComments" value="true" />
      </scope>
      <option name="processCode" value="true" />
      <option name="processLiterals" value="true" />
      <option name="processComments" value="true" />
    </inspection_tool>
  </profile>
</component>