@import "./public/colorui/main.css";
@import "./public/colorui/icon.css";
@import "./public/colorui/animation.css";
@import "./public/iconfont/iconfont.css";


.flex-four {
	flex: 4
}

.overflow {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.overflow-1 {
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 1;
	-webkit-box-orient: vertical;
}

.overflow-2 {
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
}

.display-ib {
	display: inline-block
}

.display-i {
	display: inline
}

.margin-top-bar {
	margin-top: 80rpx
}

.margin-bottom-bar {
	margin-bottom: 80rpx
}

.vertical-center {
	margin: auto 0rpx
}

.text-decorat {
	text-decoration: line-through;
}

.mar-top-30 {
	margin-top: -30rpx !important
}

.basis-3 {
	flex-basis: 30%;
}

.basis-7 {
	flex-basis: 70%;
}

.bg-grey-light{
	background-color: #f7f7f7;
}

.bg-gradual-red {
	background-image: linear-gradient(90deg, #f43f3b, #ec008c);
	color: #ffffff;
}

.bg-gradual-orange {
	background-image: linear-gradient(90deg, #ff9700, #ed1c24);
	color: #ffffff;
}

.bg-gradual-green {
	background-image: linear-gradient(90deg, #39b54a, #8dc63f);
	color: #ffffff;
}

.bg-gradual-purple {
	background-image: linear-gradient(90deg, #9000ff, #5e00ff);
	/* background-image: linear-gradient(90deg, #7764f5, #857bf4); */
	color: #ffffff;
}

.bg-gradual-pink {
	background-image: linear-gradient(90deg, #ec008c, #6739b6);
	color: #ffffff;
}

.bg-gradual-darkblue {
	background-image: linear-gradient(90deg, #0055ff, #1cbbb4);
	color: #ffffff;
}

.bg-gradual-blue {
	background-image: linear-gradient(90deg, #0081ff, #1cbbb4);
	color: #ffffff;
}

.bg-darkblue {
	background-color: #0055ff;
	color: #ffffff;
}

.bg-gradual-scarlet {
	 background-image: linear-gradient(90deg, #fb2d1a, #ff553f);
	color: #ffffff;
}

.bg-scarlet {
	background-color: #f42121;
	color: #ffffff;
}

.text-scarlet {
	color: #f42121;
}

.radius-lg{
	border-radius: 12rpx;
}

.top-radius-lg{
	border-radius: 12rpx 12rpx 0rpx 0rpx;
}

.bottom-radius-lg{
	border-radius: 0rpx 0rpx 12rpx 12rpx;
}

.cu-steps.steps-bottom .cu-item::before,
.cu-steps.steps-bottom .cu-item::after {
	left: -10upx;
	bottom: 70upx;
	top: initial;
}

.saleType{
	margin-top: -5px;
}

/* #ifdef H5 */
uni-checkbox[disabled] .uni-checkbox-wrapper .uni-checkbox-input {
	background: #d5d3d8 !important;
}

uni-toast {
	z-index: 9999;
}

/* #endif */


/* #ifdef APP-PLUS || MP-WEIXIN */
checkbox .wx-checkbox-input.wx-checkbox-input-checked{
	background-color: #FF6203 !important;
	border: 1px solid #FF6203 !important;
	/* color: #FFFFFF !important; */
}
radio .wx-radio-input.wx-radio-input-checked{
	background-color: #FF6203 !important;
	border-color: #FF6203 !important;
	/* background-clip: content-box !important;
	padding: 6rpx!important;
	box-sizing: border-box; */
}
/**
* 去除选中后的对号
*/
/* radio::before{
	content: '' !important;
} */
/* #endif */
