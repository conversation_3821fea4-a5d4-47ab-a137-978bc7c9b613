<template>
    <view class="Accordion w100 df flc jc-fs alc bc1">
        <view class="acc-title w100 df flr jc-sb alc" @click="collapseChange">
            <view class="title-text dfc">
                <text class="fc5">{{ title }}</text>
            </view>
            <view class="title-icon dfc">
                <image src="@/static/public/icon/youjiantou.png"></image>
            </view>
        </view>
        <scroll-view class="acc-con-box w100" :class="collapse?'acc-con-boxs':''" scroll-y>
            <slot></slot>
        </scroll-view>
    </view>
</template>

<script name="Accordion">
export default {
    props: {
        title: {
            type: String,
            default: '标题'
        }
    },
    data() {
        return {
            collapse: false
        }
    },
    onLoad() {
    },
    methods: {
        collapseChange() {
            this.collapse = !this.collapse
        }
    }
}
</script>

<style lang="scss" scoped>
.Accordion {
    border-radius: 20rpx;
    .acc-title {
        padding: 20rpx;
        .title-text {
            text {
                font-weight: 500;
                font-size: 28rpx;
            }
        }
        .title-icon {
            image {
                width: 12rpx;
                height: 20rpx;
            }
        }
    }

    .acc-con-box {
        max-height: 0;
        transition: max-height 1.2s;
    }

    .acc-con-boxs {
        max-height: 400rpx;
    }
}
</style>