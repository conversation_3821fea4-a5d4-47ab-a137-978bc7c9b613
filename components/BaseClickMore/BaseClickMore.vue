<template>
    <view class="BaseClickMore dfc">
        <view class="more-icon dfc" @click="showMoreClick">
            <image src="@/static/public/icon/more.png"></image>
        </view>
        <view class="more-con-box dfc" v-show="showMoreCon">
            <scroll-view class="more-scroll-box" scroll-y>
                <view class="more-list w100 df flc jc-fs alc bc1">
                    <block v-for="(item,index) in moreList" :key="index">
                        <view class="more-item dfc" @click="selCurCon">
                            <text>{{ item.label }}</text>
                        </view>
                    </block>
                </view>
            </scroll-view>
        </view>
    </view>
</template>

<script name="BaseClickMore">
export default {
    data() {
        return {
            showMoreCon: false,
            moreList: [
                {
                    label: "举报",
                    value: 0
                }
            ]
        }
    },
    onLoad() {
    },
    methods: {
        selCurCon() {
            this.showMoreCon = false
            this.$emit('change')
        },
        showMoreClick() {
            this.showMoreCon = true
        }
    }
}
</script>

<style lang="scss" scoped>
.BaseClickMore {
    position: relative;
    .more-icon {
        image {
            width: 30rpx;
            height: 6rpx;
        }
    }
    .more-con-box {
        position: absolute;
        top: -55rpx;
        right: -20rpx;
        .more-scroll-box {
            width: 150rpx;
            max-height: 300rpx;
            .more-list {
                border-radius: 20rpx;
                padding: 10rpx;
                .more-item {
                    padding: 10rpx 20rpx;
                    border-bottom: 2rpx solid #EDEDED;
                }
            }
        }
    }
}
</style>