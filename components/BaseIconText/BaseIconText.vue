<template>
    <view class="BaseIconText">
        <view class="icon-text-one dfc" @click="iconTextClick">
            <view class="text-pic dfc">
                <image :src="`../../static/${icon}`"></image>
            </view>
            <view class="text-con dfc">
                <text class="fc6">{{ text }}</text>
            </view>
        </view>
    </view>
</template>

<script name="BaseIconText">
export default {
    props: {
        icon: {
            type: String,
            default: ''
        },
        text: {
            type: String,
            default: ''
        }
    },
    data() {
        return {}
    },
    onLoad() {
    },
    methods: {
        iconTextClick() {
            this.$emit('packClick')
        }
    }
}
</script>

<style lang="scss" scoped>
.BaseIconText {
    .icon-text-one {
        .text-pic {
            image {
                width: 20rpx;
                height: 20rpx;
            }
        }
        .text-con {
            padding-left: 7rpx;
            text {
                font-size: 24rpx;
                font-weight: 500;
            }
        }
    }
}
</style>