<template>
    <view class="BaseLabel">
        <view class="label-info dfc type1" v-if="type === '1'">
            <text>{{ text }}</text>
        </view>
        <view class="label-info dfc type2" v-else-if="type === '2'">
            <text>{{ text }}</text>
        </view>
    </view>
</template>

<script name="BaseLabel">
export default {
    props: {
        type: {
            type: String,
            default: '1'
        },
        text: {
            type: String,
            default: ''
        }
    },
    data() {
        return {}
    },
    onLoad() {
    },
    methods: {}
}
</script>

<style lang="scss" scoped>
.BaseLabel {
    .label-info {
        border-radius: 6rpx;
        text {
            font-size: 24rpx;
        }
    }
    .type1 {
        background-color: #FEE6BA;
        padding: 8rpx 10rpx;
        text {
            color: #975C22;
        }
    }
    .type2 {
        padding: 8rpx 10rpx;
        border: 2rpx solid #FF6203;
        text {
            color: #FF6203;
        }
    }
}
</style>