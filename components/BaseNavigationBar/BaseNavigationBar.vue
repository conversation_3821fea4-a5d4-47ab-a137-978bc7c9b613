<template>
    <view class="BaseNavigationBar w100" :style="{ height: `${CustomBar}px` }">
        <view class="nav-bar-con nav-bar-one w100" :style="{height: `${CustomBar}px`, paddingTop: `${StatusBar}px`}" v-if="type === '1'">
            <view class="back-icon df flr jc-fs alc" @click.stop="goBack">
                <image src="@/static/public/icon/fanhui.png"></image>
            </view>
            <view class="nav-title dfc">
                <text class="fc1">{{ title }}</text>
            </view>
            <view class="nav-right"></view>
        </view>
        <view class="nav-bar-con nav-bar-two w100" :style="{height: `${CustomBar}px`, paddingTop: `${StatusBar}px`}" v-if="type === '2'">
            <view class="back-icon df flr jc-fs alc" @click.stop="goBack">
                <image src="@/static/public/icon/xiangshang.png"></image>
            </view>
            <view class="nav-title dfc">
                <text class="fc9">{{ title }}</text>
            </view>
            <view class="nav-right"></view>
        </view>
    </view>
</template>

<script name="BaseNavigationBar">

import {go} from "@/utils/customUtil";

export default {
    props: {
        type: {
            type: String,
            default: '1'
        },
        title: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            StatusBar: this.StatusBar,
            CustomBar: this.CustomBar
        }
    },
    onLoad() {
    },
    methods: {
        goBack() {
            let pages = getCurrentPages()
            if (pages.length <= 1 || pages[pages.length - 1].route == 'pages/login/index') {
                //判断是否能返回
                go('/pages/home/<USER>' ,4)
            } else {
                go(1, 5)
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.BaseNavigationBar {
    .nav-bar-con {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        padding: 0 30rpx;
        position: fixed;
        top: 0;
        left: 0;
        z-index: 100000;
        .back-icon {
            image {
                width: 40rpx;
                height: 32rpx;
            }
        }

        .nav-title {
            text {
                //font-size: 40rpx;
                font-size: 32rpx;
                font-weight: 500;
            }
        }
    }
    .nav-bar-one {
        background: linear-gradient(150deg, #FF8B00, #FF5000);
    }
    .nav-bar-two {
        .back-icon {
            image {
                width: 30rpx;
                height: 20rpx;
                transform: rotate(-90deg);
            }
        }
    }
}
</style>