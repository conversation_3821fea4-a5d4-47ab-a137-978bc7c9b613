<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view :class="'text-' + size + ' text-red'">
		<text :class="(value > index ? 'cuIcon-likefill' : 'cuIcon-like') + ' margin-right'" v-for="(item, index) in 5" :key="index"
		 @tap="redeHander(index)"></text>
	</view>
</template>

<script>
	export default {
		data() {
			return {};
		},
		watch: {
			value(val){}
		},
		components: {},
		props: {
			value: {
				type: Number,
				default: 0
			},
			size: {
				type: String,
				default: 'xxl'
			}
		},
		methods: {
			redeHander(index) {
				let value = index + 1;
				this.$emit('onChange', value);
			}

		}
	};
</script>
<style>
</style>
