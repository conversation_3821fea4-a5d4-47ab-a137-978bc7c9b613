// 公共样式
/* ==================
        外边距
 ==================== */
.margin-top-lg{
	margin-top:30px;
}
.margin-top{
	margin-top:20px;
}
.margin-top-sm{
	margin-top:10px;
}
.margin-bottom-lg{
	margin-bottom:30px;
}
.margin-bottom{
	margin-bottom:20px;
}
.margin-bottom-sm{
	margin-bottom:10px;
}
.margin-left-lg{
	margin-left:30px;
}
.margin-left{
	margin-left:20px;
}
.margin-left-sm{
	margin-left:10px;
}
.margin-right-lg{
	margin-right:30px;
}
.margin-right{
	margin-right:20px;
}
.margin-right-sm{
	margin-right:10px;
}
/* ==================
          布局
 ==================== */
.flex{
	display: flex;
	flex-wrap: wrap;
}
.flex-column{
	flex-direction: column;
}
.flex-align-s{
	align-items: flex-start;
}
.flex-align-s{
	align-items: center;
}
.flex-justify-s{
	justify-content: flex-start;
}
.flex-justify-c{
	justify-content: center;
}
.flex-justify-bet{
	justify-content: space-between;
}
.flex-sub {
	flex: 1;
}




/* ==================
    通用块级元素样式
 ==================== */
.ske-cell{
	background-color: #f2f3f5;
	height:100px;
	width:100%;
}
.ske-circle{
	width:60px;
	height:60px;
	border-radius: 50%;
}
.ske-block{
	background-color: #f2f3f5;
	height:50px;
}

.ske-animation {
	background-color: #f2f3f5;
	animation-name: skeleton-ani;
	animation-timing-function: inherit;
	animation-duration: 1.8s;
	animation-iteration-count: infinite;
}

@keyframes skeleton-ani {
	0% {
		background-color: #f2f3f5;
	}

	50% {
		opacity: 0.6;
	}

	100% {
		background-color: #f2f3f5;
	}
}

// 自定义模板样式部分
.ske-goods-left-item{
	width:60px;
	height:40px;
	&:last-child{
		margin:0;
	}
}
.ske-goods-img{
	width:100px;
	height:100px;
}
.ske-goods-cell{
	height:20px;
	width:100%;
	&:nth-child(2){
		width:60%;
	}
	&:nth-child(3){
		width:30%;
	}
}