<template>
  <view class="index-skeleton" v-if="loading">
    <skeleton type="block" customStyle="height:100px;"></skeleton>
    <skeleton type="circle" :number="5" :row="2" size="60"></skeleton>
    <skeleton type="goods" :row="3" customStyle="height:200px;width:100%;"></skeleton>
  </view>
</template>

<script>

//骨架屏
import skeleton from '@/components/base-skeleton/index.vue'
export default {
  props:{
    loading:{
      type:Boolean,
      default:true
    }
  },
  components: {
    skeleton
  }
}
</script>
<style scoped>
.index-skeleton {
  background-color: #ffffff;
  position: fixed;
  left: 0;
  right: 0;
  z-index: 99999;
}
</style>
