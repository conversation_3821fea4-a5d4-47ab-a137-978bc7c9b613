<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view :class="'flex text-center ' + customClass">
		<view class="flex-sub">
			<button class="cu-btn cuIcon-move sm st-num-bt" :disabled="stNum <= min" @tap="stNumMinus"></button>
		</view>
		<view class="flex-sub">
			<input type="number" class="st-num text-center bg-gray radius" :value="stNum" :disabled="true" @input="numChange"></input>
		</view>
		<view class="flex-sub">
			<button class="cu-btn cuIcon-add sm st-num-bt" :disabled="max >= 0 ? stNum >= max : false" @tap="stNumAdd"></button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {};
		},
		components: {},
		watch: {
			stNum() {},
		},
		props: {
			max: {
				type: Number,
				default: 0
			},
			min: {
				type: Number,
				default: 0
			},
			stNum: {
				type: Number,
				default: 0
			},
			customClass: {
				type: String,
				default: ''
			}
		},
		methods: {
			stNumMinus() {
				this.$emit('numChange', this.stNum - 1);
			},

			stNumAdd() {
				this.$emit('numChange', this.stNum + 1);
			},

			numChange(e) {
				this.$emit('numChange', e.detail);
			}

		}
	};
</script>
<style>
	.st-num {
		margin-left: 5rpx;
		margin-right: 5rpx;
		width: 50rpx;
		height: 50rpx;
		font-size: 30rpx !important;
	}

	.st-num-bt {
		line-height: 50rpx;
		overflow: hidden;
		width: 50rpx !important;
		font-size: 30rpx !important;
		height: 50rpx !important
	}
</style>
