<template>
  <view>
    <view
      class=""
      style="height: 400rpx; overflow-y: auto">
      <view
        class="cu-list grid"
        :class="['col-' + gridCol, gridBorder ? '' : 'no-border']">
        <view
          @tap="onClickItem(item)"
          class=""
          v-for="(item, index) in emojis"
          :key="index"
          style="width: 12.5%">
          <text
            class="text-sl"
            style="font-size: 60rpx"
            >{{ item }}</text
          >
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import emojis from "./emoji.js";
export default {
  data() {
    return {
      emojis: emojis,
      gridCol: 5,
      gridBorder: false,
    };
  },
  methods: {
    onClickItem(item) {
      this.$emit("onClickItem", item);
    },
  },
};
</script>

<style></style>
