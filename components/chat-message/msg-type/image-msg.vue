<!--
  - Copyright (C) 2024
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<!-- 图片消息类型 -->
<template>
	<view>
		<image mode="aspectFit" style="width: 280rpx; height: 280rpx" :src="msgBody.sourcePicture.url" @tap="imViewImage"></image>
	</view>
</template>

<script>
export default {
	props: {
		msgBody: {
			//消息体
			type: Object,
			default: {}
		}
	},
	data() {
		return {};
	},
	methods: {
		imViewImage() {
			uni.previewImage({
				urls: [this.msgBody.sourcePicture.url],
				current: this.msgBody.sourcePicture.url
			});
		}
	}
};
</script>

<style></style>
