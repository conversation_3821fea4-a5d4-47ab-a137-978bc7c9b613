<!--
  - Copyright (C) 2024
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<!-- 视频消息类型 -->
<template>
  <view>
    <view @click="showModal">
      <view
        style="z-index: -1"
        class="flex align-center"
        ><view style="width: 180rpx; height: 180rpx; background-color: #333333" />
        <view
          class="text-xxl"
          style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%)">
          <view class="cuIcon-video text-bold text-white"></view>
        </view>
        <view
          class="flex justify-between"
          style="position: absolute; bottom: 10px; width: 100%">
          <view class="text-white text-xs margin-left-xs">{{ formatFileSize(msgBody.videoSize) }}</view>
          <!-- <view
            v-if="msgBody && msgBody.duration > 0"
            class="text-white text-xs margin-right-xl"
            >{{ formatDurationTime(msgBody.duration) }}</view
          > -->
        </view>
      </view>
    </view>
    <!-- 弹框播放 -->
    <view
      v-if="modal == 'show'"
      :class="'cu-modal  ' + modal"
      @tap="hideModal">
      <view
        class="cu-dialog bg-white"
        style="width: 100%"
        @tap.stop>
        <view>
          <video-player
            ref="videoDialogRef"
            :videoId="msgBody.videoUUID + 'dialog'"
            :src="msgBody.videoUrl || msgBody.snapshotPath"
            :isPlay="modal == 'show'"
            :autoplay="false" />
        </view>
      </view>
      <view
        @tap="hideModal"
        style="position: absolute; bottom: 30px; left: 50%; transform: translate(-50%, -50%); font-size: 30px">
        <text class="cuIcon-close text-white text-xl"></text>
      </view>
    </view>
  </view>
</template>

<script>
import VideoPlayer from "@/public/video-player/video-player.vue";
export default {
  components: {
    VideoPlayer,
  },
  props: {
    msgBody: {
      //消息体
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      modal: "",
      content: "",
    };
  },
  methods: {
    showModal() {
      this.modal = "show";
    },
    hideModal() {
      this.modal = "";
    },
    // 格式化文件大小
    formatFileSize(bytes) {
      let units = ["k", "KB", "MB", "GB", "TB"];
      while (bytes >= 1024 && units.length > 1) {
        bytes /= 1024;
        units.shift();
      }
      return Number(bytes.toFixed(2)) + units[0];
    },
    // 格式化时间
    formatDurationTime(second) {
      second = Math.round(second);
      var hh = parseInt(second / 3600);
      var mm = parseInt((second - hh * 3600) / 60);
      if (mm < 10) mm = "0" + mm;
      var ss = parseInt((second - hh * 3600) % 60);
      if (ss < 10) ss = "0" + ss;
      if (hh < 10) hh = hh == 0 ? "" : `0${hh}:`;
      var length = hh + mm + ":" + ss;
      if (second > 0) {
        return length;
      } else {
        return "00:00";
      }
    },
  },
};
</script>

<style></style>
