<!--
	  - Copyright (C) 2018-2019
	  - All rights reserved, Designed By www.joolun.com
	  - 注意：
	  - 本软件为www.joolun.com开发研制，未经购买不得使用
	  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
	-->
<template>
	<view class="text-sm" style="display: initial;" :style="'color:'+textColor">
		<text v-if="day">{{day}}天</text>
		<text v-if="hour">{{hour}}小时</text>
		<text v-if="minute">{{minute}}分</text>
		<text v-if="second||second==0">{{second}}秒</text>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				day: null,
				hour: null,
				minute: null,
				second: null,
				timer: null,
				totalTime: 0,
			};
		},
		components: {},
		watch:{
			outTime(val,oldVal){
				if(val!=oldVal){
				}
			}
		},
		props: {
			// 这里定义了innerText属性，属性值可以在组件使用时指定，毫秒
			outTime: {
				type: Number,
				default: 0
			},
			textColor: {
				type: String,
				default: 'red'
			}
		},
		mounted: function() {
			this.CaculateDate();
		},
		destroyed: function() {
			if (this.timer) {
				clearInterval(this.timer);
			}
		},
		methods: {
			CaculateDate: function() {
				var that = this;
				if (this.totalTime == 0) {
					this.totalTime = this.outTime;
				}
				this.timer = setInterval(function() {
					var leftTime = that.totalTime - 1000;
					var days = parseInt(leftTime / 1000 / 60 / 60 / 24, 10);
					var hours = parseInt(leftTime / 1000 / 60 / 60 % 24, 10);
					var minutes = parseInt(leftTime / 1000 / 60 % 60, 10);
					var seconds = parseInt(leftTime / 1000 % 60, 10);
					if (leftTime > 0) {
						that.totalTime = leftTime;
						that.day = days > 0 ? that.timeFormat(days) : null;
						that.hour = hours > 0 ? that.timeFormat(hours) : null;
						that.minute = minutes > 0 ? that.timeFormat(minutes) : null;
						that.second = seconds > 0 ? that.timeFormat(seconds) : 0;
					} else {
						//结束
						clearInterval(that.timer);
						setTimeout(function() {
							that.$emit('countDownDone', null);
						}, 500);
					}
				}, 1000);
			},

			timeFormat(param) {
				//小于10的格式化函数
				return param < 10 ? '0' + param : param;
			}

		}
	};
</script>
<style>
</style>
