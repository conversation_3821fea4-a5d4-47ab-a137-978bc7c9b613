<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
  <view>
    <view class="flex electronic-coupons align-center" v-if="couponUserInfo.type == '1'">
      <view class="padding-lr padding-tb-sm justify-between" style="width: 70%;">
        <view class="flex">
          <view class="flex-sub flex">
            <text class="text-sl text-bold overflow-1 number">{{couponUserInfo.reduceAmount}}</text>
            <text class="text-sm margin-top-lg margin-left-xs">元</text>
          </view>
          <view class="margin-top-sm">
            <view class="text-xs overflow-1">订单满{{couponUserInfo.premiseAmount}}元可使用</view>
            <view class="text-xs">{{couponUserInfo.suitType == '1' ? '全部商品可用' : couponUserInfo.suitType == '2' ? '指定商品可用' : couponUserInfo.suitType == '3' ? '指定商品不可用' : ''}}</view>
          </view>
        </view>
        <view   v-if="theme.showType!=2" class="flex margin-top-sm align-center justify-center">
          <image :src="couponUserInfo.shopInfo.imgUrl" class="round head-image"></image>
          <view class="overflow text-sm text-center">{{couponUserInfo.shopInfo.name}}</view>
        </view>
        <view  v-else class="flex margin-top-sm align-center justify-center">
          <view class="head-image" ></view>
        </view>
      </view>
      <view class="flex-sub padding-sm text-center">
        <!-- <view class="ticket margin-top-sm">代金券</view> -->
        <navigator :class="'bg-'+theme.backgroundColor" hover-class="none" :url="'/pages/goods/goods-list/index?couponUserId=' + couponUserInfo.id" class="cu-btn round text-bold sm margin-top-sm"
                   v-if="toUse && couponUserInfo.status == '0'">前去使用</navigator>
        <view class="padding-xs round" v-if="couponUserInfo.status == '1'">已使用</view>
        <view class="padding-xs round" v-if="couponUserInfo.status == '2'">已过期</view>
        <view class="validity margin-top-xs text-center">
          <view class="text-xs">有效期至{{couponUserInfo.validEndTime}}</view>
        </view>
      </view>
    </view>
    <view class="flex electronic-coupons-2 align-center" v-if="couponUserInfo.type == '2'">
      <view class="padding-lr padding-tb-sm justify-between" style="width: 476rpx;">
        <view class="flex">
          <view class="flex-sub flex">
            <text class="text-sl text-bold overflow-1 number">{{couponUserInfo.discount}}</text>
            <text class="text-sm margin-top-lg margin-left-xs">折</text>
          </view>
          <view class="margin-top-sm">
            <view class="text-xs overflow-1">订单满{{couponUserInfo.premiseAmount}}元可使用</view>
            <view class="text-xs">{{couponUserInfo.suitType == '1' ? '全部商品可用' : couponUserInfo.suitType == '2' ? '指定商品可用' : couponUserInfo.suitType == '3' ? '指定商品不可用' : ''}}</view>
          </view>
        </view>
        <view  v-if="theme.showType!=2" class="flex margin-top-sm align-center justify-center">
          <image :src="couponUserInfo.shopInfo.imgUrl" class="round head-image"></image>
          <view class="overflow text-sm text-center">{{couponUserInfo.shopInfo.name}}</view>
        </view>
        <view  v-else class="flex margin-top-sm align-center justify-center">
          <view class="head-image" ></view>
        </view>
      </view>
      <view class="flex-sub padding-sm text-center">
        <!-- <view class="ticket margin-top-sm">折扣券</view> -->
        <navigator :class="'bg-'+theme.backgroundColor" hover-class="none" :url="'/pages/goods/goods-list/index?couponUserId=' + couponUserInfo.id" class="cu-btn round text-bold sm margin-top-sm"
                   v-if="toUse && couponUserInfo.status == '0'">前去使用</navigator>
        <view class="padding-xs text-bold round" v-if="couponUserInfo.status == '1'">已使用</view>
        <view class="padding-xs text-bold round" v-if="couponUserInfo.status == '2'">已过期</view>
        <view class="validity margin-top-xs text-center">
          <view class="text-xs">有效期至{{couponUserInfo.validEndTime}}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
const app = getApp();

export default {
  data() {
    return {
      CustomBar: this.CustomBar,
      theme: app.globalData.theme, //全局颜色变量
    };
  },

  components: {},
  props: {
    couponUserInfo: {
      type: Object,
      default: () => ({})
    },
    toUse: {
      type: Boolean,
      default: true
    }
  },
  methods: {}
};
</script>
<style>
.electronic-coupons {
  background-image: url('data:image/png;base64,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');
  background-size: 100% 178rpx;
  background-repeat: no-repeat;
  color: #f92713;
}

.electronic-coupons-2 {
  background-image: url('data:image/png;base64,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');
  background-size: 100% 178rpx;
  background-repeat: no-repeat;
  color: #f92713;
}

.head-image {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}

.number{
  font-size: 68rpx;
}

.btn-get{
  padding: 8rpx 3rpx;
  width: 80%;
  margin: auto;
}
</style>
