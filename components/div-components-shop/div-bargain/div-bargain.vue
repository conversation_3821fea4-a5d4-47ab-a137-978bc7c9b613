<template>
	<!-- 砍价 -->
	<view>
		<view :style="{marginBottom: `${newData.pageSpacing}px`}" class="bargain-bg padding-lr-xs">
			<!-- <image v-if="newData.backgroundImg" :src="newData.backgroundImg" class="bargain-image"></image> -->
			<view class="wrapper-list-goods radius bg-white padding-sm">
				<view class="flex justify-between">
					<view class="goods-selection text-df flex align-center">
						<image style="width: 164rpx; height: 28rpx;"
							:src="newData.titleImage?newData.titleImage:'/static/public/img/bargain-title.png'"></image>
						<view class="margin-left-sm text-sm text-black">{{newData.subtitle}}</view>
					</view>
					<navigator :url="'/pageA/bargain/bargain-list/index?shopId=' + shopId"  class="round text-sm bargain-more" :class="'bg-'+theme.backgroundColor">更多</navigator>
				</view>
				<view class="bargain-list">
					<scroll-view v-if="newData.goodsList&&newData.goodsList.length > 0" class="scroll-view_x goods-detail margin-top-xs" scroll-x>
						<block v-for="(item, index) in newData.goodsList" :key="index">
							<navigator hover-class="none" :url="'/pageA/bargain/bargain-detail/index?id=' + item.id" class="item flex goods-box radius">
								<view class="img-box padding-sm">
									<image :src="item.picUrl ? item.picUrl : '/static/public/img/no_pic.png'"></image>
								</view>
								<view class="text-cut goods-name text-sm text-center">{{item.name}}</view>
								<view class="cu-btn round sm flex margin-left margin-right margin-top-sm" :class="'bg-'+theme.backgroundColor"><text class="text-price text-df">{{item.bargainPrice}}</text></view>
								<view v-if="item.goodsSku" class="text-price text-gray text-center margin-top-xs">{{item.goodsSku?item.goodsSku.salesPrice:''}}</view>
							</navigator>
						</block>
					</scroll-view>
					<view v-else class="goods-detail text-center text-gray">
						<view class="text-sm padding margin-top-lg ">暂无商品信息</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	import api from '@/utils/api'

	export default {
		components: {},
		props: {
			value: {
				type: Object,
				default: function() {
					return {
						background: ``,
						themeColor: ``,
					}
				}
			},
            shopId: {
                type: String
            }
		},
		mounted() {},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				newData: this.value,
				couponInfoList: [],
			};
		},
		methods: {
			jumpPage(page) {
				if (page) {
					uni.navigateTo({
						url: page
					});
				}
			}
		}
	}
</script>

<style scoped lang="scss">
	.bargain-bg {
		margin: auto;
	}

	.bargain-more {
		padding: 2px 10px;
	}

	.goods-detail {
		width: auto;
		overflow: hidden;
	}

	.goods-box{
		border: none;
	}

	.wrapper-list-goods {
		white-space: nowrap;
	}

	.wrapper-list-goods .item {
		display: inline-block;
		width: 220rpx;
		margin: 10rpx;
		background-color: #fff;
	}
	
	.wrapper-list-goods .item .img-box {
		width: 220rpx;
		height: 220rpx;
		margin: auto;
	}
	
	.wrapper-list-goods .item .img-box image {
		width: 100%;
		height: 100%;
		border-radius: 5rpx;
	}
</style>
