<template>
	<!-- 组件中应用的常用组件  -->
	<!-- 跳转页面组件 -->
	<view @click="goPage()"  style="height: 100%;">
		<slot></slot>
	</view>
</template>

<script>
	const app = getApp();
	
	import { pageUrls } from '../div-base/div-page-urls.js'

    export default {
	    props: {
            pageUrl: {
                type: String,
	            default: ''
            }
	    },
	    components: {},
	    watch: {
			pageUrl(){
			}
		},
		data() {
            return {
				pageUrls : pageUrls
			};
		},
		methods: {
			goPage() {
				if (this.pageUrl) {
					if(this.pageUrls.tabPages.indexOf(this.pageUrl)!=-1){
						uni.switchTab({
							url: this.pageUrl
						});
					}else{
						uni.navigateTo({
							url: this.pageUrl
						});
					}
				}
			}
		}
    }
</script>

<style scoped lang="scss">
	
</style>
