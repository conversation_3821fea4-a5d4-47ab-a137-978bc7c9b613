<template>
	<!-- 商品显示组件-横向排列方式显示 -->
	<view class="padding-lr-xs" :style="{marginBottom: `${newData.pageSpacing}px`}">
		<view class="wrapper-list-goods padding-lr-sm padding-tb-sm"
			:style="{'background-image': newData.backgroundImg?`url(${newData.backgroundImg})`:'' }">
			<view class="flex align-center justify-between">
				<view class="text-lg text-bold" :style="{color: `${newData.titleColor}`}">{{newData.title}} </view>
				<view @click="onMoreShopGoods" class="flex text-sm" :style="{color: `${newData.titleColor}`}">查看更多 ></view>
			</view>
			<skeleton v-if="loading" type="block" customStyle="height:240rpx;" style="background: #00000000;">
			</skeleton>
			<scroll-view class="scroll-view_x" scroll-x style="width:auto;overflow:hidden;">
				<block v-for="(item, index) in goodsList" :key="index">
					<navigator hover-class="none" :url="'/pages/goods/goods-detail/index?id=' + item.id"
						class="item flex goods-box radius">
						<view class="img-box">
							<image :src="item.picUrls[0] ? item.picUrls[0] : '/static/public/img/no_pic.png'"></image>
						</view>
						<view class="text-cut margin-top-xs text-df text-center padding-lr-xs">{{item.name}}</view>
						<!-- <view class="margin-top-xs text-sm text-gray padding-left-sm overflow-2">{{item.sellPoint}}</view> -->
						<view class="text-price text-scarlet text-center margin-top-xs text-lg text-bold">{{item.priceDown}}
						</view>
					</navigator>
				</block>
			</scroll-view>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	import api from '@/utils/api'
	import skeleton from '@/components/base-skeleton/index.vue' //骨架屏
	export default {
		components: {
			skeleton
		},
		props: {
			value: {
				type: Object,
				default: function() {
					return {
						title: '商品甄选',
						titleColor: 'red',
						titleIcon: 'cuIcon-message',
						pageSpacing: 0,
						goodsList: []
					}
				}
			}
		},
		mounted() {
			this.getGoodsByIds()
		},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				newData: this.value,
				goodsList: [],
				loading: true,
			};
		},
		methods: {
			onMoreShopGoods() {
				this.$emit('onMoreShopGoods');
			},
			jumpPage(page) {
				if (page) {
					uni.navigateTo({
						url: page
					});
				}
			},
			getGoodsByIds() {
				if (this.value.goodsIds) {
					this.loading = true
					api.goodsSpuListByIds(this.value.goodsIds).then(res => {
						// 对接口返回的商品顺序进行排序
						let goods = new Array(this.value.goodsIds.length)
						res.data.map(item => {
							let index = this.value.goodsIds.indexOf(item.id);
							goods[index] = item
						})
						// 如果是下架或删除的商品则去掉
						let goodsNew = []
						goods.map((item, index)=>{
							if(item && !(item.shelf=='0' || item.enable == '0')){
								goodsNew.push(item)
							}
						})
						this.goodsList = goodsNew
						this.loading = false
					});
				} else {
					this.goodsList = this.value.goodsList
					this.loading = false
				}
			}
		}
	}
</script>

<style scoped lang="scss">
	.wrapper-list-goods {
		white-space: nowrap;
		margin-top: 20rpx;
		background-image: url(https://minio.joolun.com/joolun/1/material/c8bf6793-9177-49d3-bcc7-f6dfc4fed265.png);
		background-size: 100% 100%;
		background-repeat: no-repeat;
	}

	.wrapper-list-goods .item {
		display: inline-block;
		width: 180rpx;
		height: 260rpx;
		margin-top: 20rpx;
		margin-left: 10rpx;
		margin-right: 10rpx;
		margin-bottom: 20rpx;
		background-color: #fff;
	}

	.wrapper-list-goods .item .img-box {
		width: 100%;
		height: 160rpx;
	}

	.wrapper-list-goods .item .img-box image {
		width: 100%;
		height: 100%;
		border-radius: 5rpx 5rpx 0 0;
	}

	.goods-box {
		overflow: hidden;
	}
</style>
