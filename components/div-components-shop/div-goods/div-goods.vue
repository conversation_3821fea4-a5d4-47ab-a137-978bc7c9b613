<template>
	<!-- 商品显示 组件 -->
	<view class="margin-lr-xs" :style="{marginBottom: `${newData.pageSpacing}px`}">
		<view v-show="newData.showTitle!='gone'" class="cu-bar justify-center">
			<view class="action text-bold text-sm" :style="{color: `${newData.titleColor}`}">
				<text class="cuIcon-move"></text>
				<text class="text-sm" :class="newData.titleIcon"></text>{{newData.title}}<text class="cuIcon-move"></text>
			</view>
		</view>
		<view class="bg-white radius" v-if="newData.showType=='row'">
			<goods-row :goodsList="goodsList"></goods-row>
		</view>
		<view v-else-if="newData.showType=='card'">
			<goods-card :goodsList="goodsList"></goods-card>
		</view>
		<skeleton v-if="loading" type="goods" :row="3" customStyle="height:200px;width:180px;"></skeleton>
	</view>
</template>

<script>
	const app = getApp();
	import api from '@/utils/api'
	import skeleton from '@/components/base-skeleton/index.vue' //骨架屏
	import goodsCard from "components/goods-card/index";
	import goodsRow from "components/goods-row/index";

	export default {
		components: {
			goodsCard,
			goodsRow,
			skeleton
		},
		props: {
			value: {
				type: Object,
				default: function() {
					return {
						showType: 'row',
						pageSpacing: 0,
						goodsList: []
					}
				}
			}
		},
		mounted() {
			this.getGoodsByIds()
		},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				newData: this.value,
				goodsList: [],
				loading: true,
			};
		},
		methods: {
			jumpPage(page) {
				if (page) {
					uni.navigateTo({
						url: page
					});
				}
			},
			getGoodsByIds() {
				if (this.value.goodsIds) {
					this.loading = true
					api.goodsSpuListByIds(this.value.goodsIds).then(res => {
						// 对接口返回的商品顺序进行排序
						let goods = new Array(this.value.goodsIds.length)
						res.data.map(item => {
							let index = this.value.goodsIds.indexOf(item.id);
							goods[index] = item
						})
						// 如果是下架或删除的商品则去掉
						let goodsNew = []
						goods.map((item, index)=>{
							if(item && !(item.shelf=='0' || item.enable == '0')){
								goodsNew.push(item)
							}
						})
						this.goodsList = goodsNew
						this.loading = false
					});
				} else {
					this.goodsList = this.value.goodsList
					this.loading = false
				}
			}
		}
	}
</script>

<style scoped lang="scss">


</style>
