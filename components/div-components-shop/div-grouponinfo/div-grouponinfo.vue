<template>
	<!-- 拼团 -->
	<view>
		<view :style="{marginBottom: `${newData.pageSpacing}px`}" class="groupon-bg padding-lr-xs">
			<!-- <image v-if="newData.backgroundImg" :src="newData.backgroundImg" class="groupon-image"></image> -->
			<view class="wrapper-list-goods radius bg-white padding-sm">
				<view class="flex justify-between">
					<view class="goods-selection text-df flex align-center">
						<image style="width: 164rpx; height: 28rpx;"
						:src="newData.titleImage?newData.titleImage:'/static/public/img/groupon-title.png'"></image>
						<view class="margin-left-sm text-sm text-black">{{newData.subtitle}}</view>
					</view>
					<navigator :url="'/pageA/groupon/groupon-list/index?shopId='+shopId" class="round text-sm groupon-more" :class="'bg-'+theme.backgroundColor">更多</navigator>
				</view>
				<view class="groupon-list">
					<scroll-view v-if="newData.goodsList&&newData.goodsList.length > 0" class="scroll-view_x goods-detail margin-top-xs" scroll-x>
						<block v-for="(item, index) in newData.goodsList" :key="index">
							<navigator hover-class="none" :url="'/pageA/groupon/groupon-detail/index?id=' + item.id" class="item flex goods-box radius">
								<view class="img-box padding-sm">
									<image class="bg-white" :src="item.picUrl ? item.picUrl : '/static/public/img/no_pic.png'"></image>
								</view>
								<view class="text-cut text-sm text-black text-center padding-lr-sm">{{item.name}}</view>
								<view class="padding-left-sm margin-top-xs">
									<text class="cu-tag radius margin-right-xs sm" :class="'bg-'+theme.backgroundColor">{{item.grouponNum?item.grouponNum:1}}人团</text>
									<text class="text-xs text-black">已有{{item.launchNum?item.launchNum:0}}人参与</text>
								</view>
								<view class="text-price text-scarlet text-lg text-bold text-center margin-top-xs">{{item.grouponPrice}}</view>
							</navigator>
						</block>
					</scroll-view>
					<view v-else class="goods-detail text-center text-gray">
						<view class="text-sm padding margin-top-lg">暂无商品信息</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	import api from '@/utils/api'

	export default {
		components: {},
		props: {
			value: {
				type: Object,
				default: function() {
					return {
						background: ``,
						themeColor: ``,
					}
				}
			},
            shopId: {
                type: String
            }
		},
		mounted() {},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				newData: this.value,
				couponInfoList: [],
			};
		},
		methods: {
			jumpPage(page) {
				if (page) {
					uni.navigateTo({
						url: page
					});
				}
			}
		}
	}
</script>

<style scoped lang="scss">
	.groupon-bg {
	}

	.groupon-more {
		padding: 2px 10px;
	}

	.goods-detail {
		width: auto;
		overflow: hidden;
	}

	.wrapper-list-goods {
		white-space: nowrap;
	}

	.wrapper-list-goods .item {
		display: inline-block;
		width: 220rpx;
		margin: 10rpx;
		background-color: #fff;
	}
	
	.wrapper-list-goods .item .img-box {
		width: 220rpx;
		height: 220rpx;
		margin: auto;
	}
	
	.wrapper-list-goods .item .img-box image {
		width: 100%;
		height: 100%;
		border-radius: 5rpx;
	}
</style>
