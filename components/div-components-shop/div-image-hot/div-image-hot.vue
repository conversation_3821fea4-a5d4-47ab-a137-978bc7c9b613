<template>
	<!-- 热图显示组件 -->
	<view class="padding-lr-xs" :style="{ marginBottom: `${newData.pageSpacing}px` }">
		<div-base-navigator :pageUrl="newData.pageUrl" style="background: #ffffff" :style="{ height: `${newData.height}px` }">
			<view class="cp-image-hot">
				<image
					:src="newData.imageUrl ? newData.imageUrl : ''"
					style="width: 100%"
					:style="[
						{
							height: `${newData.height}px`,
							borderTopLeftRadius: `${newData.topLeftRadius}px`,
							borderTopRightRadius: `${newData.topRightRadius}px`,
							borderBottomLeftRadius: `${newData.bottomLeftRadius}px`,
							borderBottomRightRadius: `${newData.bottomRightRadius}px`
						}
					]"
				></image>
				<navigator
					:url="item.pageUrl"
					class="image-hot-container"
					v-for="(item, index) in newData.hotspotsPosition"
					:key="index"
					ref="dragBoxRef"
					:style="{ left: item.boxTLPoint.x + 'px', top: item.boxTLPoint.y + 'px', height: item.boxHeight + 'px', width: item.boxWidth + 'px' }"
				/>
			</view>
		</div-base-navigator>
	</view>
</template>

<script>
const app = getApp();
import divBaseNavigator from '../div-base/div-base-navigator.vue';
export default {
	name: 'basic-image',
	props: {
		value: {
			type: Object,
			default: function () {
				return {
					pageUrl: ``,
					imageUrl: '',
					height: 100,
					pageSpacing: 0
				};
			}
		}
	},
	components: {
		divBaseNavigator
	},
	data() {
		return {
			theme: app.globalData.theme, //全局颜色变量
			newData: this.value
		};
	},
	methods: {}
};
</script>

<style scoped lang="scss">
.cp-image-hot {
	position: relative;
	z-index: 1;

	.image-hot-container {
		position: absolute;
		z-index: 10;
	}
}
</style>
