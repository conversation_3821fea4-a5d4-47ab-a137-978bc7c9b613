<template>
  <!-- 多图显示组件 -->
  <view>
    <div class="imageMultiComponent">
      <div
        v-show="newData.styleType === '样式一'"
        :style="{
          margin: `${newData.pageSpacingOut}px`,
          padding: `${newData.pageSpacingIn}px`,
          backgroundColor: newData.background,
          borderRadius: `${newData.borderRadius == 1 ? 10 : 0}px`,
        }"
        :class="
          newData.background && newData.background.indexOf('bg-') != -1
            ? newData.background
            : ''
        "
      >
        <div class="flex">
          <!--左边-->
          <div-base-navigator
            :pageUrl="newData.imageItems[0].pageUrl"
            class="flex-sub"
            :style="{
              height: `${
                Number(newData.height) + Number(newData.imageSpacing)
              }px`,
              margin: `${newData.imageSpacing}px`,
              marginRight: `${newData.imageSpacing / 2}px`,
              marginBottom: `0px`,
            }"
          >
            <div
              class="image-multi-left"
              :style="{
                height: `${
                  Number(newData.height) + Number(newData.imageSpacing)
                }px`,
                background: newData.imageItems[0].imageUrl
                  ? `url(${newData.imageItems[0].imageUrl}) round`
                  : '',
              }"
            ></div>
          </div-base-navigator>
          <!--右边-->
          <div
            class="flex-sub"
            :style="{
              margin: `${newData.imageSpacing}px`,
              marginLeft: `${newData.imageSpacing / 2}px`,
            }"
          >
            <div class="flex">
              <div-base-navigator
                :pageUrl="newData.imageItems[1].pageUrl"
                class="image-multi-right"
                :style="{
                  height: `${newData.height / 2}px`,
                  background: newData.imageItems[1].imageUrl
                    ? `url(${newData.imageItems[1].imageUrl}) round`
                    : '',
                }"
              ></div-base-navigator>
            </div>
            <div
              class="flex image-multi-right"
              :style="{ marginTop: `${newData.imageSpacing}px` }"
            >
              <div-base-navigator
                :pageUrl="newData.imageItems[2].pageUrl"
                class="flex-sub"
                :style="{ marginRight: `${newData.imageSpacing / 2}px` }"
              >
                <div
                  class="image-multi-class"
                  :style="{
                    height: `${newData.height / 2}px`,
                    background: newData.imageItems[2].imageUrl
                      ? `url(${newData.imageItems[2].imageUrl}) round`
                      : '',
                  }"
                ></div>
              </div-base-navigator>
              <div-base-navigator
                :pageUrl="newData.imageItems[3].pageUrl"
                class="flex-sub"
                :style="{ marginLeft: `${newData.imageSpacing / 2}px` }"
              >
                <div
                  class="image-multi-class"
                  :style="{
                    height: `${newData.height / 2}px`,
                    background: newData.imageItems[3].imageUrl
                      ? `url(${newData.imageItems[3].imageUrl}) round`
                      : '',
                  }"
                ></div>
              </div-base-navigator>
            </div>
          </div>
        </div>
      </div>

      <div
        v-show="newData.styleType === '样式二'"
        :style="{
          margin: `${newData.pageSpacingOut}px`,
          padding: `${newData.pageSpacingIn}px`,
          backgroundColor: newData.background,
          borderRadius: `${newData.borderRadius == 1 ? 10 : 0}px`,
        }"
        :class="
          newData.background && newData.background.indexOf('bg-') != -1
            ? newData.background
            : ''
        "
      >
        <div class="flex">
          <div-base-navigator
            :pageUrl="newData.imageItems[0].pageUrl"
            class="flex-sub"
            :style="{
              height: `${Number(newData.height) / 2}px`,
              marginTop: `${newData.imageSpacing}px`,
              marginLeft: `${newData.imageSpacing}px`,
              marginRight: `${newData.imageSpacing / 2}px`,
              marginBottom: `${newData.imageSpacing / 2}px`,
            }"
          >
            <div
              class="image-multi-class"
              :style="{
                height: `${newData.height / 2}px`,
                background: newData.imageItems[0].imageUrl
                  ? `url(${newData.imageItems[0].imageUrl}) round`
                  : '',
              }"
            ></div>
          </div-base-navigator>
          <div-base-navigator
            :pageUrl="newData.imageItems[1].pageUrl"
            class="flex-sub"
            :style="{
              height: `${Number(newData.height) / 2}px`,
              marginTop: `${newData.imageSpacing}px`,
              marginRight: `${newData.imageSpacing}px`,
              marginLeft: `${newData.imageSpacing / 2}px`,
              marginBottom: `${newData.imageSpacing / 2}px`,
            }"
          >
            <div
              class="image-multi-class"
              :style="{
                height: `${newData.height / 2}px`,
                background: newData.imageItems[1].imageUrl
                  ? `url(${newData.imageItems[1].imageUrl}) round`
                  : '',
              }"
            ></div>
          </div-base-navigator>
        </div>
        <div class="flex">
          <div-base-navigator
            :pageUrl="newData.imageItems[2].pageUrl"
            class="flex-sub"
            :style="{
              height: `${Number(newData.height) / 2}px`,
              marginTop: `${newData.imageSpacing / 2}px`,
              marginRight: `${newData.imageSpacing / 2}px`,
              marginLeft: `${newData.imageSpacing}px`,
              marginBottom: `${newData.imageSpacing}px`,
            }"
          >
            <div
              class="image-multi-class"
              :style="{
                height: `${newData.height / 2}px`,
                background: newData.imageItems[2].imageUrl
                  ? `url(${newData.imageItems[2].imageUrl}) round`
                  : '',
              }"
            ></div>
          </div-base-navigator>
          <div-base-navigator
            :pageUrl="newData.imageItems[3].pageUrl"
            class="flex-sub"
            :style="{
              height: `${Number(newData.height) / 2}px`,
              marginTop: `${newData.imageSpacing / 2}px`,
              marginRight: `${newData.imageSpacing / 2}px`,
              marginLeft: `${newData.imageSpacing / 2}px`,
              marginBottom: `${newData.imageSpacing}px`,
            }"
          >
            <div
              class="image-multi-class"
              :style="{
                height: `${newData.height / 2}px`,
                background: newData.imageItems[3].imageUrl
                  ? `url(${newData.imageItems[3].imageUrl}) round`
                  : '',
              }"
            ></div>
          </div-base-navigator>
          <div-base-navigator
            :pageUrl="newData.imageItems[4].pageUrl"
            class="flex-sub"
            :style="{
              height: `${Number(newData.height) / 2}px`,
              marginTop: `${newData.imageSpacing / 2}px`,
              marginRight: `${newData.imageSpacing}px`,
              marginLeft: `${newData.imageSpacing / 2}px`,
              marginBottom: `${newData.imageSpacing}px`,
            }"
          >
            <div
              class="image-multi-class"
              :style="{
                height: `${newData.height / 2}px`,
                background: newData.imageItems[4].imageUrl
                  ? `url(${newData.imageItems[4].imageUrl}) round`
                  : '',
              }"
            ></div>
          </div-base-navigator>
        </div>
      </div>

      <div
        v-show="newData.styleType === '样式三'"
        :style="{
          margin: `${newData.pageSpacingOut}px`,
          padding: `${newData.pageSpacingIn}px`,
          backgroundColor: newData.background,
          borderRadius: `${newData.borderRadius == 1 ? 10 : 0}px`,
        }"
        :class="
          newData.background && newData.background.indexOf('bg-') != -1
            ? newData.background
            : ''
        "
      >
        <div class="flex">
          <!--左边-->
          <div-base-navigator
            :pageUrl="newData.imageItems[0].pageUrl"
            class="flex-sub"
            :style="{
              height: `${
                Number(newData.height) + Number(newData.imageSpacing)
              }px`,
              marginTop: `${newData.imageSpacing}px`,
              marginRight: `0px`,
              paddingRight: `${newData.imageSpacing / 2}px`,
              marginLeft: `${newData.imageSpacing}px`,
              marginBottom: `${newData.imageSpacing}px`,
            }"
          >
            <div
              class="image-multi-left"
              :style="{
                height: `${
                  Number(newData.height) + Number(newData.imageSpacing)
                }px`,
                background: newData.imageItems[0].imageUrl
                  ? `url(${newData.imageItems[0].imageUrl}) round`
                  : '',
              }"
            ></div>
          </div-base-navigator>
          <!--右边-->
          <div
            class="flex-sub"
            :style="{
              height: `${newData.height}px`,
              marginTop: `${newData.imageSpacing}px`,
              marginRight: `${newData.imageSpacing}px`,
              marginLeft: `${newData.imageSpacing / 2}px`,
              marginBottom: `${newData.imageSpacing}px`,
            }"
          >
            <div class="flex">
              <div-base-navigator
                :pageUrl="newData.imageItems[1].pageUrl"
                class="image-multi-right"
                :style="{
                  height: `${newData.height / 3}px`,
                  marginBottom: `${newData.imageSpacing / 2}px`,
                  background: newData.imageItems[1].imageUrl
                    ? `url(${newData.imageItems[1].imageUrl}) round`
                    : '',
                }"
              ></div-base-navigator>
            </div>
            <div class="flex">
              <div-base-navigator
                :pageUrl="newData.imageItems[2].pageUrl"
                class="image-multi-right"
                :style="{
                  height: `${newData.height / 3}px`,
                  marginTop: `${newData.imageSpacing / 2}px`,
                  marginBottom: `${newData.imageSpacing / 2}px`,
                  background: newData.imageItems[2].imageUrl
                    ? `url(${newData.imageItems[2].imageUrl}) round`
                    : '',
                }"
              ></div-base-navigator>
            </div>
            <div class="flex">
              <div-base-navigator
                :pageUrl="newData.imageItems[3].pageUrl"
                class="image-multi-right"
                :style="{
                  height: `${newData.height / 3}px`,
                  marginTop: `${newData.imageSpacing / 2}px`,
                  background: newData.imageItems[3].imageUrl
                    ? `url(${newData.imageItems[3].imageUrl}) round`
                    : '',
                }"
              ></div-base-navigator>
            </div>
          </div>
        </div>
      </div>

      <!-- 样式四 -->
      <div
        v-show="newData.styleType === '样式四'"
        :style="{
          margin: `${newData.pageSpacingOut}px`,
          padding: `${newData.pageSpacingIn}px`,
          backgroundColor: newData.background,
          borderRadius: `${newData.borderRadius == 1 ? 6 : 0}px`,
        }"
        :class="
          newData.background && newData.background.indexOf('bg-') != -1
            ? newData.background
            : ''
        "
      >
        <div class="flex">
          <div-base-navigator
            :pageUrl="newData.imageItems[0].pageUrl"
            class="flex-sub"
            :style="{
              height: `${Number(newData.height) / 2}px`,
              marginTop: `${newData.imageSpacing}px`,
              marginLeft: `${newData.imageSpacing}px`,
              marginRight: `${newData.imageSpacing / 2}px`,
              marginBottom: `${newData.imageSpacing / 2}px`,
            }"
          >
            <div
              class="image-multi-class"
              :style="{
                height: `${newData.height / 2}px`,
                background: newData.imageItems[0].imageUrl
                  ? `url(${newData.imageItems[0].imageUrl}) round`
                  : '',
              }"
            ></div>
          </div-base-navigator>
          <div-base-navigator
            :pageUrl="newData.imageItems[1].pageUrl"
            class="flex-sub"
            :style="{
              height: `${Number(newData.height) / 2}px`,
              marginTop: `${newData.imageSpacing}px`,
              marginRight: `${newData.imageSpacing}px`,
              marginLeft: `${newData.imageSpacing / 2}px`,
              marginBottom: `${newData.imageSpacing / 2}px`,
            }"
          >
            <div
              class="image-multi-class"
              :style="{
                height: `${newData.height / 2}px`,
                background: newData.imageItems[1].imageUrl
                  ? `url(${newData.imageItems[1].imageUrl}) round`
                  : '',
              }"
            ></div>
          </div-base-navigator>
        </div>

        <div class="flex">
          <div-base-navigator
            :pageUrl="newData.imageItems[2].pageUrl"
            class="flex-sub"
            :style="{
              height: `${Number(newData.height) / 2}px`,
              marginTop: `${newData.imageSpacing}px`,
              marginLeft: `${newData.imageSpacing}px`,
              marginRight: `${newData.imageSpacing / 2}px`,
              marginBottom: `${newData.imageSpacing / 2}px`,
            }"
          >
            <div
              class="image-multi-class"
              :style="{
                height: `${newData.height / 2}px`,
                background: newData.imageItems[2].imageUrl
                  ? `url(${newData.imageItems[2].imageUrl}) round`
                  : '',
              }"
            ></div>
          </div-base-navigator>
          <div-base-navigator
            :pageUrl="newData.imageItems[3].pageUrl"
            class="flex-sub"
            :style="{
              height: `${Number(newData.height) / 2}px`,
              marginTop: `${newData.imageSpacing}px`,
              marginRight: `${newData.imageSpacing}px`,
              marginLeft: `${newData.imageSpacing / 2}px`,
              marginBottom: `${newData.imageSpacing / 2}px`,
            }"
          >
            <div
              class="image-multi-class"
              :style="{
                height: `${newData.height / 2}px`,
                background: newData.imageItems[3].imageUrl
                  ? `url(${newData.imageItems[3].imageUrl}) round`
                  : '',
              }"
            ></div>
          </div-base-navigator>
        </div>
      </div>
    </div>
  </view>
</template>

<script>
const app = getApp();
import divBaseNavigator from "../div-base/div-base-navigator.vue";
export default {
  name: "div-image-multi",
  props: {
    value: {
      type: Object,
      default: function () {
        return {
          styleType: "样式一",
          height: 275,
          imageSpacing: 0,
          borderRadius: "0",
          pageSpacingIn: 0,
          background: "#FFFFFF",
          pageSpacingOut: 0,
          imageItems: [
            {
              id: Math.random(),
              imageUrl: "",
              pageUrl: "",
            },
            {
              id: Math.random(),
              imageUrl: "",
              pageUrl: "",
            },
            {
              id: Math.random(),
              imageUrl: "",
              pageUrl: "",
            },
            {
              id: Math.random(),
              imageUrl: "",
              pageUrl: "",
            },
            {
              id: Math.random(),
              imageUrl: "",
              pageUrl: "",
            },
          ],
        };
      },
    },
  },
  components: {
    divBaseNavigator,
  },
  data() {
    return {
      theme: app.globalData.theme, //全局颜色变量
      newData: this.value,
    };
  },
  methods: {},
};
</script>

<style lang="scss">
.image-multi-class {
  background: round;
  width: 100%;
}

.image-multi-left {
  background: round;
  width: 80%;
}

.image-multi-right {
  background: round;
  width: 120%;
  margin-left: -70rpx;
}
</style>
