<template>
	<!-- 导航按钮组件 -->
	<!-- <view :class="'bg-'+theme.backgroundColor"> -->
	<view class="padding-lr-xs margin-top-xs">
		<view :style="{marginBottom: `${newData.pageSpacing}px`}">
			<view class="cu-list grid no-border navButton radius" :class="'col-'+newData.navButtons.length">
				<view class="cu-item" v-for="(item,index) in newData.navButtons" :key="index">
					<div-base-navigator :pageUrl="item.pageUrl" hover-class="none">
						<image :src="item.imageUrl" class="nav-bt-img"></image>
						<text class="text-sm" :style="{color: `${newData.textColor}`}">{{item.navName}}</text>
					</div-base-navigator>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import divBaseNavigator from '../div-base/div-base-navigator.vue'
	const app = getApp();
    export default {
	    props: {
            value: {
                type: Object,
	            default: function() {
	                return {
                        rowNum: 4,
					    textColor: '#333333',
					    pageSpacing: 0,
					    navButtons: []
	                }
	            }
            }
	    },
	    components: {
			divBaseNavigator
	    },
		data() {
            return {
				theme: app.globalData.theme, //全局颜色变量
                newData: this.value,
			};
		},
		methods: {
			jumpPage(page) {
				if (page) {
					uni.navigateTo({
						url: page
					});
				}
			}
		}
    }
</script>

<style scoped lang="scss">
	/* 导航 */
	.nav-bt-img{
		width: 120rpx !important;
		height: 120rpx !important;
	}
	
	.cu-list.grid.no-border>.cu-item {
		padding-top: 0upx;
		padding-bottom: 0upx;
	}
	
	.cu-list.grid>.cu-item text {
	    margin-top: 0rpx;
	}
	
	.cu-list.grid.no-border {
	    padding: 5rpx 5rpx;
	}
</style>
