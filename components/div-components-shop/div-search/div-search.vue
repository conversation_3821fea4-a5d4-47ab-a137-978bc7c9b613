<template>
	<!-- 搜索组件 -->
	<view class="cu-bar search  " :style="{ backgroundColor: newData.background}"
		:class="newData.background&&newData.background.indexOf('bg-')!=-1?newData.background:''">
		<view class="search-form round" :style="{backgroundColor: newData.color, borderRadius: `${newData.radius}rpx`,color: `${newData.textColor}`}">
			<text class="cuIcon-search" ></text>
			<view class="response" hover-class="none"  @click="goPage()">
				<view :style="{color: newData.textColor, 'text-align':newData.textPosition == 'center'?'center':'left',marginLeft: newData.textPosition == 'center'?'-25px':'0px'}"  >{{newData.placeholder}}</view>
			</view>
		</view>
		<view class="collection round margin-right-sm text-sm text-center" @click="userCollect"
			  :style="{color: `${newData.collectionTextColor}`}">
			<text :class="'cuIcon-' + (collectId ? 'likefill text-white' : 'like text-white')"
				:style="{color: `${newData.collectionTextColor}`}"></text>{{collectId ? '已收藏' : '收藏'}}
		</view>
	</view>
</template>

<script>
	const app = getApp();
    export default {
        name: 'basic-search',
	    props: {
			shopId: [String],
			collectId: [String],
            value: {
                type: Object,
	            default: function() {
	                return {
                        background: `#efeff4`,
                        color: '#ffffff',
                        placeholder: '请输入关键字',
                        radius: 38,
                        textColor: '#999999',
                        textPosition: `center`,
	                }
	            }
            }
	    },
		watch: {
			shopId(){},
			collectId() {

			}
		},
	    components: {
	    },
		data() {
            return {
				theme: app.globalData.theme, //全局颜色变量
                newData: this.value
			};
		},
		methods: {
        	goPage(){
        		uni.navigateTo({
					url: '/extraJumpPages/base/search/index?shopId='+this.shopId
				})
			},
			userCollect() {
				this.$emit('userCollect')
			}
		}
    }
</script>

<style scoped lang="scss">

</style>
