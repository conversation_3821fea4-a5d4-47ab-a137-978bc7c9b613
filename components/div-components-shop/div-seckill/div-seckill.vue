<template>
	<!-- 秒杀 -->
	<view v-if="listSeckillGoodsInfo&&listSeckillGoodsInfo.length > 0">
		<view :style="{marginBottom: `${newData.pageSpacing}px`}" class="seckill-bg padding-lr-xs">
			<!-- <image v-if="newData.backgroundImg" :src="newData.backgroundImg" class="seckill-image"></image> -->
			<view class="wrapper-list-goods radius bg-white padding-sm">
				<view class="flex">
					<view class="flex-treble text-df">
						<view class="flex align-center">
							<image style="width: 164rpx; height: 28rpx;" 
							:src="newData.titleImage?newData.titleImage:'/static/public/img/seckill-title.png'"></image>
							<view class="seckill-time flex margin-left-sm align-center">
								<view class="text-xs text-white" style="margin-left: 12rpx;">{{curHour}}点</view>
								<view class="text-xs text-red margin-left-sm padding-right padding-left-sm">
									<count-down v-if="outTime>0" :outTime="outTime" :textColor="'red'" @countDownDone="countDownDone"></count-down>
									<text v-else>00:00</text>
								</view>
							</view>
						</view>
						<!-- <view class="margin-left-xs text-bold" :style="{color: `${newData.titleColor}`}">{{newData.title}}</view> -->
						<view class="text-sm text-red margin-top-xs">{{newData.subtitle}}</view>
					</view>
					<view class="flex-sub flex justify-end align-center">
						<navigator :url="'/pageA/seckill/seckill-list/index?shopId='+ shopId" class="round text-sm seckill-more" :class="'bg-'+theme.backgroundColor">更多</navigator>
					</view>
				</view>
				<view class="seckill-list">
					<scroll-view v-if="listSeckillGoodsInfo&&listSeckillGoodsInfo.length > 0" class="scroll-view_x goods-detail" scroll-x>
						<block v-for="(item, index) in listSeckillGoodsInfo" :key="index">
							<navigator hover-class="none" :url="'/pageA/seckill/seckill-detail/index?seckillHallInfoId=' + item.seckillHallInfo.id"
							 class="item flex goods-box radius">
								<view class="img-box padding-sm">
									<image class="bg-white" :src="item.picUrl ? item.picUrl : '/static/public/img/no_pic.png'"></image>
								</view>
								<view>
									<!-- <view class="text-cut text-sm margin-top-xs">{{item.name}}</view> -->
									<!-- <view class="sm margin-top-xs cu-tag line-white radius">限量 {{item.limitNum}}</view> -->
									<view class="text-center text-scarlet text-lg text-bold"><text class="text-price">{{item.seckillPrice}}</text></view>
									<view class="text-gray text-center margin-top-xs text-sm text-decorat"><text class="text-price">{{item.goodsSku.salesPrice}}</text></view>
									<view class="flex justify-center margin-top-xs">
										<view class="cu-progress round xs bg-progress">
											<view :class="'bg-'+theme.backgroundColor" :style="[{ width:item.progress+'%'}]"></view>
										</view>
									</view>
									<view class="text-center margin-top-xs"><text class="text-gray text-sm">已售{{item.seckillNum}}</text></view>
								</view>
							</navigator>
						</block>
					</scroll-view>
					<view v-else class="goods-detail text-center" :style="{color: `${newData.titleColor}`}">
						<view class="text-sm padding margin-top-lg">暂无商品信息</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	import api from '@/utils/api'
	const util = require("utils/util.js");
	import countDown from "@/components/count-down/index";

	export default {
		components: {
			countDown
		},
		props: {
			value: {
				type: Object,
				default: function() {
					return {
						background: ``,
						themeColor: ``,
					}
				}
			},
            shopId: {
                type: String
            },
		},
		mounted() {
			this.parameter.shopId = this.shopId;
			this.getData();
		},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				newData: this.value,
				curSeckillHall: {}, //当前会场
				hasSeckill: false,
				listSeckillGoodsInfo: [], //当前时间段 秒杀商品
				parameter: {},
				page: {
					searchCount: false,
					current: 1,
					size: 10,
					ascs: 'sort',
					descs: '' //升序字段
				},
				curHour: this.$moment().format("H"),
				outTime: -1,
			};
		},
		methods: {
			countDownDone() {
				this.getData();
			},
			setCountDown(){
				// 设置倒计时时间，
				// 如果当前时间大于会场时间，结束
				// 如果当前时间等于，正在进行中
				// 如果小于暂未开始
				if(this.curSeckillHall.hallTime<this.curHour){
					this.outTime = 0;
				}else if(this.curSeckillHall.hallTime==this.curHour){//计算倒计时多少秒
					let curDateTime = new Date().getTime();//当前时间
					let nextHour = Number(this.curHour) + 1;
					let nextDateTime = this.curSeckillHall.hallDate + ' ' + nextHour + ':00:00';
					let timeTemp = this.$moment(nextDateTime).toDate();
					this.outTime = new Date(timeTemp).getTime() - curDateTime;//下一个整点时间
				}else{
					this.outTime=-1;
				}
			},
			getData() {
				let curDate = this.$moment().format("YYYY-MM-DD");
				let that = this;

				let curHour = this.$moment().format("H");
				api.seckillhallList(curDate).then(res => {
					let seckillList = res.data;
					if (seckillList && seckillList.length > 0) {
						this.hasSeckill = true;
						let hasSeckill = false;
						seckillList.forEach((item, index) => {
							if (item.hallTime == curHour) { //默认设置当前小时的秒杀时间，如果没有就设置最近时间的秒杀时间
								this.curSeckillHall = item;
								hasSeckill = true;
								this.getSeckillGoodsData(item.id);
								return;
							} else if (!hasSeckill && item.hallTime > curHour) { //秒杀时间必须大于当前时间
								this.curSeckillHall = item;
								hasSeckill = true;
								this.getSeckillGoodsData(item.id);
								return;
							}
						})
						if (!hasSeckill) {
							this.curSeckillHall = seckillList[0];
							this.getSeckillGoodsData(this.curSeckillHall.id);
						}
					} else {
						this.hasSeckill = false;
					}
				});
			},
			getSeckillGoodsData(id) {
				this.page.size = this.newData.goodsQty;
				api.seckillinfoPage(Object.assign({
					seckillHallId: id
				}, this.page, util.filterForm(this.parameter))).then(res => {
					let listSeckillGoodsInfo = res.data.records;
					listSeckillGoodsInfo.forEach((item, index) => {
						item.progress = (item.seckillNum / item.limitNum).toFixed(2) * 100
					})
					this.listSeckillGoodsInfo = [...this.listSeckillGoodsInfo, ...listSeckillGoodsInfo];
				});
			},
			jumpPage(page) {
				if (page) {
					uni.navigateTo({
						url: page
					});
				}
			}
		}
	}
</script>

<style scoped lang="scss">
	.seckill-bg {
		margin: auto;
	}
	
	.seckill-more {
		padding: 2px 10px;
	}
	
	.seckill-time {
		background-image: url('../../../static/public/img/seckill-time.png');
		background-size: 288rpx 28rpx;
		background-repeat: round; 
	}
	
	.goods-detail {
		width: auto;
		overflow: hidden;
	}
	
	.wrapper-list-goods {
		white-space: nowrap;
	}
	
	.wrapper-list-goods .item {
		display: inline-block;
		width: 220rpx;
		margin: 10rpx;
		background-color: #fff;
	}
	
	.wrapper-list-goods .item .img-box {
		width: 220rpx;
		height: 220rpx;
		margin: auto;
	}
	
	.wrapper-list-goods .item .img-box image {
		width: 100%;
		height: 100%;
		border-radius: 5rpx;
	}
	
	.bg-progress{
		width: 70%;
		background: rgba($color: #ff553f, $alpha: .2);
	}
</style>
