<template>
	<!-- 轮播图组件 -->
	<!-- <view :class="'bg-'+theme.backgroundColor"> -->
	<view class="bg-white">
        <view v-show="newData.swiperType=='card-swiper'" 
			:style="{height: `${(newData.height/2)+14}px`}"></view>
		<view :style="{
				padding: `${newData.swiperType=='card-swiper'?'0 14px 14px 14px':''}`, 
				marginTop: `${newData.swiperType=='card-swiper'? '-'+(newData.height/2)+'px':''}`}">
			<swiper class="screen-swiper " 
					:class="newData.dotStyle=='square-dot' ? 'square-dot' : 'round-dot'"
					:indicator-dots="true" :circular="true" :autoplay="true" :interval="newData.interval"
					duration="500" @change="cardSwiper" indicator-color="#cccccc" indicator-active-color="#ffffff" 
					:style="{height: `${newData.height}px`}" >
				<swiper-item v-for="(item,index) in newData.swiperList" :key="index" :class="cardCur==index?'cur':''" @tap="jumpPage(item.pageUrl)"
					:style="{height: `${newData.height}px`}">
					<image 
						:src="item.imageUrl" 
						:style="{height: `${newData.height}px`, borderRadius: `${newData.borderRadius==1?'6':'0'}px`}"></image>
				</swiper-item>
			</swiper>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	import { pageUrls } from '../div-base/div-page-urls.js'
    export default {
	    props: {
            value: {
                type: Object,
	            default: function() {
	                return {
                        swiperType: 'screen-swiper',
						height: 150,
						interval: 3000,
						borderRadius: 0,
						imageSpacing: 0,
						swiperList: []
	                }
	            }
            }
	    },
	    components: {
	    },
		data() {
            return {
				theme: app.globalData.theme, //全局颜色变量
                newData: this.value,
				cardCur: 0,
				pageUrls: pageUrls
			};
		},
		methods: {
			cardSwiper(e) {
				this.cardCur = e.detail.current
			},
			jumpPage(page) {
				if (page) {
					if(this.pageUrls.tabPages.indexOf(page)!=-1){
						uni.switchTab({
							url: page
						});
					}else{
						uni.navigateTo({
							url: page
						});
					}
				}
			}
		}
    }
</script>

<style scoped lang="scss">
	.screen-swiper {
		min-height: 90upx!important;
	}
</style>
