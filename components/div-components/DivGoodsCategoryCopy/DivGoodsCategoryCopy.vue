<template>
    <view class="DivGoodsCategoryCopy w100">
        <view class="goods-category-type1 w100 df flr jc-fs alc bc1">
            <scroll-view class="category-box w100 f1" scroll-with-animation :scroll-x="true">
                <view class="category-list w100 df flr jc-fs alc">
                    <block v-for="(item,index) in firstCategoryData" :key="index">
                        <view class="category-item df flc jc-fs alc fs0" @click="tabSelect" :data-index="index">
                            <view class="item-text dfc" :class="index === TabCur?'item-texts':''">
                                <text>{{ item.name }}</text>
                            </view>
                            <view class="item-line bc2" v-show="index === TabCur"></view>
                        </view>
                    </block>
                </view>
            </scroll-view>
            <view class="category-icon dfc" @click="showClassInfo">
                <image src="@/static/public/icon/xialabiaoshi.png"></image>
            </view>
            <view class="info-show-con w100 df flc jc-fs alc" :style="{ top: `calc(${CustomBar}px + 110rpx)` }" v-if="pageShow">
                <view class="top-box w100 df flc jc-fs alc bc1">
                    <view class="info-top-con w100 df flr jc-sb alc">
                        <view class="top-text dfc">
                            <text>所有分类</text>
                        </view>
                        <view class="top-icon dfc" @click="retractInfo">
                            <image src="@/static/public/icon/xiangshang.png"></image>
                        </view>
                    </view>
                    <view class="info-list-box w100">
                        <block v-for="(item,index) in firstCategoryData" :key="index">
                            <view class="info-item dfc fs0" @click="tabSelect" :data-index="index">
                                <text class="single-line-hide">{{ item.name }}</text>
                            </view>
                        </block>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script name="DivGoodsCategoryCopy">
const app = getApp();
import api from '@/utils/api'

export default {
    props: {
        value: {
            type: Object,
            default: function () {
                return {
                    background: ``
                }
            }
        }
    },
    data() {
        return {
            CustomBar: this.CustomBar,
            theme: app.globalData.theme, //全局颜色变量
            newData: this.value,
            secondCategoryData: [],
            TabCur: 0,
            scrollLeft: 0,
            firstCategoryData: [{
                id: '-1',
                name: '全部分类'
            }],
            page2: {
                searchCount: false,
                current: 1,
                size: 10
            },
            loadmore2: true,
            goodsList2: [],
            pageShow: false
        }
    },
    mounted() {
        if (this.$homeDivPageLoad)
            this.getData();
    },
    methods: {
        // 收起
        retractInfo() {
            this.pageShow = false
        },
        // 展示分类信息
        showClassInfo() {
            this.pageShow = true
        },
        //商品分类
        getData() {
            api.goodsCategoryTree().then(res => {
                this.firstCategoryData = [...this.firstCategoryData, ...res.data];
            });
        },
        tabSelect(e) {
            this.pageShow = false
            let TabCur = e.currentTarget.dataset.index
            uni.setStorage({
                key: 'param-goods-category-index',
                data: TabCur - 1
            });
            if (TabCur > 0) {
                uni.switchTab({
                    url: '/pages/goods/goods-category/index',
                })
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.DivGoodsCategoryCopy {
    .goods-category-type1 {
        .category-box {
            .category-list {
                padding: 15rpx 0 0 31rpx;
                .category-item {
                    margin-right: 36rpx;
                    .item-text {
                        margin-bottom: 18rpx;
                        text {
                            font-size: 30rpx;
                            font-weight: 500;
                            color: #2A2A2A;
                        }
                    }
                    .item-texts {
                        text {
                            color: #FF6203;
                            font-size: 32rpx;
                            font-weight: bold;
                        }
                    }
                    .item-line {
                        width: 50rpx;
                        height: 6rpx;
                        border-radius: 3rpx;
                    }
                }
            }
        }
        .category-icon {
            image {
                width: 100rpx;
                height: 70rpx;
            }
        }
        .info-show-con {
            position: fixed;
            left: 0;
            height: 100vh;
            background-color: rgba(0, 0, 0, .5);
            z-index: 10000;
            .top-box {
                padding-bottom: 30rpx;
                .info-top-con {
                    padding: 20rpx 30rpx;
                    .top-text {
                        text {
                            color: #2A2A2A;
                            font-size: 32rpx;
                            font-weight: bold;
                        }
                    }
                    .top-icon {
                        image {
                            width: 30rpx;
                            height: 16rpx;
                        }
                    }
                }
                .info-list-box {
                    margin-top: 6rpx;
                    padding: 0 30rpx;
                    display: grid;
                    grid-template-columns: repeat(4, 1fr);
                    grid-gap: 15rpx;
                    .info-item {
                        padding: 22rpx 24rpx;
                        border-radius: 20rpx;
                        background-color: #EEEEEE;
                        text {
                            font-weight: 500;
                            font-size: 28rpx;
                            color: #2A2A2A;
                        }
                    }
                }
            }
        }
    }
}
</style>