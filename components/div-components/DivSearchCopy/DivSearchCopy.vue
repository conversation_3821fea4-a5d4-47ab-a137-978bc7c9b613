<template>
    <view class="DivSearchCopy w100">
        <view class="search-type1 w100">
            <view class="search-box w100 df flr jc-fs alc">
                <view class="search-icon dfc">
                    <text class="cuIcon-search " :class="'text-'+theme.themeColor"></text>
                </view>
                <view class="enter-con dfc">
                    <input type="text" :style="{color: curData.textColor, 'text-align':curData.textPosition === 'center'?'center':'left',marginLeft: curData.textPosition === 'center'?'-25px':'0px'}" :placeholder="curData.placeholder"/>
                </view>
            </view>
        </view>
    </view>
</template>

<script name="DivSearchCopy">
const app = getApp();
export default {
    props: {
        value: {
            type: Object,
            default: function () {
                return {
                    background: `#efeff4`,
                    color: '#ffffff',
                    placeholder: '请输入关键字',
                    radius: 38,
                    textColor: '#999999',
                    textPosition: `center`,
                }
            }
        }
    },
    data() {
        return {
            theme: app.globalData.theme, //全局颜色变量
            curData: this.value
        }
    },
    onLoad() {
    },
    methods: {}
}
</script>

<style lang="scss" scoped>
.DivSearchCopy {
    .search-type1 {
        padding: 10rpx 25rpx;
        background: linear-gradient(-30deg, #FFD2C4 0%, #FFFFFF 50%, #FFF4DC 100%);
        .search-box {
            padding: 20rpx 30rpx;
            border-radius: 60rpx;
            border: 2rpx solid #FF6203;
            .search-icon {
                margin-right: 20rpx;
            }
        }
        .enter-con {
            input::placeholder {
                color: #848484;
                font-size: 28rpx;
            }
        }
    }
}
</style>