<template>
  <!-- 优惠券 -->
  <view class="padding-lr-xs" :style="{marginBottom: `${newData.pageSpacing}px`}" v-if="couponInfoList.length > 0">
    <view class="flex coupons "
          :class="newData.background && newData.background.indexOf('bg-') != -1 ? newData.background : '' ">
      <scroll-view class="scroll-view_x "  scroll-x style="margin-right: -40rpx;padding-right: 36rpx;">
        <block v-for="(item, index) in couponInfoList" :key="index">
          <view class="item flex">
            <view class="cu-item  coupons-image"
                  :style="{'background-image': newData.couponImage1?'url('+newData.couponImage1+')':'',backgroundColor: newData.themeColor, }"
                  :class="!newData.couponImage1&&newData.themeColor&&newData.themeColor.indexOf('bg-') != -1 ? newData.themeColor : ''"
                  v-if="item.type == '1'">
              <view class="flex text-white electronic-coupons">
                <view class="flex-twice padding-lr-xs" style="color: #f92713; min-width: 210rpx;"
                      :style="{color: newData.textColor1}">
                  <view v-if="theme.showType!=2"
                        class="margin-top-xs text-xs text-center overflow-1 coupons-shop-name">
                    {{item.shopInfo?item.shopInfo.name:''}}
                  </view>
                  <view v-else style="height: 20rpx;"></view>
                  <view class="text-center">
                    <text class="text-price text-xl"></text>
                    <text class="number text-bold">{{item.reduceAmount}}</text>
                  </view>
                  <view class="text-center">
                    <view class="text-xs">满{{item.premiseAmount}}元可用</view>
                  </view>
                </view>
                <view class="flex-sub text-center t1-l"
                   :style="{'borderLeft':newData.couponImage1?'':'1px dashed rgb(255, 255, 255, 0.3)','color':newData.textColor1}">
                  <!-- <view class="text-xs margin-top-sm">代金券</view> -->
                  <view v-if="!item.couponUser" @tap="couponUserSave(item)" class=" already  ">
                    <text>领取</text>
                  </view>
                  <view v-if="item.couponUser" class="margin-top-sm received" style="">已领取</view>
                </view>
              </view>
            </view>
            <view v-else-if="item.type == '2'&&newData.themeColor2" class="cu-item  coupons-image2"
                  :style="{'background-image': newData.couponImage2?'url('+newData.couponImage2+')':'',backgroundColor: newData.themeColor2, }"
                  :class="!newData.couponImage2&&newData.themeColor2&&newData.themeColor2.indexOf('bg-') != -1 ? newData.themeColor2 : ''">
              <view class="flex text-white electronic-coupons">
                <view class="flex-twice padding-lr-xs" style="color: #f92713;    min-width: 210rpx;"
                      :style="{color: newData.textColor2}">
                  <view v-if="theme.showType!=2"
                        class="margin-top-xs text-xs overflow-1 text-center coupons-shop-name">
                    {{item.shopInfo?item.shopInfo.name:''}}
                  </view>
                  <view v-else style="height: 20rpx;"></view>
                  <view class="text-center">
                    <text class="number text-bold">{{item.discount}}折</text>
                  </view>
                  <view class="text-center">
                    <view class="text-xs">满{{item.premiseAmount}}元可用</view>
                  </view>
                </view>
                <view class="flex-sub  text-center t1-l"
                   :style="{'borderLeft':newData.couponImage2?'':'1px dashed rgb(255, 255, 255, 0.3)','color':newData.textColor2}">
                  <!-- <view class="text-xs margin-top-sm">折扣券</view> -->
                  <view v-if="!item.couponUser" @tap="couponUserSave(item)"
                        class="margin-top-sm already">领取</view>
                  <view v-if="item.couponUser" class="margin-top-sm received">已领取</view>
                </view>
              </view>
            </view>
          </view>
        </block>
      </scroll-view>
      <view class="get-more-p">
        <navigator class="get-more" url="/extraJumpPages/coupon/coupon-list/index">领取更多</navigator>
      </view>
    </view>
  </view>
</template>

<script>
const app = getApp();
import api from '@/utils/api'

export default {
  components: {},
  props: {
    value: {
      type: Object,
      default: function() {
        return {
          background: ``,
          themeColor: ``,
          loadNumber: 3
        }
      }
    },
  },
  mounted() {
    if (this.$homeDivPageLoad)
      this.getData();
  },
  data() {
    return {
      theme: app.globalData.theme, //全局颜色变量
      newData: this.value,
      couponInfoList: [],
      pageSpacing: 0,
    };
  },
  methods: {
    // 获取数据
    getData() {
      let data = {
        searchCount: false,
        current: 1,
        size: this.newData.loadNumber ? this.newData.loadNumber : 3,
        ascs: 'coupon_user_id',
        //升序字段
        descs: ''
      };
      api.couponInfoPage(data).then(res => {
        this.couponInfoList = res.data.records;
      });
    },
    couponUserSave(item) {
      api.couponUserSave({
        couponId: item.id
      }).then(res => {
        uni.showToast({
          title: '领取成功',
          icon: 'success',
          duration: 2000
        });
        this.getData();
      });
    }
  }
}
</script>

<style scoped lang="scss">
.t1-l {
  background-size: 100% 60%;
  background-repeat: no-repeat;
}

.coupons {
  white-space: nowrap;
  overflow-x: scroll;
  height: 130rpx;
  width: 100%;
}

.coupons .item {
  display: inline-block;
  width: 254rpx;
  margin-right: 8upx;
  height: auto;
}

.coupons-image {
  background-size: 254rpx 128rpx;
  border-radius: 6rpx;
}

.coupons-image2 {
  background-size: 254rpx 128rpx;
  border-radius: 6rpx;
}

.coupons-shop-name {
  width: 190rpx;
}

.electronic-coupons {
  height: 128rpx;
}

.number {
  font-size: 18px;
}

.already {
  writing-mode: vertical-lr;
  font-size: 10px;
  text-align: center;
  padding: 0px 2px;
  height: 100%;
  letter-spacing: 15rpx;
  margin-top: 2px;
}

.received {
  writing-mode: vertical-lr;
  font-size: 10px;
  text-align: center;
  padding: 0px 2rpx;
  letter-spacing: 5rpx;
  opacity: 0.7;
}

.get-more-p {
  padding: 1rpx 0;
  background: #FFFFFF;
  z-index: 1;
  width: 70rpx;
  padding-left: 4rpx;
}

.get-more {
  writing-mode: vertical-rl;
  border: red solid 1px;
  text-align: center;
  color: red;
  border-radius: 4rpx;
  font-size: 10px;
  height: 125rpx;
  margin-right: 4rpx;
}
</style>
