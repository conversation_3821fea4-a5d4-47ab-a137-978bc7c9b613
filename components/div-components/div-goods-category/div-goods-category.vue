<template>
    <!-- 商品分类组件 -->
    <view class="cu-bar goods-category" :style="{backgroundColor: newData.background}"
          :class="newData.background&&newData.background.indexOf('bg-')!=-1?newData.background:''">
        <view class="margin-left-xs" style="width: 90%;">
            <scroll-view scroll-x class="nav text-white text-df" scroll-with-animation :scroll-left="scrollLeft">
                <view class="cu-item" :class="index==TabCur ? 'cur text-bold text-white text-lg' : ''"
                      v-for="(item,index) in firstCategoryData"
                      :key="index" @tap="tabSelect" :data-index="index">
                    {{ item.name }}
                </view>
            </scroll-view>
        </view>
        <view class="action">
            <navigator url="/pages/goods/goods-category/index" open-type="switchTab" hover-class="none"
                       class="cuIcon-moreandroid text-white"></navigator>
        </view>
    </view>
</template>

<script>

const app = getApp();
import api from '@/utils/api'

export default {
    components: {},
    props: {
        value: {
            type: Object,
            default: function () {
                return {
                    background: ``,
                }
            }
        }
    },
    mounted() {
        if (this.$homeDivPageLoad)
            this.getData();
    },
    data() {
        return {
            theme: app.globalData.theme, //全局颜色变量
            newData: this.value,
            secondCategoryData: [],
            TabCur: 0,
            scrollLeft: 0,
            firstCategoryData: [{
                id: '-1',
                name: '首页'
            }],
            page2: {
                searchCount: false,
                current: 1,
                size: 10
            },
            loadmore2: true,
            goodsList2: []
        };
    },
    methods: {
        //商品分类
        getData() {
            api.goodsCategoryTree().then(res => {
                this.firstCategoryData = [...this.firstCategoryData, ...res.data];
            });
        },
        tabSelect(e) {
            let TabCur = e.currentTarget.dataset.index
            uni.setStorage({
                key: 'param-goods-category-index',
                data: TabCur - 1
            });
            if (TabCur > 0) {
                uni.switchTab({
                    url: '/pages/goods/goods-category/index',
                })
            }
        },
    }
}
</script>

<style scoped lang="scss">
.goods-category {
    min-height: 70rpx;
}

.nav .cu-item {
    height: 70rpx;
    display: inline-block;
    line-height: 70rpx;
    margin: 0 8rpx;
    padding: 0 8rpx;
}

.nav .cu-item.cur {
    border-bottom: 6rpx solid;
}
</style>
