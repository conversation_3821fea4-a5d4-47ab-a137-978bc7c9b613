<!--
  - Copyright (C) 2018-2022
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<div class="goodsNewComponent" :style="{marginBottom: `${newData.pageSpacing}px`}">
		<div class="flex">
			<div class="flex-sub bg-white padding-xs radius margin-right-xs goods-new-bg"
				:style="{backgroundColor: newData.goodsItem1.titleBg}">
				<div class="flex">
					<span class="text-bold text-xl" :style="{color: `${newData.goodsItem1.titleColor}`,
                  fontSize: `${newData.goodsItem1.titleSize}px`,}">{{newData.goodsItem1.title}}</span>
					<div class="margin-left-xs text-sm round" style="padding: 2px 6px;" :style="{
                  color: `${newData.goodsItem1.subtitleColor}`,
                  fontSize: `${newData.goodsItem1.subtitleSize}px`,
                  backgroundColor: newData.goodsItem1.subtitleBg,}">{{newData.goodsItem1.subtitle}}</div>
				</div>
				<div class="flex margin-top-xs">
					<div-base-navigator :pageUrl="newData.goodsItem1.goodsList[0].pageUrl" class="flex-sub text-center">
						<img :src="newData.goodsItem1.goodsList[0].imageUrl ? newData.goodsItem1.goodsList[0].imageUrl : '/static/public/img/no_pic.png'"
							class="goods-new-img">
						<div class="text-black text-sm">
							<span class="text-xl overflow-1"
								:style="{color: `${newData.goodsItem1.goodsList[0].color}`,fontSize: `${newData.goodsItem1.goodsList[0].size}px`}">{{newData.goodsItem1.goodsList[0].name}}</span>
						</div>
					</div-base-navigator>
					<div-base-navigator :pageUrl="newData.goodsItem1.goodsList[1].pageUrl" class="flex-sub text-center">
						<img :src="newData.goodsItem1.goodsList[1].imageUrl ? newData.goodsItem1.goodsList[1].imageUrl : '/static/public/img/no_pic.png'"
							class="goods-new-img">
						<div class="text-black text-sm">
							<span class="text-xl overflow-1"
								:style="{color: `${newData.goodsItem1.goodsList[1].color}`,fontSize: `${newData.goodsItem1.goodsList[1].size}px`}">{{newData.goodsItem1.goodsList[1].name}}</span>
						</div>
					</div-base-navigator>
				</div>
			</div>
			<view class="box-line" :style="{ width: `${newData.goodsItem1.pageSpacing2?newData.goodsItem1.pageSpacing2:0}px` }"></view>
			<div class="flex-sub bg-white padding-xs radius goods-new-bg"  :style="{backgroundColor: newData.goodsItem2.titleBg}">
				<div class="flex">
					<span class="text-bold text-xl" :style="{color: `${newData.goodsItem2.titleColor}`,
                  fontSize: `${newData.goodsItem2.titleSize}px`,}">{{newData.goodsItem2.title}}</span>
					<div class="margin-left-xs text-sm round" style="padding: 2px 6px;" :style="{
                  color: `${newData.goodsItem2.subtitleColor}`,
                  fontSize: `${newData.goodsItem2.subtitleSize}px`,
                  backgroundColor: newData.goodsItem2.subtitleBg,}">{{newData.goodsItem2.subtitle}}</div>
				</div>
				<div class="flex margin-top-xs">
					<div-base-navigator :pageUrl="newData.goodsItem2.goodsList[0].pageUrl" class="flex-sub text-center">
						<img :src="newData.goodsItem2.goodsList[0].imageUrl ? newData.goodsItem2.goodsList[0].imageUrl : '/static/public/img/no_pic.png'"
							class="goods-new-img ">
						<div class="text-black text-sm">
							<span class="text-xl overflow-1"
								:style="{color: `${newData.goodsItem2.goodsList[0].color}`,fontSize: `${newData.goodsItem2.goodsList[0].size}px`}">{{newData.goodsItem2.goodsList[0].name}}</span>
						</div>
					</div-base-navigator>
					<div-base-navigator :pageUrl="newData.goodsItem2.goodsList[1].pageUrl" class="flex-sub text-center">
						<img :src="newData.goodsItem2.goodsList[1].imageUrl ? newData.goodsItem2.goodsList[1].imageUrl : '/static/public/img/no_pic.png'"
							class="goods-new-img">
						<div class="text-black text-sm">
							<span class="text-xl overflow-1"
								:style="{color: `${newData.goodsItem2.goodsList[1].color}`,fontSize: `${newData.goodsItem2.goodsList[1].size}px`}">{{newData.goodsItem2.goodsList[1].name}}</span>
						</div>
					</div-base-navigator>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
	const app = getApp();
	import divBaseNavigator from '../div-base/div-base-navigator.vue'
	export default {
		name: 'div-goods-new',
		props: {
			value: {
				type: Object,
				default: function() {
					return {
						goodsItem1: {
							title: '',
							titleColor: '#333333',
							titleBg: 'red',
							titleSize: '18',
							subtitleColor: '#ffffff',
							subtitle: '',
							subtitleBg: '#ffb17d',
							subtitleSize: '13',
							goodsList: [{
									id: Math.random(),
									imageUrl: '',
									name: '',
									pageUrl: '',
									color: '#333333',
									size: 13,
								},
								{
									id: Math.random(),
									imageUrl: '',
									name: '￥99',
									pageUrl: '',
									color: '#333333',
									size: 13,
								},
							]
						},
						goodsItem2: {
							title: '',
							titleColor: '#333333',
							titleBg: 'red',
							titleSize: '18',
							subtitleColor: '#ffffff',
							subtitle: '',
							subtitleBg: '#FF7D7D',
							subtitleSize: '13',
							goodsList: [{
									id: Math.random(),
									imageUrl: '',
									name: '',
									pageUrl: '',
									color: '#FF3636',
									size: 13,
								},
								{
									id: Math.random(),
									imageUrl: '',
									name: '',
									pageUrl: '',
									color: '#333333',
									size: 13,
								},
							]
						},
						pageSpacing: 0,
					}
				}
			}
		},
		components: {
			divBaseNavigator
		},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				newData: this.value
			};
		},
		methods: {}
	}
</script>

<style lang='less' scoped>
	.goods-new-bg {
		background-image: linear-gradIEnt(to top, rgba(255, 255, 255, 1), rgba(255, 255, 255, 1), rgba(255, 255, 255, 1), rgba(255, 255, 255, 0.8));
	}

	.goodsNewComponent {
		position: relative;
		display: block;
		width: 100%;
		padding: 10rpx;
		background-color: #FFFFFF;
		border-radius: 20rpx;


		.goods-new-img {
			width: 70px !important;
			height: 70px !important;
			border-radius: 3px;
		}
	}

	.overflow-1 {
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 1;
		-webkit-box-orient: vertical;
	}

	.overflow-2 {
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
	}
</style>
