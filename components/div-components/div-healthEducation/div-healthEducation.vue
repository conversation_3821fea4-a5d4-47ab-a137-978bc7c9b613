<template>
    <view class="education-con-box w100 df flc jc-fs alc bc1">
        <view class="title-box-con w100 df flr jc-sb alc">
            <view class="title-left-con df flr jc-fs alc">
                <view class="left-icon dfc">
                    <image src="@/static/public/icon/jiankangxuanjiao.png" mode="aspectFit"></image>
                </view>
                <view class="left-text dfc">
                    <text class="fwb fc2">健康宣教</text>
                </view>
            </view>
            <view class="title-right-con df flr jc-fe alc" @click="moreClick">
                <view class="right-text dfc">
                    <text class="fc3">更多</text>
                </view>
                <view class="right-icon dfc">
                    <image src="@/static/public/icon/youjiantou.png" mode="aspectFit"></image>
                </view>
            </view>
        </view>
        <view class="info-box-list w100 df flc jc-fs alc">
            <block v-for="(item,index) in healthListData" :key="index">
                <view class="info-box-con w100 f1 df flr jc-sb alc" @click="missionaryClick(item)">
                    <view class="info-pic dfc">
                        <image :src="item['picUrl']" mode="aspectFit"></image>
                    </view>
                    <view class="info-con w100 df flc jc-fs alc">
                        <view class="info-title w100 df flr jc-fs alc">
<!--                            <text class="fc2">{{ item['articleTitle'] }}</text>-->
                            <u-parse :content="item['articleContent']"></u-parse>
                        </view>
                        <view class="info-bot-con w100 df flr jc-sb alc">
                            <view class="see-num df flr jc-fs alc">
                                <!--                                <view class="see-icon dfc">-->
                                <!--                                    <image src="@/static/public/icon/chakan.png" mode="aspectFit"></image>-->
                                <!--                                </view>-->
                                <!--                                <view class="see-text dfc">-->
                                <!--                                    <text class="fc4">1</text>-->
                                <!--                                </view>-->
                            </view>
                            <view class="time-text dfc">
                                <text class="fc4">{{ item['createTime'] }}</text>
                            </view>
                        </view>
                    </view>
                </view>
            </block>
        </view>
    </view>
</template>

<script>
import uParse from '@/components/u-parse/u-parse.vue'
import api from "@/utils/api";
import {go} from "../../../utils/customUtil";

export default {
    components: {
        uParse
    },
    props: {
        value: {
            type: Object,
            default: function () {
                return {}
            }
        }
    },
    data() {
        return {
            healthData: this.value,
            healthListData: []
        }
    },
    mounted() {
        this.getHealthEducation()
    },
    methods: {
        // 更多点击
        moreClick() {
            go(`/pages/article/article-list/index`)
        },
        // 宣教点击
        missionaryClick(data) {
            go(`/pages/article/article-info/index?id=${data.id}`)
        },

        // 获取健康宣教
        async getHealthEducation() {
            let {code, data} = await api.getArticleInfoPage({size: 3, current: 1})
            if (code === 0) {
                this.healthListData = data['records']
            }
        },
    }
}
</script>

<style lang="scss" scoped>
.education-con-box {
    border-radius: 20rpx;
    padding: 28rpx 20rpx;

    .title-box-con {
        padding-bottom: 33rpx;

        .title-left-con {
            .left-icon {
                margin-right: 11rpx;

                image {
                    width: 30rpx;
                    height: 30rpx;
                }
            }

            .left-text {
                text {
                    font-size: 34rpx;
                }
            }
        }

        .title-right-con {
            .right-text {
                text {
                    font-size: 26rpx;
                    font-weight: 400;
                }
            }

            .right-icon {
                margin-left: 12rpx;

                image {
                    width: 10rpx;
                    height: 18rpx;
                }
            }
        }
    }

    .info-box-con {
        margin-top: 33rpx;

        .info-pic {
            margin-right: 20rpx;

            image {
                width: 140rpx;
                height: 140rpx;
            }
        }

        .info-con {
            .info-title {
                font-weight: 500;
                font-size: 30rpx;
                overflow: hidden;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                word-break: break-all;
            }

            .info-bot-con {
                margin-top: 30rpx;

                .see-num {
                    .see-icon {
                        image {
                            width: 30rpx;
                            height: 20rpx;
                        }
                    }

                    .see-text {
                        text {
                            font-size: 28rpx;
                            font-weight: 500;
                        }
                    }
                }

                .time-text {
                    text {
                        font-size: 28rpx;
                        font-weight: 500;
                    }
                }
            }
        }
    }
}
</style>
