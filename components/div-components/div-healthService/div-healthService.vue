<template>
	<view class="health-con-box w100 df flc jc-fs alc bc1">
		<view class="health-title w100 df flr jc-fs alc">
			<text class="fwb fc2">{{ title }}</text>
		</view>
		<uni-swiper-dot class="uni-swiper-dot-box w100" :info="info" :current="current" mode="round" :dots-styles="dotsStyles" field="content">
			<swiper class="swiper-box w100" :class="current === 0?'gaodu1': 'gaodu2'" @change="changeCon">
				<swiper-item class="swiper-item" v-for="(item, index) in info" :key="index">
					<block v-for="(ite,ind) in item['data']" :key="ind">
						<view class="item-con w100" @click="swiperClick(ite)">
							<view class="item-pic dfc">
								<image :src="ite['imageUrl']" mode="aspectFill"></image>
							</view>
							<view class="item-text dfc">
								<text>{{ ite['dataName'] }}</text>
							</view>
						</view>
					</block>
				</swiper-item>
			</swiper>
		</uni-swiper-dot>
	</view>
</template>

<script>
	export default {
	    props: {
            value: {
                type: Object,
	            default: function() {
	                return {}
	            }
            }
	    },
		data() {
			return {
				title: "健康服务",
				current: 0,
				dotsStyles: {
					width: 10,
					backgroundColor: 'rgba(255, 90, 95,0.3)',
					border: '1px rgba(255, 90, 95,0.3) solid',
					color: '#ffffff',
					selectedBackgroundColor: 'rgba(255, 90, 95,0.9)',
					selectedBorder: '1px rgba(255, 90, 95,0.9) solid',
				},
				info: []
			}
		},
		watch: {
			value: {
				immediate: true,
				handler(val) {
					this.title = val['title']?val['title']:'健康服务'
					if(Array.isArray(val['dataCon']) && val['dataCon'].length > 0) {
						let arr1 = []
						let arr2 = []
						let arr3 = []
						val['dataCon'].map((item,index)=>{
							if(index > 3) {
								arr2.push(item)
							} else {
								arr1.push(item)
							}
						})
						if(arr2.length > 8) {
							arr2.map((item,index)=>{
								if(index < 7) {
									arr3.push(item)
								}
							})
							arr3.push({
								dataName: "更多",
								imageUrl: "https://video.xiyuns.cn/1/material/168f3fd4-fa90-4efd-bd11-171418c40fca.png",
								urlText: ""
							})
						} else {
							arr2.map((item,index)=>{
								arr3.push(item)
							})
						}
                        if(arr3.length === 0) {
                            this.info = [{data: arr1}]
                        } else {
                            this.info = [{data: arr1}, {data: arr3}]
                        }
					}
				}
			}
		},
		methods: {
            // 点击
            swiperClick(data) {
                console.log('我的点击：', data)
								if(data['typeStatus'] == 2) {
									if (data['pathText'] == '/pages/goods/goods-category/index') {
										// 跳转tab
										uni.switchTab({
											url: `${data['pathText']}`
										})
									} else {
										if (data['pathText'].includes('/pages/goods/goods-category/index')) { 
											function getQueryParameters(url) {
												const params = {};
												const queryString = url.split('?')[1];

												if (queryString) {
													queryString.split('&').forEach(item => {
														const parts = item.split('=');
														const key = parts[0];
														const value = decodeURIComponent(parts[1] || ''); // 处理没有值的参数，并解码
														params[key] = value;
													});
												}

												return params;
											}
											// 查看是否带有查询字符串
											const url = data['pathText'];
											const queryParams = getQueryParameters(url);

											const category = queryParams.category;
											const categorySon = queryParams.categorySon;
											const categoryName = queryParams.categoryName;
											const categorySonName = queryParams.categorySonName;

											if (category && !categorySon) {
												//分类对应
												uni.switchTab({
													url: `${data['pathText']}`
												})
												uni.setStorageSync('param-goods-category-id', category)
											} else if (category && categorySon) {
												//分类对应
												uni.navigateTo({
													url: `/pages/goods/goods-list/index?categorySecond=${categorySon}&title=${categorySonName}`
												})
											}

											console.log(category, categorySon, categoryName, categorySonName, '查询字符串');
										}
										// 内链
										uni.navigateTo({
											url: `${data['pathText']}`
										})
									}
									return
								}
                uni.navigateToMiniProgram({
                    appId: data['urlText'],
                    path: data['pathText'],
                    envVersion: 'release',
                    complete(res) {
                        // 打开成功
                        console.log('打开信息：', res)
                    }
                })
            },
            // 切换
            changeCon(e) {
				this.current = e.detail.current
			},
		}
	}
</script>

<style lang="scss" scoped>
	.health-con-box {
		padding: 27rpx 25rpx;
		border-radius: 20rpx;

		.health-title {
			margin-bottom: 33rpx;

			text {
				font-size: 32rpx;
			}
		}
		.uni-swiper-dot-box {
			.swiper-box {
				transition: height 0.5s;
				.swiper-item {
					display: grid;
					grid-template-columns: repeat(4, 1fr);
					
					.item-con {
						.item-pic {
							margin-bottom: 17rpx;
							image {
								width: 90rpx;
								height: 90rpx;
								border-radius: 20rpx;
							}
						}
						.item-text {
							text {
								font-size: 26rpx;
								color: #2A2A2A;
								font-weight: 500;
							}
						}
					}
				}
			}
			.gaodu1 {
				height: 180rpx;
			}
			.gaodu2 {
				height: 360rpx;
			}
		}
	}
</style>