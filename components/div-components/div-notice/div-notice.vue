<template>
	<!-- 通知通告 组件 -->
	<view class="padding-lr-xs">
		<view class="adsec light bg-white margin-tb-xs radius" :style="{marginBottom: `${newData.pageSpacing}px`}">
			<swiper class="swiper-container" autoplay="true" circular="true" :interval="newData.interval">
				<swiper-item v-for="(item, index) in newData.noticeList" :key="index" @tap="jumpPage(item.pageUrl)" >
					<view class="text-orange text-df flex align-center" :style="{color: `${newData.textColor}`}">
						<image class="notice-image" v-if="item.imageUrl" :src="item.imageUrl"></image>
						<text class="details margin-left-xs overflow-1">{{item.content}}</text>
						<text v-if="item.pageUrl" class="cuIcon-right"></text>
					</view>
				</swiper-item>
			</swiper>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	import { pageUrls } from '../div-base/div-page-urls.js'
    export default {
	    props: {
            value: {
                type: Object,
	            default: function() {
	                return {
						noticeList: []
	                }
	            }
            }
	    },
	    components: {
		},
		data() {
            return {
				theme: app.globalData.theme, //全局颜色变量
                newData: this.value,
				pageUrls: pageUrls
			};
		},
		methods: {
			jumpPage(page) {
				if (page) {
					if(this.pageUrls.tabPages.indexOf(page)!=-1){
						uni.switchTab({
							url: page
						});
					}else{
						uni.navigateTo({
							url: page
						});
					}
				}
			}
		}
    }
</script>

<style scoped lang="scss">

	/* 公告 */
	.adsec {
		width: 100%;
		display: flex;
		flex-direction: row;
		flex-wrap: nowrap;
		align-items: center;
		padding: 10rpx 10rpx;
		height: 68rpx;
		border-radius: 20rpx;
	}

	.swiper-container {
		height: 68rpx;
		width: 100%;
		line-height: 68rpx;
	}

	.notice-image{
		width: 80rpx;
		height: 48rpx;
	}
</style>
