<template>
	<view class="science-con w100 df flc jc-fs alc bc1">
		<view class="science-title-box w100 df flr jc-fs alc">
			<view class="box-icon dfc">
				<image :src="titleIcon" mode="aspectFit"></image>
			</view>
			<view class="box-title dfc">
				<text class="fc2 fwb">{{ title }}</text>
			</view>
		</view>
		<view class="science-video-list">
			<block v-for="(item,index) in dataList" :key="index">
				<view class="video-item" @click="clickInfo(item)">
					<image :src="item['imageUrl']" mode="aspectFit"></image>
				</view>
			</block>
		</view>
	</view>
</template>

<script>
	export default {
	    props: {
            value: {
                type: Object,
	            default: function() {
	                return {}
	            }
            }
	    },
		data() {
			return {
				title: "",
				titleIcon: "",
				dataList: []
			}
		},
		watch: {
			value: {
				immediate: true,
				handler(val) {
					this.title = val['title']
					this.titleIcon = val['titleIcon']
					this.dataList = val['dataCon']
				}
			}
		},
		methods: {
			clickInfo(val) {
                this.$emit('clickInfo', val)
            }
		}
	}
</script>

<style lang="scss" scoped>
.science-con {
	border-radius: 20rpx;
	padding: 28rpx 20rpx;
	.science-title-box {
		width: 100%;
		margin-bottom: 30rpx;
		.box-icon {
			margin-right: 6rpx;
			image {
				width: 35rpx;
				height: 30rpx;
			}
		}
		.box-title {
			text {
				font-size: 34rpx;
				font-weight: bold;
			}
		}
	}
	.science-video-list {
		width: 100%;
		display: grid;
		grid-gap: 16rpx 20rpx;
		grid-template-columns: repeat(2, 1fr);
		.video-item {
			width: 100%;
			image {
				width: 100%;
				height: 140rpx;
				border-radius: 20rpx;
			}
		}
	}
}
</style>
