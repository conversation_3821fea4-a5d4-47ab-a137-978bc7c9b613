<template>
	<!-- 搜索组件 -->
	<view class="cu-bar search padding-left-sm" :style="{backgroundColor: newData.background}"
		:class="newData.background&&newData.background.indexOf('bg-')!=-1?newData.background:''">
		<view class="search-form round" style="line-height: 60rpx;"
          :style="{backgroundColor: newData.color, borderRadius: `${newData.radius}rpx`,color: `${newData.textColor}`,marginRight: newData.rightImageShow==1?'0px':'',}">
			<text class="cuIcon-search "  :class="'text-'+theme.themeColor"></text>
			<navigator class="response" hover-class="none" url="/extraJumpPages/base/search/index">
				<view :style="{color: newData.textColor, 'text-align':newData.textPosition == 'center'?'center':'left',marginLeft: newData.textPosition == 'center'?'-25px':'0px'}"  >{{newData.placeholder}}</view>
			</navigator>
			<navigator class="round text-center align-center" hover-class="none" url="/extraJumpPages/base/search/index"
			 style="width: 80px; margin-right: 1px;" :style="{backgroundColor: newData.background}"  :class="'bg-'+theme.backgroundColor">
				<text>搜索</text>
			</navigator>
		</view>
    <!--  右侧图片，必须是开启状态且有图片地址才显示  -->
	<div-base-navigator :pageUrl="newData.rightImagePageUrl" hover-class="none">
		<image v-show="newData.rightImageShow==1&&newData.rightImageUrl"
		     :src="newData.rightImageUrl"
		     style="width: 30px;height: 25px;margin-right: 5px;margin-left: 5px;"
		     :style="{width: `${newData.rightImageWidth}px`, height: `${newData.rightImageHeight}px`}"></image>
	</div-base-navigator>
	</view>
</template>

<script>
	import divBaseNavigator from '../div-base/div-base-navigator.vue'
	const app = getApp();
    export default {
        name: 'basic-search',
	    components: {
			divBaseNavigator
	    },
	    props: {
            value: {
                type: Object,
	            default: function() {
	                return {
                        background: `#efeff4`,
                        color: '#ffffff',
                        placeholder: '请输入关键字',
                        radius: 38,
                        textColor: '#999999',
                        textPosition: `center`,
	                }
	            }
            }
	    },
		data() {
            return {
				theme: app.globalData.theme, //全局颜色变量
                newData: this.value
			};
		},
		methods: {
		}
    }
</script>

<style scoped lang="scss">
	.search{
		min-height: 60rpx;
	}

	.cu-bar .search-form {
		margin: 0 10rpx;
	}
</style>
