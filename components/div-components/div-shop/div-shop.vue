<template>
	<!-- 店铺推荐 组件 -->
	<view class="padding-lr-xs">
		<view :style="{marginBottom: `${newData.pageSpacing}px`, borderRadius: '20rpx'}" class="bg-white radius padding-tb-sm padding-lr-xs" >
			<view class="wrapper-list-shop">
				<view class="flex justify-between">
					<view class="shop-selection margin-left-xs flex align-center" :style="{color: `${newData.titleColor}`}">
						<!-- <image v-show="newData.titleImage" class="shop-title-image margin-right-xs" :src="newData.titleImage"></image> -->
						<!--<text class="text-bold" :class="newData.titleIcon"></text>-->
						<view class="text-box dfc" style="padding-right: 10rpx;">
							<text class="fwb" style="font-size: 32rpx;color:#2A2A2A;">{{newData.titleOne}}</text>
						</view>
						<text class=" text-red">{{newData.title}}</text>
					</view>
					<navigator hover-class="none" url="/extraJumpPages/shop/shop-list/index?type=2" class="shop-more text-sm margin-right-xs">更多大牌<text
						 class="cuIcon-right"></text></navigator>
				</view>
				<scroll-view class="scroll-view_x" scroll-x>
					<block v-for="(item, index) in newData.shopInfoData" :key="index">
						<navigator hover-class="none" :url="'/extraJumpPages/shop/shop-detail/index?id=' + item.id"
						 class="item flex shop-box radius justify-center">
							<view class="flex shop-image radius justify-center">
								<image :src="item.imgUrl" class="radius flex justify-center"></image>
							</view>
							<view class="text-center text-sm margin-top-xs text-cut  text-black ">{{item.name}}</view>
						</navigator>
					</block>
				</scroll-view>
			</view>
	</view>
	</view>
</template>

<script>
	const app = getApp();

	export default {
		components: {},
		props: {
			value: {
				type: Object,
				default: function() {
					return {
						pageSpacing: 0,
						shopInfoData: []
					}
				}
			}
		},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				newData: this.value,
			};
		},
		methods: {
			jumpPage(page) {
				if (page) {
					uni.navigateTo({
						url: page
					});
				}
			}
		}
	}
</script>

<style scoped lang="scss">
	.wrapper-list-shop {
		white-space: nowrap;
	}

	.wrapper-list-shop .item {
		display: inline-block;
		background-color: #fff;
	}

	.wrapper-list-shop .item .img-box image {
		width: 100%;
		height: 100%;
		border-radius: 5rpx;
	}

	.shop-box {
		height: 142rpx !important;
		width: 144rpx !important;
	}

	.shop-more {
		margin-right: 0rpx !important;
		color: #666666;
	}

	.shop-image {
		width: 108rpx;
		height: 108rpx !important;
		margin: auto;
	}

	.shop-image image {
		height: 108rpx !important;
	}
	
	.shop-title-image {
		width: 160rpx; 
		height: 30rpx;
	}
</style>
