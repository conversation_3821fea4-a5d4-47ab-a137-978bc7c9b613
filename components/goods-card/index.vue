<!--
  - Copyright (C) 2020-2022
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view class="goods-container flex">
		<view class="goods-box" v-for="(item, index) in goodsList" :key="index">
			<navigator class="goods-item" v-if="item" hover-class="none"
				:url="'/pages/goods/goods-detail/index?id=' + item.id">
				<image class="activeBorder" v-if="item.activeBorder" :src="item.activeBorder.picUrl"></image>
				<view class="goods-absolute">
					<view class="img-box">
						<image :src="item.picUrls[0] ? item.picUrls[0] : '/static/public/img/no_pic.png'"></image>
					</view>
					<view class="text-black text-df margin-top-xs padding-lr-xs overflow-1">
						<view class="cu-tag bg-red light sm radius margin-right-xs saleType" v-if="item.shopInfo.saleType == 2">
							自营 </view>{{item.name}}
					</view>
					<view class="margin-top-xs text-sm text-gray padding-left-xs overflow-1">{{item.sellPoint}}</view>
					<view class="flex margin-top-xs align-center padding-lr-xs">
						<view class="cu-tag bg-scarlet radius sm margin-right-xs"
							v-if="item.freightTemplat&&item.freightTemplat.type == '2'">包邮</view>
						<view class="cu-tag line-orange radius sm margin-right-xs" v-if="item.pointsGiveSwitch == '1'">
							积分</view>
						<view class="text-gray text-sm">已售{{item.saleNum}}</view>
					</view>
					<view class="text-price text-bold text-scarlet text-xl padding-lr-sm margin-top-xs">
						{{item.priceDown}}
					</view>
				</view>
			</navigator>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {};
		},

		components: {},
		props: {
			goodsList: {
				type: Array,
				default: () => []
			}
		},
		methods: {}
	};
</script>
<style>
	.goods-container {
		justify-content: space-between;
		flex-wrap: wrap;
		box-sizing: content-box;
		padding-left: 5rpx;
		padding-right: 5rpx;
		padding-bottom: 110rpx;
	}

	.goods-box {
		width: 341rpx;
		height: 550rpx;
		background-color: #fff;
		overflow: hidden;
		margin: 10rpx;
		border-radius: 10rpx;
	}

	.goods-box .img-box {
		width: 100%;
		height: 348rpx;
		overflow: hidden;
	}

	.goods-box .img-box image {
		width: 100%;
		height: 348rpx;
	}

	.activeBorder {
		width: 100%;
		height: 340rpx;
		border-radius: 10rpx 10rpx 0rpx 0rpx;
		z-index: 1;
	}

	.goods-item {
		position: relative;
	}

	.goods-absolute {
		position: absolute;
		top: 0;
		width: 341rpx;
	}
</style>