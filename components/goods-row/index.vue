<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view class="cu-card article">
		<view class="cu-item goods-item bg-white" v-for="(item, index) in goodsList" :key="index">
			<navigator hover-class="none" :url="'/pages/goods/goods-detail/index?id=' + item.id">
				<view class="content align-center solid-bottom">
					<view class="image-box " style="position: relative;">
						<image class="radius activeBorder" v-if="item.activeBorder" :src="item.activeBorder.picUrl">
						</image>
						<image :src="item.picUrls[0] ? item.picUrls[0] : '/static/public/img/no_pic.png'"
							mode="aspectFill" class="row-img radius"></image>
					</view>
					<view class="margin-left-sm " style="width: 100%;">
						<view class="text-black overflow-2">
							<view class="cu-tag bg-red light sm radius margin-right-xs saleType"
								v-if="item.shopInfo.saleType == 2">
								自营 </view>{{item.name}}
						</view>
						<view class="text-gray text-sm margin-top-xs overflow-2">{{item.sellPoint}}</view>
						<view class="flex margin-top-xs align-center">
							<view class="cu-tag bg-scarlet radius sm"
								v-if="item.freightTemplat&&item.freightTemplat.type == '2'">包邮</view>
							<view class="cu-tag line-orange radius sm margin-right-sm"
								v-if="item.pointsGiveSwitch == '1'">积分</view>
							<view class="text-gray text-sm">已售{{item.saleNum?item.saleNum:'0'}}</view>
						</view>
						<view class="flex justify-between margin-top-sm">
							<view class="text-bold text-xl text-price text-scarlet">
								{{item.priceDown?item.priceDown:'0'}}</view>
							<view class="round buy text-sm bg-scarlet" :class="'bg-'+theme.themeColor"><text>立即购买</text>
							</view>
						</view>
					</view>
				</view>
			</navigator>
		</view>
	</view>
</template>

<script>
	const app = getApp();

	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
			};
		},

		components: {},
		props: {
			goodsList: {
				type: Array,
				default: () => []
			}
		},
		methods: {}
	};
</script>
<style>
	.goods-item {
		margin: auto !important;
		padding: 0;
	}

	.cu-card.article>.cu-item {
		padding-bottom: 0rpx;
	}

	.cu-card.article>.cu-item .content {
		display: flex;
		padding: 20rpx;
	}

	.image-box {
		width: 200rpx !important;
		height: 200rpx !important;
	}

	.row-img {
		width: 200rpx !important;
		height: 200rpx !important;
	}

	.buy {
		padding: 10rpx 20rpx 10rpx 20rpx;
	}

	.activeBorder {
		width: 200rpx;
		height: 200rpx;
		z-index: 1;
		position: absolute;
	}
</style>