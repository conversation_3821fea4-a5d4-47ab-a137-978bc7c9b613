<template>
	<view>
		<view :class="'cu-modal ' + graphCodeModal" @tap="hideModal"
			catchtouchmove="touchMove">
			<view class="cu-dialog bg-white " @tap.stop>
				<view class="cu-bar justify-end">
					<view class="content">图形验证码</view>
					<view class="action" @tap="hideModal">
						<text class="cuIcon-close text-red"></text>
					</view>
				</view>
				<view>
					<view>
						<image v-if="graphCodeUrl" :src="graphCodeUrl" @click="refreshGraphCode" class="margin-bottom" style="height: 100px;"></image>
						<view v-else class="margin-bottom-sm text-center text-red">请检查是否填写手机号码</view>
						<view class="margin-lr padding-sm solid">
							<input placeholder="请输入图形验证码" name="graphCode" v-model="graphCode"></input>
						</view>
					</view>
					<view class="padding-lr padding-bottom">
						<button class="cu-btn margin-top response lg round" :disabled="!graphCodeUrl" :class="'bg-'+theme.themeColor" @click="onSend">发送短信验证码</button>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	// 图形验证码
	const app = getApp();
	import __config from '@/config/env'; // 配置文件
	export default {
		name:"graph-code",
		props:{
			phone: {
				type: String
			}
		},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				graphCodeUrl: '', //图形验证码请求地址
				graphCode: '', // 图形验证码
				graphCodeModal: '' // 用 'show' 或 '' 切换弹框显示和隐藏
			};
		},
		methods: {
			// 隐藏图形验证码弹框
			hideModal() {
				this.graphCodeModal  = ''
			},
			// 显示图形验证码弹框
			showModal() {
				this.refreshGraphCode();
				this.graphCodeModal  = 'show'
			},
			// 生成随机图形验证码
			refreshGraphCode(){
				if(this.phone){
					//实际只要randomStr参数,s参数是保证图片能实时刷新
					//#ifndef H5
					this.graphCodeUrl = `${__config.basePath}/code?randomStr=${this.phone}&s=${new Date().getTime()}`
					//#endif
					//#ifdef H5
					// h5直接用本身的域名地址
					this.graphCodeUrl = `${location.origin}/code?randomStr=${this.phone}&s=${new Date().getTime()}`
					//#endif
					this.graphCode = ''
				}
			},
			onSend(){
				if(!this.graphCode){
					uni.showToast({
						title: '请输入图形验证码',
						icon: 'none'
					})
					return;
				}
				this.$emit('onSend', this.graphCode)
			}
		}
	}
</script>

<style>

</style>
