<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view class="flex justify-end" v-if="orderInfo">
		<navigator class="cu-btn round line-green margin-right" open-type="navigate"
			:url="'/pages/customer-service/customer-service-list/index?shopId='+orderInfo.shopId+ '&orderInfoId='+ orderInfo.id" >
			<view class="cuIcon-servicefill">客服</view>
		</navigator>
		<button class="cu-btn round line-grey margin-right" @tap="orderDel" :loading="loading" :disabled="loading" type v-if="orderInfo.status == '5' && orderInfo.isPay == '0'">删除订单</button>
		<button class="cu-btn round line-grey margin-right" @tap="orderCancel" :loading="loading" :disabled="loading" type
		 v-if="orderInfo.isPay == '0' && !orderInfo.status">取消订单</button>
		<button class="cu-btn round line-grey margin-right" @tap="orderLogistics" :loading="loading" :disabled="loading" type
		 v-if="orderInfo.deliveryWay == '1' && orderInfo.deliveryType == '1' && (orderInfo.status == '2' || orderInfo.status == '3' || orderInfo.status == '4')">查看物流</button>
		 <!-- 同城配送物流 -->
		 <button class="cu-btn round line-grey margin-right" @tap="orderLogisticsFlow" :loading="loading" :disabled="loading" type
		 v-if="orderInfo.deliveryWay == '3' && (orderInfo.status == '2' || orderInfo.status == '3' || orderInfo.status == '4')">查看信息</button>
		<button class="cu-btn round line-red margin-right" @tap="goPay" :loading="loading" :disabled="loading" type
		 v-if="orderInfo.isPay == '0' && !orderInfo.status">立即付款</button>
		<!-- <button class="cu-btn round line-red margin-right" bindtap="urgeOrder" loading="{{loading}}" 
  disabled="{{loading}}" type="" wx:if="{{orderInfo.status == '1'}}">
    提醒卖家发货
  </button> -->
		<button class="cu-btn round line-red margin-right" @tap="orderReceive" :loading="loading" :disabled="loading" type
		 v-if="orderInfo.status == '2'">{{ orderInfo.deliveryWay == 3 ? '确认完成' : '确认收货' }}</button>
		<button class="cu-btn round line-red margin-right" @tap="orderAppraise" :loading="loading" :disabled="loading" type
		 v-if="orderInfo.status == '3' && orderInfo.appraisesStatus == '0'">评价</button>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2018-2019
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	import api from 'utils/api'
	import util from 'utils/util'
	
	export default {
		data() {
			return {
				loading: false
			};
		},

		components: {},
		props: {
			orderInfo: {
				type: Object,
				default: () => ({})
			},
			callPay: {
				type: Boolean,
				default: false
			},
			contact: {
				type: Boolean,
				default: false
			}
		},

		mounted() {
			let that = this;
			setTimeout(function() {
				if (that.callPay && that.orderInfo.paymentPrice > 0) {
					that.goPay();
				}
			}, 1000);
		},

		methods: {
			orderReceive() {
				let that = this;
				uni.showModal({
					content: '是否确认收货吗？',
					cancelText: '我再想想',
					confirmColor: '#ff0000',
				  success(res) {
						if (res.confirm) {
							let id = that.orderInfo.id;
							api.orderReceive(id).then(res => {
								that.$emit('orderReceive', res);
							});
						}
					}
				});
			},

			orderCancel() {
				let that = this;
				uni.showModal({
					content: '确认取消该订单吗？',
					cancelText: '我再想想',
					confirmColor: '#ff0000',
					success(res) {
						if (res.confirm) {
							let id = that.orderInfo.id;
							api.orderCancel(id).then(res => {
								that.$emit('orderCancel', res);
							});
						}
					}
				});
			},

			orderDel() {
				let that = this;
				uni.showModal({
					content: '确认删除该订单吗？',
					cancelText: '我再想想',
					confirmColor: '#ff0000',
					success(res) {
						if (res.confirm) {
							let id = that.orderInfo.id;
							api.orderDel(id).then(res => {
								that.$emit('orderDel', res);
							});
						}
					}

				});
			},
			
			goPay(){
				if(this.orderInfo.paymentPrice > 0){
					uni.navigateTo({
						url: '/pages/pay/index?orderId=' + this.orderInfo.id
					});
				}else{//0元支付
					this.loading = true;
					let orderInfo = this.orderInfo
					api.unifiedOrder({
						id: orderInfo.id,
						// #ifdef MP-WEIXIN
						tradeType: 'JSAPI',
						// #endif
						// #ifdef H5
						tradeType: util.isWeiXinBrowser() ? 'JSAPI' : 'MWEB',
						// #endif
						// #ifdef APP-PLUS
						tradeType: 'APP',
						// #endif
					}).then(res => {
						this.loading = false;
						//0元付款
						//支付成功跳转到支付结果页面
						uni.redirectTo({
							url: '/pages/order/order-pay-result/index?orderId=' + orderInfo.id
						});
					});
				}
			},

			urgeOrder() {
				uni.showToast({
					title: '已提醒卖家发货',
					icon: 'success',
					duration: 2000
				});
			},

			orderLogistics() {
				uni.navigateTo({
					url: '/pages/order/order-logistics/index?id=' + this.orderInfo.orderLogistics.id
				});
			},
			// 同城配送物流
			orderLogisticsFlow () { 
				uni.navigateTo({
					url: '/subPageFlow/pages/orderDetail/index?id=' + this.orderInfo.id + '&isUser=1'
				});
			},

			orderAppraise() {
				uni.navigateTo({
					url: '/pages/appraises/form/index?orderId=' + this.orderInfo.id
				});
			}
		}
	};
</script>
<style>
</style>
