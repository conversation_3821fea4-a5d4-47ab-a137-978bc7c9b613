<template name="protocol-popup">
	<view @touchmove.stop.prevent="clear" v-show="showPopup">
		<view class="popup_mask" @touchmove.stop.prevent="clear"></view>
		<view class="popup_content">
			<view class="title">{{title}}</view>
			<view class="explain_text">
				请你务必认真阅读、充分理解“服务协议”和“隐私政策”各条款，包括但不限于：为了向你提供数据、分享等服务所要获取的权限信息。
				<view class="line">
					你可以阅读
					<!-- #ifdef MP -->
					<text class="link" @tap="openPrivacyContract">{{privacyContractName}}</text>
					<!-- #endif -->
					<!-- #ifdef APP -->
					<navigator :url="'/pages/public/webview/webview?title=服务协议&url='+protocolPath" class="path"
						hover-class="navigator-hover">《服务协议》</navigator>和<navigator
						:url="'/pages/public/webview/webview?title=隐私政策&url='+policyPath" class="path"
						hover-class="navigator-hover">《隐私政策》</navigator>
					<!-- #endif -->
					了解详细信息。如您同意，请点击"同意"开始接受我们的服务。
				</view>
			</view>
			<view class="button">
				<view class="cu-btn bg-white" @click="back">暂不使用</view>
				<button class="cu-btn bg-white" id="agree-btn" @click="confirm" open-type="agreePrivacyAuthorization"
					@agreeprivacyauthorization="handleAgreePrivacyAuthorization">同意</button>
			</view>
		</view>
	</view>
</template>

<script>
	import __config from '@/config/env'; // 配置文件

	export default {
		name: "privacy-policy",
		props: {
			title: {
				type: String,
				default: "服务协议和隐私政策"
			},
			policyStorageKey: {
				type: String,
				default: "has_read_privacy"
			}
		},
		data() {
			return {
				showPrivacyPolicy: __config.showPrivacyPolicy,
				policyPath: __config.privacyPolicyUrl, // 政策路径
				protocolPath: __config.protocolUrl, // 协议路径
				showPopup: false,
				isRead: false,
				privacyContractName: ''
			};
		},
		created() {
			var that = this;
			if (this.showPrivacyPolicy) {
				// #ifdef MP
				// this.showPopup = true
				wx.getPrivacySetting({
					success: res => {
						// 返回结果为: res = { needAuthorization: true/false, privacyContractName: '《xxx隐私保护指引》' }
						this.privacyContractName = res.privacyContractName;
						// console.log(res)
						if (res.needAuthorization) {
							uni.hideTabBar()
							// 需要弹出隐私协议
							this.showPopup = true
						} else {
							// 用户已经同意过隐私协议，所以不需要再弹出隐私协议，也能调用已声明过的隐私接口
							// this.showPopup = false
						}
					}
				})
				// #endif
				// #ifdef APP
				uni.getStorage({
					key: this.policyStorageKey,
					success: (res) => {
						if (res.data) {
							that.showPopup = false;
						}
					},
					fail: function(e) {
						that.showPopup = true;
					}
				});
				// #endif
			}
		},
		methods: {
			// 禁止滚动
			clear() {
				return;
			},
			back() {
				let that = this;
				this.$emit('popupState', false)
				// #ifdef MP-WEIXIN
				that.showPopup = false;
				wx.exitMiniProgram();
				// #endif
				// #ifdef APP-PLUS
				uni.getSystemInfo({
					success: function(e) {
						if (e.platform == 'android') {
							plus.runtime.quit()
						} else if (e.platform == 'ios') {
							that.showPopup = false;
						}
					}
				})
				// #endif
			},
			// 关闭弹框
			confirm() {
				// #ifndef MP
				this.showPopup = false;
				this.$emit('popupState', true);
				uni.setStorage({
					key: this.policyStorageKey,
					data: true
				});
				// #endif
			},
			openPrivacyContract() {
				wx.openPrivacyContract({
					success: () => {
						this.isRead = true;
					},
					fail: () => {
						uni.showToast({
							title: '遇到错误',
							icon: 'error',
						});
					},
				});
			},
			exitMiniProgram() {
				wx.exitMiniProgram();
			},
			handleAgreePrivacyAuthorization() {
				// #ifdef MP
				this.showPopup = false;
				uni.showTabBar()
				if (typeof this.resolvePrivacyAuthorization === 'function') {
					this.resolvePrivacyAuthorization({
						buttonId: 'agree-btn',
						event: 'agree',
					});
				}
				// #endif
			},
		}
	};
</script>

<style lang="scss">
	.popup_mask {
		position: fixed;
		bottom: 0;
		top: 0;
		left: 0;
		right: 0;
		background-color: rgba(0, 0, 0, 0.4);
		transition-property: opacity;
		transition-duration: 0.3s;
		opacity: 0;
		z-index: 99998;

	}

	.popup_mask {
		opacity: 1;
	}

	.popup_content {
		overflow: hidden;
		box-sizing: border-box;
		padding: 40upx 20upx 0 20upx;
		position: fixed;
		bottom: 30%;
		border-radius: 8px;
		left: 50%;
		margin-left: -40%;
		right: 0;
		min-height: 400upx;
		background: #ffffff;
		width: 80%;
		z-index: 99999;

		.title {
			text-align: center;
			font-size: 34upx;
			padding: 10upx 0 0 0;
		}

		.explain_text {
			font-size: 30upx;
			padding: 30upx 30upx 40upx 30upx;
			line-height: 38upx;

			.line {
				display: block;

				.path {
					color: #007aff;
					display: inline-block;
					text-align: center;
				}
			}
		}

		.button {
			display: flex;
			padding: 20upx;
			align-items: center;
			font-size: 34upx;
			justify-content: space-around;
			border-top: 1upx solid #f2f2f2;

			view {
				text-align: center;
			}
		}

		.link {
			color: #07c160;
			text-decoration: underline;
		}
	}
</style>