<!--
  - Copyright (C) 2018-2021
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<!-- 分享组件 (重构版)  -->
<!-- 分享给微信好友 和 分享海报生成 -->
<!-- 可以单独设置分享微信好友和海报生成 showShareFriends 和 showSharePoster -->
<template>
	<view >
		<!-- 显示第一个 -->
		<view :class="'cu-modal bottom-modal ' + (value==true ? 'show' : '')" @tap="shareHide" >
			<view class="cu-dialog" @tap.stop>
				<view class="cu-bar bg-white">
					<view class="action text-green"></view>
					<view class="action text-red" @tap="shareHide">取消</view>
				</view>
				<view class="padding flex flex-direction">
					<!-- #ifdef APP-PLUS || MP-WEIXIN -->
					<share-friends
						v-show="showShareFriends"
						:shareObj="{
								title: shareParams.title,
								desc: shareParams.desc,
								imgUrl: shareParams.imgUrl,
								url: shareParams.url
						}"></share-friends>
					<!-- #endif -->
					<!-- #ifdef H5 -->
					<share-friends
						v-show="showShareFriends && isWeiXinBrowser"
						:shareObj="{
								title: shareParams.title,
								desc: shareParams.desc,
								imgUrl: shareParams.imgUrl,
								url: shareParams.url
						}"></share-friends>
					<!-- #endif -->
					<button v-show="showSharePoster" class="cu-btn margin-tb-sm lg round shadow-blur" :class="'bg-'+theme.themeColor" @tap="onCreatePoster">生成海报</button>
				</view>
			</view>
		</view>

		<view :class="'cu-modal ' + (posterShow ? 'show' : '')">
			<view class="cu-dialog show-bg">
				<view class="bg-white" style="height: 100%">
					<!-- #ifdef H5 -->
					<image :src="posterUrl" mode="widthFix" />
					<!-- #endif -->
					<!-- #ifndef H5 -->
					<image :src="posterUrl" mode="widthFix" ></image>
					<!-- #endif -->
				</view>
				<view class="cu-bar bg-white solid-top show-btn">
					<view class="action margin-0 flex-sub" @tap="hidePosterShow">取消</view>
					<!-- #ifdef MP || APP-PLUS -->
					<view class="action margin-0 flex-sub solid-left text-red text-bold" @tap="savePoster">保存到相册</view>
					<!-- #endif -->
					<!-- #ifdef H5 -->
					<view class="action margin-0 flex-sub solid-left text-red text-bold"  @tap="hidePosterShow">长按图片可保存或分享</view>
					<!-- #endif -->
				</view>
			</view>
		</view>
		<poster id="poster" ref='posterRef' :hide-loading="false" :preload="false" :config="posterConfig"
			@success="onPosterSuccess"
			@fail="onPosterFail"></poster>
		<!-- #ifdef H5 || APP-PLUS -->
		<!-- 二维码组件，不显示，只用来生成二维码调用 说明文档 https://github.com/q310550690/uni-app-qrcode -->
		<!-- 该组件生成二维码时需要canvas元素装载,固直接引用组件没有使用js，效果一样 -->
		<view>
			<tki-qrcode ref="qrCodeRef" :size="260" :show="false" :val="curLocalUrl" @result="startCreatePoster"  icon="/static/public/logo.png"></tki-qrcode>
		</view>
		<!-- #endif -->

	</view>
</template>

<script>

	const app = getApp();
	const { base64src } = require("utils/base64src.js");
	import api from 'utils/api'
	import util from '@/utils/util'
	import __config from '@/config/env';

	import shareFriends from '@/components/share-friends/index.vue'
	import tkiQrcode from "@/components/tki-qrcode/tki-qrcode.vue"
	import poster from "@/components/wxa-plugin-canvas/poster/index";

	export default {
		components:{
			shareFriends,
			tkiQrcode,
			poster,
		},
		props: {
			value: Boolean ,// 是否显示弹框
			shareParams: {// 分享参数
				type: Object,
				default: () => {
					return {
						title: '', 	// 分享给微信好友链接时的标题
						desc: '',	// 分享给微信好友链接时的描述
						imgUrl: '',	// 分享给微信好友链接时的图片
						url: '', 	// 分享给微信好友链接时的url,  如果有传值则使用该值，否则用该页的当前路径

						scene: '', 	// 分享海报时的scene,一般为 id
						page: '', 	// 分享海报的page, 如果有传值则使用该值，否则用该页的当前路径
						posterConfig: null // 分享海报的配置
					}
				}
			},
			showShareFriends: { //显示分享好友，默认true显示
				type: Boolean,
				default: true
			},
			showSharePoster: { //显示分享海报，默认true显示
				type: Boolean,
				default: true
			}
		},
		mounted(){

		},
		watch:{
			value(val){},
			shareParams(val){},
			posterUrl(val){},
		},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				shareShow: ' show ',
				posterUrl: "",
				posterShow: false,
				posterConfig: null,
				curLocalUrl: null,
				showModal: false,
				isInitShare: false,
				shareObjTemp: {},
				isWeiXinBrowser: util.isWeiXinBrowser()
			};
		},
		created() {
			this.initData()
		},
		onShow() {},
		methods: {
			initData(){
				// #ifdef APP-PLUS
				api.wxAppConfig(__config.wxAppId).then(res => {
					if(res.data.data&&res.data.data.isComponent=='1') {
						this.curLocalUrl= util.setAppPlusShareUrl(res.data.data);
					}else{
						this.curLocalUrl = util.setAppPlusShareUrl();
					}
				});
				// #endif
			},
			shareHide() {
				this.$emit('input', false);
			},
			onPosterSuccess(e) {
				this.posterUrl = e;
				this.posterShow = true;
			},
			onPosterFail(err) {
				console.error(err);
			},
			hidePosterShow() {
				this.posterShow = false;
				this.$emit('input', false);
			},
			/**
			 * 异步生成海报
			 */
			onCreatePoster() {
				// #ifdef MP
					const userInfo = uni.getStorageSync('user_info')
					const userCode = userInfo ? '&'+userInfo.userCode : ''
					api.qrCodeUnlimited({
						theme: app.globalData.theme,  // 全局颜色变量
						page: this.shareParams.page ? this.shareParams.page : util.getCurPage(getCurrentPages()), // 当前页面路径
						scene: this.shareParams.scene + userCode
					}).then(res => {
						base64src(res.data, res2 => {
							this.startCreatePoster(res2);
						});
					});
				// #endif
				// #ifdef APP-PLUS
					let url = ''
					if(this.shareParams.url){
						url = this.shareParams.url;
						uni.showLoading({
							title: '海报生成中',
							mask: false
						});
						this.$refs.qrCodeRef._makeCode(url); // 需要先生成二维码后 才能生成海报
					}else{
						api.wxAppConfig(__config.wxAppId).then(res => {
							if(res.data.data&&res.data.data.isComponent=='1') {
								url = util.setAppPlusShareUrl(res.data.data);
							}else{
								url = util.setAppPlusShareUrl();
							}
							uni.showLoading({
								title: '海报生成中',
								mask: false
							});
							this.$refs.qrCodeRef._makeCode(url); // 需要先生成二维码后 才能生成海报
						});
					}
				// #endif
				// #ifdef H5
					let url = '';
					if(this.shareParams.url){
						url = this.shareParams.url
					}else{
						url =  util.setH5ShareUrl();
					}
					uni.showLoading({
						title: '海报生成中',
						mask: false
					});
					this.$refs.qrCodeRef._makeCode(url); // H5需要先生成二维码后 才能生成海报
				// #endif
			},
			startCreatePoster(res){ // 开始 生成海报
				uni.hideLoading();
				// 需要注意：分享海报的图片方法会传入res对象,用 qrCodeName: 'qrCodeName'属性进行唯一标识
				if(!this.shareParams.posterConfig){
					return
				}
				let images = this.shareParams.posterConfig.images;
				// 将二维码图片的base64放入到海报绘制配置中
				images.forEach(val=>{
					if(val.qrCodeName){
						val.url = res;
						return
					}
				})
				this.posterConfig = this.shareParams.posterConfig;
				this.posterShow = false;
				this.$refs.posterRef.onCreate(false, this.posterConfig); // 入参：true为抹掉重新生成
			},
			//点击保存到相册
			savePoster: function() {
				var that = this;
				uni.saveImageToPhotosAlbum({
					filePath: this.posterUrl,
					success(res) {
						that.posterShow = false;
						that.$emit('input', false);
						uni.showModal({
							content: '图片已保存到相册，赶紧晒一下吧~',
							showCancel: false,
							confirmText: '好的',
							confirmColor: '#333',
							success: function(res) {
								if (res.confirm) {
									/* 该隐藏的隐藏 */
									that.$emit('input', false);
								}
							},
							fail: function(res) {
								console.log(res);
							}
						});
					}
				});
			},
			previewImage(picUrl){
				// 预览图片
				uni.previewImage({
					urls: picUrl,
					longPressActions: {
						itemList: picUrl,
						success: function(data) {

						},
						fail: function(err) {
							console.log(err.errMsg);
						}
					}
				});
			},
		}
	};
</script>
<style>

	.show-bg{
		height: auto;
		margin-top: 120rpx;
	}

	.show-bg image{
		-webkit-touch-callout:default!important;
		height: 80vh;
	}
	.show-bg img{
		width: 100%;
		height: 90%;
		-webkit-touch-callout:default!important;
	}

	.image-box{
		height: 90%;
	}

	.show-btn{
		margin-top: 0;
	}
</style>
