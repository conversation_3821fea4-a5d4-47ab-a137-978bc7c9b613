<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view class="cu-card article bg-white radius-lg padding-sm" :class="card?'':'no-card'" v-if="shopInfo">
		<view class="flex align-center" @click="toShopHome(shopInfo.id)">
			<view class="text-df text-black text-bold">{{shopInfo.name}}</view>
			<view class="cu-tag bg-red light sm radius margin-left-xs" v-if="shopInfo.saleType == 2"> 自营 </view>
		</view>
		<view class="flex shop-detail margin-top-xs align-center">
			<image :src="shopInfo.imgUrl" mode="aspectFit"></image>
			<view class="text-gray margin-left-sm">
				<view class="cuIcon-locationfill location overflow-2">
					<text class="address text-sm">{{shopInfo.address}}</text>
				</view>
				<view class="cuIcon-mobilefill mobile margin-top-xs" :hover-stop-propagation="true"
					@click="callPhone(shopInfo.phone)">
					<text class="phone text-sm">{{shopInfo.phone}}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2018-2019
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	export default {
		data() {
			return {};
		},

		components: {},
		props: {
			shopInfo: {
				type: Object,
				default: () => ({})
			},
			card: {
				type: Boolean,
				default: true
			}
		},
		methods: {
			//跳转到商铺首页
			toShopHome(id) {
				uni.navigateTo({
					url: '/extraJumpPages/shop/shop-detail/index?id=' + id
				});
			},

			callPhone(phone) {
				uni.makePhoneCall({
					phoneNumber: phone
				});
			}
		}
	};
</script>
<style>
	.shop-detail image {
		width: 120rpx !important;
		height: 120rpx !important;
	}

	.location {
		width: 520rpx;
	}
</style>