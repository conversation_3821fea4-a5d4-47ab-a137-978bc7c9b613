<template>
	<view>
		<view class="flex align-center padding-lr padding-tb-sm">
			<view><image class="shop-logo xl" :src="shopStore.imgUrl" /></view>
			<view class="margin-left-sm">
				<view class="flex align-center padding-right-sm">
					<view class="text-df">
						<view class="cu-tag radius bg-scarlet sm margin-right-sm">自提门店</view>
						<text>{{shopStore.name}}</text>
					</view>
				</view>
				<view class=" align-center margin-top-xs text-gray">
					<text class="text-sm">{{shopStore.address}}</text>
					<text class="text-sm margin-left-xs">{{shopStore.phone}}</text>
				</view>
			</view>
		</view>
	</view>
</template>
<script>
	import api from '@/utils/api'
	export default {
		props: {
			storeId: {
				type: String,
				default: ''
			}
		},
		data() {
			return {
				shopStore: {}
			}
		},
		watch: {
			storeId: {
				handler(val) {
					this.shopStoreGet()
				},
				immediate: true
			}
		},
		methods: {
			shopStoreGet() {
				//门店地址
				if (this.storeId) {
					api.shopStoreGet(this.storeId).then(res => {
						this.shopStore = res.data;
					})
				}
			},
		}
	}
</script>
<style>
	.shop-logo {
		width: 108rpx;
		height: 108rpx;
	}

	.cu-tag.sm {
		font-size: 20upx;
		padding: 0upx 10upx;
		height: 40upx;
	}
</style>