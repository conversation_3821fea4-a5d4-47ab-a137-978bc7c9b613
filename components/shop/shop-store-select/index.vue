<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view>
		<view @click="showModal" class="flex align-center text-sm">
			{{ shopStoreName?shopStoreName:'选择自提门店' }}<text class="cuIcon-right"></text>
		</view>
		<view :class="'cu-modal bottom-modal ' + modal" @tap="hideModal">
			<view class="cu-dialog bg-gray padding-lr-sm" @tap.stop>
				<view class="text-lg text-center padding-top">自提门店</view>
				<scroll-view scroll-y scroll-with-animation style="max-height: 70vh;" @scrolltolower="onReachBottom">
					<view class="bg-white radius-lg  margin-tb-sm padding solid-bottom"
						v-for="(item, index) in shopStoreList" :key="index">
						<view v-if="storeId==item.id" class="checked text-xs"
							:class="'bg-'+theme.themeColor">当前选择</view>
						<view class="flex align-center">
							<image :src="item.imgUrl" class="round head-image"></image>
							<view class="margin-left-sm text-bold text-df flex align-center text-left">
								{{item.name}}
							</view>
						</view>
						<view class="margin-top-sm text-gray">
							<view class="flex align-center justify-between margin-top-sm">
								<view class="flex align-center">
									<image src="/static/public/img/phone.png" class="item-img"></image>
									<view class="text-gray margin-left-xs">{{item.phone}}</view>
								</view>
								<view class="flex align-center">
									<image src="/static/public/img/createTime.png" class="item-img"></image>
									<view class="text-gray margin-left-xs">
										{{item.businessHours?item.businessHours.split(',').join('-'):'-'}}
									</view>
								</view>
							</view>
							<view class="margin-top-sm flex align-center">
								<view class="cuIcon-locationfill margin-lr-xs"></view>
								<view class="text-left margin-left-xs">
									<text>{{item.address}}</text>
									<text class="text-sm" v-if="item.juli">（距你{{formatDistance(item.juli)}}）</text>
								</view>
							</view>
						</view>
						<view class="flex justify-end margin-top-xs" v-if="!disabled">
							<view class="cu-btn radius sm" :class="'bg-'+theme.themeColor" @click="onSelect(item)">
								选择门店</view>
						</view>
					</view>
					<view v-if="shopStoreList.length>0" :class="'cu-load ' + (loadmore?'loading':'over')"></view>
				</scroll-view>
				<view v-show="loadLocation" class="text-center padding-lr padding-bottom text-grey">
					<view class="flex align-center justify-center"><text class="cu-load loading"></text><text
							class="text-blue margin-left-sm" @click="shopStorePage(false)">直接加载</text></view>
					<view class="text-sm">正在根据当前位置加载门店信息...</view>
				</view>
				<view v-if="!loadLocation&&shopStoreList.length==0" class="text-center padding text-grey">没有门店不支持到店自提
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2023-2028
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	import api from '@/utils/api'

	export default {
		props: {
			shopId: { //店铺 Id
				type: String
			},
			storeId: { //门店 id
				type: String,
				default: ''
			},
			disabled: {
				type: Boolean,
				default: false
			}
		},
		watch: {
			storeId: {
				handler(val, oldVal) {},
				immediate: true
			}
		},
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: getApp().globalData.theme, //全局颜色变量
				page: {
					searchCount: false,
					current: 1,
					size: 5,
					ascs: 'juli',
					//升序字段
					descs: ''
				},
				parameter: {},
				loadmore: true,
				loadmoreTimeOut: 3, // 3秒后自动获取
				shopStoreList: [],
				shopStoreName: '',
				modal: '',
				loadLocation: false,
			};
		},
		created() {
			this.parameter.shopId = this.shopId;
			// 根据位置获取门店列表
			this.getLocation()
		},
		methods: {
			onSelect(item, hide = true) {
				this.shopStoreName = item.name;
				this.$emit('change', item.id)
				if (hide) {
					this.hideModal()
				}
			},
			getLocation() {
				let that = this
				if (this.loadLocation) return
				this.loadLocation = true
				uni.getLocation({
					// isHighAccuracy: true, //高精度定位
					success: function(res) {
						that.parameter.longitude = res.longitude
						that.parameter.latitude = res.latitude
						// console.log('当前位置success：', res);
						// console.log('当前位置的纬度：' + res.latitude);
					},
					complete: function(res) {
						// console.log('当前位置complete：', res);
						if (that.loadLocation) {
							that.shopStoreList = []
							that.shopStorePage();
						}
					},
					fail: function(res) {
						// console.log('当前位置fail：', res);
						// uni.showToast({
						//   icon: "none",
						//   title: "获取位置信息失败，请检查是否开启手机定位或应用权限！"
						// })
					}
				});
				// 在较新的浏览器上，H5 端获取定位信息，要求部署在 https 服务上，本地预览（localhost）仍然可以使用 http 协议。
				// 国产安卓手机上，H5若无法定位，检查手机是否开通位置服务、GPS，ROM是否给该浏览器位置权限、浏览器是否对网页弹出请求给予定位的询问框。
			},
			shopStorePage(hide = true) {
				this.loadLocation = false
				if (this.parameter.shopId) {
					api.shopStorePage(Object.assign({}, this.page, this.parameter)).then(res => {
						let shopStoreList = res.data.records;
						this.shopStoreList = [...this.shopStoreList, ...shopStoreList];
						if (shopStoreList.length < this.page.size) {
							this.loadmore = false;
						}
						uni.hideLoading()
						// 第一页时默认选第一个地址
						if (this.shopStoreList.length > 0 && this.page.current == 1) {
							this.onSelect(this.shopStoreList[0], hide)
						}
					});
				} else {
					this.loadmore = false;
					uni.hideLoading()
				}
			},
			refresh() {
				this.loadmore = true;
				this.shopStoreList = [];
				this.page.current = 1;
				this.shopStorePage();
			},
			onReachBottom() {
				if (this.loadmore) {
					this.page.current = this.page.current + 1;
					this.shopStorePage();
				}
			},
			onPullDownRefresh() {
				// 显示顶部刷新图标
				this.refresh(); // 隐藏导航栏加载框
			},
			showModal() {
				this.modal = 'show';
			},
			hideModal() {
				this.modal = '';
			},
			formatDistance(value) {
				if (!value) return ''
				if (value < 1000) {
					return value + 'm'
				} else {
					return (value / 1000).toFixed(2) + 'km'
				}
			}
		}
	};
</script>

<style lang="scss" scoped>
	.item-img {
		opacity: 0.5;
		width: 42rpx;
		height: 42rpx;
	}

	.head-image {
		width: 48rpx;
		height: 48rpx;
	}

	.cu-load.loading::after {
		content: "定位中...";
	}
	
	.checked{
		position: absolute;
		top:0; right: 0; 
		padding: 8rpx 15rpx; 
		border-radius: 0rpx 10rpx 0rpx 10rpx;
	}
</style>