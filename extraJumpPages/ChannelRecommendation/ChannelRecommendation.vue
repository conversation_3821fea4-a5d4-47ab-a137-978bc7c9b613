<template>
    <view class="ChannelRecommendation bc6">
        <view class="top-back-con bc6 w100 dfc">
            <text class="fc1" style="font-size: 60rpx;">渠道推荐</text>
        </view>
        <scroll-view class="scroll-view-list w100" scroll-y>
            <view class="shop-list w100 df flc jc-fs alc">
                <block v-for="(item,index) in shopData" :key="index">
                    <view class="shop-item w100 df flr jc-sb alc">
                        <view class="item-pic dfc">
                            <image :src="item['picUrls']"></image>
                        </view>
                        <view class="item-shop-info w100 f1 df flc jc-fs alc">
                            <view class="info-title w100 df flr jc-fs alc">
                                <text class="single-line-hide">{{ item['goodsSpuName'] }}</text>
                            </view>
                            <view class="info-label-list w100 df flr jc-fs alc" v-if="isEmptyArr(item['labelList'])">
                                <block v-for="(ite,ind) in item['labelList']" :key="ind">
                                    <BaseLabel class="base-label" :type="ite['labelType']" :text="ite['labelText']"></BaseLabel>
                                </block>
                            </view>
                            <view class="info-name w100 df flr jc-fs alc">
                                <text>{{ item['shopName'] }}</text>
                            </view>
                            <view class="price-info w100 df flr jc-sb alc bc7">
                                <view class="price-text df flr jc-fs alc">
                                    <text class="fc16">￥<text class="fc16">{{ item['priceUp'] }}</text></text>
                                </view>
                                <view class="price-btn dfc" @click="jumpPage(1, item)">
                                    <text class="fc1">去抢购</text>
                                </view>
                            </view>
                        </view>
                    </view>
                </block>
            </view>
        </scroll-view>
    </view>
</template>

<script name="ChannelRecommendation">
import BaseLabel from "@/components/BaseLabel/BaseLabel.vue";
import {getQueryString, isEmptyArr, go} from "@/utils/customUtil";
import { getProductList } from "@/utils/api";
import {setStorage} from "../../utils/customUtil";
export default {
    components: {
        BaseLabel
    },
    data() {
        return {
            channelId: "",
            CustomBar: this.CustomBar,
            shopData: []
        }
    },
    onLoad(options) {
        let optionsData = getQueryString(options)
        if(isEmptyArr(optionsData)) {
            this.channelId = optionsData[0]
            setStorage('channelId', optionsData[0])
        }
        this.getProductData()
    },
    methods: {
        // 跳转商品详情
        jumpPage(type, data) {
            if(type === 1) {
                go(`/pages/goods/goods-detail/index?id=${data.goodsSpuId}&channelId=${this.channelId}`)
            }
        },

        isEmptyArr,
        // 获取商品列表信息
        async getProductData() {
            const { code, data } = await getProductList({ channelId: this.channelId })
            if(code === 0) {
                this.shopData = data
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.ChannelRecommendation {
    .top-back-con {
        height: 330rpx;
    }
    .scroll-view-list {
        height: calc(100vh - 330rpx);
        border-top-left-radius: 30rpx;
        border-top-right-radius: 30rpx;
        background: linear-gradient(0deg, #F6F6F6 83%, #FFFFFF 100%);
        .shop-list {
            padding: 20rpx;
            .shop-item {
                margin-bottom: 20rpx;
                .item-pic {
                    padding-right: 20rpx;
                    image {
                        width: 240rpx;
                        height: 240rpx;
                        border-top-left-radius: 20rpx;
                        border-bottom-left-radius: 20rpx;
                    }
                }
                .item-shop-info {
                    .info-title {
                        margin-bottom: 20rpx;
                    }
                    .info-label-list {
                        margin-bottom: 20rpx;
                        .base-label {
                            margin-right: 10rpx;
                        }
                        .base-label:last-child {
                            margin-right: 0;
                        }
                    }
                    .info-name {
                        margin-bottom: 20rpx;
                    }
                    .price-info {
                        padding-left: 20rpx;
                        border-radius: 30rpx;
                        .price-text {
                            text {
                                font-size: 24rpx;
                                text {
                                    font-size: 40rpx;
                                }
                            }
                        }
                        .price-btn {
                            padding: 27rpx 20rpx;
                            background: linear-gradient(0deg, #FD3D0C 0%, #FF540F 100%);
                            border-radius: 30rpx;
                        }
                    }
                }
            }
            .shop-item:last-child {
                margin-bottom: 0;
            }
        }
    }
}
</style>