<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true">
			<block slot="backText">返回</block>
			<block slot="content">搜索</block>
		</cu-custom>

		<view class="cu-bar search" :class="'bg-'+theme.backgroundColor">
			<view class="search-form round">
				<text class="cuIcon-search"></text>
				<input v-model="searchKeyword" class="text-dm text-gray"  autofocus="autofocus" type="text" placeholder="请输入关键字" confirm-type="search"
					@confirm="searchHandle" focus></input>
				<text @click="searchClick" class="text-sm margin-right"
					:class="'text-'+theme.themeColor">{{shopId ? '搜索本店' : '搜索'}}</text>
			</view>
		</view>
		<view v-if="searchHistory.length > 0">
			<view class="cu-bar bg-white">
				<view class="action"><text class="cuIcon-time"></text>历史搜索</view>
				<view class="action">
					<text class="cuIcon-delete lg text-gray" @tap="clearSearchHistory"></text>
				</view>
			</view>
			<view class="padding-sm flex flex-wrap bg-white">
				<view class="padding-xs" v-for="(item, index) in searchHistory" :key="index">
					<view class="cu-tag round" @tap="searchHandle" :data-name="item.name">{{item.name}}</view>
				</view>
			</view>
		</view>
		<view v-if="goodsList">
			<view class="cu-bar bg-white">
				<view class="action"><text class="cuIcon-hot text-orange"></text>全网热榜</view>
			</view>
			<view class="cu-list menu card-menu sm-border margin-top-sm">
				<view class="cu-item" v-for="(item, index) in goodsList" :key="index">
					<navigator hover-class="none" :url="'/pages/goods/goods-detail/index?id=' + item.id"
						class="content overflow-1" :data-name="item.name">
						<text class="margin-right-sm text-bold text-orange">{{index+1}}</text>{{item.name}}
					</navigator>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2018-2019
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	const app = getApp();
	const util = require("utils/util.js");
	import api from 'utils/api'

	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				searchHistory: [],
				goodsList: [],
				searchKeyword: '',
				shopId: ''
			};
		},

		components: {},
		props: {},

		onShow() {
			this.searchHistory = uni.getStorageSync('searchHistory') ? uni.getStorageSync('searchHistory') : [];
		},

		onLoad(options) {
			// 保存别人分享来的 userCode
			util.saveSharerUserCode(options);
			if (options.shopId && options.shopId != 'null' && options.shopId != 'undefined') {
				this.shopId = options.shopId
			}
			app.initPage().then(res => {
				this.goodsPage();
			});
		},

		methods: {
			searchClick() {
				if (this.searchKeyword) {
					this.searchHandle({
						detail: {
							value: this.searchKeyword
						}
					});
				}
			},
			searchHandle(e) {
				let value;

				if (e.detail.value) {
					value = e.detail.value;
				} else if (e.currentTarget.dataset.name) {
					value = e.currentTarget.dataset.name;
				}
				this.searchKeyword = value

				let searchHistory = this.searchHistory;
				searchHistory.forEach(function(item, index) {
					let i = 9; //最多缓存10条

					if (item.name == value) {
						searchHistory.splice(index, 1);
						i++;
					}

					if (index >= i) {
						searchHistory.splice(index, 1);
					}
				});
				searchHistory.unshift({
					name: value
				});
				uni.setStorageSync('searchHistory', searchHistory);
				uni.navigateTo({
					url: '/pages/goods/goods-list/index?name=' + value + '&shopId=' + this.shopId
				});
			},

			clearSearchHistory() {
				let that = this;
				uni.showModal({
					content: '确认删除全部历史记录？',
					cancelText: '我再想想',
					confirmColor: '#ff0000',
					success(res) {
						if (res.confirm) {
							that.searchHistory = [];
							uni.setStorageSync('searchHistory', []);
						}
					}

				});
			},

			goodsPage() {
				api.goodsPage({
					searchCount: false,
					current: 1,
					size: 10,
					ascs: '',
					//升序字段
					descs: 'sale_num'
				}).then(res => {
					this.goodsList = res.data.records;
				});
			}

		}
	};
</script>
<style>
	.search-home {
		top: unset !important;
	}

	.text-dm {
		font-size: 26upx;
	}
</style>
