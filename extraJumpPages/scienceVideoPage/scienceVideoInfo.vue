<template>
    <view class="scienceVideoInfo">
<!--        <BaseNavigationBar title="视频播放"></BaseNavigationBar>-->
        <cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true">
            <block slot="backText">返回</block>
            <block slot="content">视频播放</block>
        </cu-custom>
        <view class="video-title-box w100 df flc jc-fs alc bc1">
            <video class="video-box w100" :src="videoInfo['videoUrl']" controls></video>
            <view class="title-box w100 df flc jc-fs alc">
                <view class="title-text w100 df flr jc-fs alc">
                    <text class="fc5 single-line-hide">{{ videoInfo.title }}</text>
                </view>
                <view class="title-info-list w100 df flr jc-fs alc">
                    <BaseIconText class="info-show-item" icon="public/icon/chakan.png" :text="videoInfo['playNum']"></BaseIconText>
                    <BaseIconText class="info-show-item" icon="public/icon/dianzan.png" :text="videoInfo['likesNum']"></BaseIconText>
                    <BaseIconText class="info-show-item" icon="public/icon/shoucang.png" :text="videoInfo['playNum']"></BaseIconText>
                </view>
            </view>
        </view>
        <view class="video-introduction w100 df flc jc-fs alc bc1">
            <view class="video-introduction-title w100 dfc">
                <text class="fc5">视频简介</text>
            </view>
            <view class="video-introduction-con w100 df flc jc-fs als">
<!--                <text class="fc8">{{ videoInfo['brief'] }}</text>-->
                <u-parse :content="videoInfo['brief']"></u-parse>
            </view>
        </view>
    </view>
</template>

<script name="scienceVideoInfo">
import uParse from '@/components/u-parse/u-parse.vue'
import BaseNavigationBar from "@/components/BaseNavigationBar/BaseNavigationBar.vue";
import BaseIconText from "@/components/BaseIconText/BaseIconText.vue";
import api from "@/utils/api";
const app = getApp();

export default {
    components: {
        uParse,
        BaseIconText,
        BaseNavigationBar
    },
    data() {
        return {
            vid: "",
            videoInfo: {},
            theme: app.globalData.theme //全局颜色变量
        }
    },
    onLoad(options) {
        this.vid = options.vid
        this.getVideoInfo()
    },
    methods: {
        // 获取视频详情信息
        async getVideoInfo() {
            const { code, data } = await api.getVideoPopularInfo({id: this.vid})
            if(code === 0) {
                this.videoInfo = data
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.scienceVideoInfo {
    .video-title-box {
        .video-box {
            height: 422rpx;
        }
        .title-box {
            padding: 27rpx 22rpx 22rpx 22rpx;
            .title-text {
                margin-bottom: 30rpx;
                text {
                    font-size: 40rpx;
                    font-weight: 800;
                }
            }
            .title-info-list {
                .info-show-item {
                    margin-right: 19rpx;
                }
                .info-show-item:last-child {
                    margin-right: 0;
                }
            }
        }
    }
    .video-introduction {
        margin-top: 20rpx;
        padding: 30rpx 20rpx;
        .video-introduction-title {
            margin-bottom: 30rpx;
            text {
                font-size: 30rpx;
                font-weight: 500;
            }
        }
        .video-introduction-con {
            text {
                font-size: 26rpx;
                font-weight: 500;
            }
        }
    }
}
</style>