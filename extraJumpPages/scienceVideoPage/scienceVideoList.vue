<template>
    <view class="scienceVideoList">
<!--        <BaseNavigationBar title="科普视频"></BaseNavigationBar>-->
        <cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true">
            <block slot="backText">返回</block>
            <block slot="content">科普视频</block>
        </cu-custom>
        <scroll-view class="top-list bc1" :scroll-x="true">
            <view class="top-list-box df flr jc-fs alc">
                <block v-for="(item,index) in titleData['data']" :key="index">
                    <view class="top-list-item df flc jc-fs alc fs0" @click="titleClick(index)">
                        <view class="item-text dfc">
                            <text :class="index === titleData['curNum']?'fc7':'fc5'">{{ item.vtName }}</text>
                        </view>
                        <view :class="index === titleData['curNum']?'item-lines':'item-line'"></view>
                    </view>
                </block>
            </view>
        </scroll-view>

        <scroll-view class="bot-list w100" :style="{ height: `calc(100vh - ${CustomHeight}px - 80rpx)` }" :scroll-y="true" @scrolltolower="botTrigger">
            <view class="list-box w100">
                <block v-for="(item,index) in videoList" :key="index">
                    <view class="list-item df flc jc-fs alc bc1" @click="viewDetails(item)">
                        <view class="item-pic dfc w100">
                            <image class="w100" :src="item['hostGraphUrl']"></image>
                        </view>
                        <view class="item-info w100 df flc jc-fs alc">
                            <view class="info-title w100 df flr jc-fs alc">
                                <text class="single-line-hide fc5">{{ item.title }}</text>
                            </view>
                            <view class="info-show-list w100 df flr jc-fe alc">
                                <BaseIconText class="info-show-item" icon="public/icon/chakan.png" :text="item['playNum']"></BaseIconText>
                                <BaseIconText class="info-show-item" icon="public/icon/dianzan.png" :text="item['likesNum']"></BaseIconText>
                                <BaseIconText class="info-show-item" icon="public/icon/shoucang.png" :text="item['playNum']"></BaseIconText>
                            </view>
                        </view>
                    </view>
                </block>
            </view>
        </scroll-view>
    </view>
</template>

<script name="scienceVideoList">
import BaseNavigationBar from "@/components/BaseNavigationBar/BaseNavigationBar.vue";
import BaseIconText from "@/components/BaseIconText/BaseIconText.vue";
import {go} from "@/utils/customUtil";
import api from '@/utils/api'
const app = getApp();
import {deepCopy} from "../../utils/customUtil";
export default {
    components: {
        BaseIconText,
        BaseNavigationBar
    },
    data() {
        return {
            CustomHeight: this.CustomBar,
            theme: app.globalData.theme, //全局颜色变量
            titleData: {
                curNum: 0,
                data: []
            },
            videoParams: {
                vtId: "",
                current: 1,
                size: 5,
                total: 0
            },
            videoList: []
        }
    },
    async onLoad() {
        await this.getVideoList()
        await this.getVideoInfoList()
    },
    methods: {
        // 滚动到底部
        botTrigger() {
            let videoLength = this.videoList.length
            let total = this.videoParams.total
            if(videoLength < total) {
                this.videoParams.current++
                this.getVideoInfoList()
            }
        },
        // 获取视频信息列表
        async getVideoInfoList() {
            let that = this
            let params = deepCopy(that.videoParams)
            params['vtId'] = that.titleData.data[that.titleData.curNum].id
            let { code, data } = await api.getVideoPopularList(params)
            if(code === 0) {
                that.videoParams.total = data['total']
                that.videoList = [...that.videoList, ...data['records']]
            }
        },

        // 获取视频分类列表
        async getVideoList() {
            let { code, data } = await api.getVideoClassificationList()
            if(code === 0) {
                this.titleData.data = data
                data.unshift({
                    id: "",
                    vtName: "全部"
                })
            }
        },

        // 查看详情
        viewDetails(data) {
            go(`/extraJumpPages/scienceVideoPage/scienceVideoInfo?vid=${data.id}`)
        },
        // 标题切换
        titleClick(index) {
            this.titleData.curNum = index
            this.videoList = []
            this.getVideoInfoList()
        }
    }
}
</script>

<style lang="scss" scoped>
.scienceVideoList {
    .top-list {
        .top-list-box {
            height: 80rpx;
            padding: 0 40rpx;
            .top-list-item {
                padding-right: 50rpx;
                .item-text {
                    padding: 25rpx 0 18rpx 0;
                    text {
                        font-size: 28rpx;
                        font-weight: 500;
                    }
                }
                .item-line {
                    width: 60rpx;
                    height: 6rpx;
                    transform: translateY(-5rpx);
                }
                .item-lines {
                    width: 60rpx;
                    height: 6rpx;
                    background-color: #FF6203;
                    border-radius: 3rpx;
                    transform: translateY(-5rpx);
                }
            }
        }
    }
    .bot-list {
        .list-box {
            padding: 16rpx;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            grid-gap: 16rpx;
            .list-item {
                border-radius: 10rpx;
                .item-pic {
                    image {
                        border-top-left-radius: 10rpx;
                        border-top-right-radius: 10rpx;
                        height: 450rpx;
                    }
                }
                .item-info {
                    padding: 20rpx 16rpx 21rpx 20rpx;
                    border-bottom-left-radius: 10rpx;
                    border-bottom-right-radius: 10rpx;
                    .info-title {
                        margin-bottom: 30rpx;
                        text {
                            font-size: 28rpx;
                            font-weight: 500;
                        }
                    }
                    .info-show-list {
                        .info-show-item {
                            margin-right: 19rpx;
                        }
                        .info-show-item:last-child {
                            margin-right: 0;
                        }
                    }
                }
            }
        }
    }
}
</style>