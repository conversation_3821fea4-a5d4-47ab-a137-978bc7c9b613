<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view :class="'bg-'+theme.backgroundColor">
		<form @submit="shopApplySubmit">
			<cu-custom :isBack="true" :bgColor="showBg?'bg-'+theme.backgroundColor:''">
				<block slot="backText">返回</block>
				<block slot="content">店铺入驻</block>
			</cu-custom>
			<image class="shop-apply-bg" src="https://minio.joolun.com/joolun/1/material/99b36b9e-8472-4298-95ec-32a8d4796d2e.png"></image>
			<view class="padding-lr-sm padding-bottom-xl">
				<view class="bg-white padding-lr-sm shop-apply-list">
					<view class="padding-top-xl">
						<view class="cu-steps padding-tb-xl margin-top-xl">
							<view class="cu-item" :class="index>curStep?'':'text-red'"
								v-for="(item,index) in stepList" :key="index">
								<!-- <text class="num" :class="index==2?'err':''" :data-index="index + 1"></text> {{item.name}} -->
								<text class="num" :data-index="index + 1"></text> {{item.name}}
							</view>
						</view>
					</view>
					<view v-if="applyForm.status">
						<!-- <view > -->
						<view class="cu-bar solid-bottom">
							<view class="action">
								<text class="cuIcon-formfill text-red"></text> 审核信息
							</view>
						</view>
						<view class="cu-form-group">
							<view class="title">申请编号</view>
							<view>{{applyForm.id}}<text class="cuIcon-info margin-left-xs"
									@click="tipsModal=true"></text><text class="margin-left-xs text-blue"
									@click="copyData(applyForm.id)">复制</text></view>
						</view>
						<view v-if="applyForm.createTime" class="cu-form-group">
							<view class="title">申请时间</view>
							<view>{{applyForm.createTime}}</view>
						</view>
						<view v-if="applyForm.updateTime" class="cu-form-group">
							<view class="title">更新时间</view>
							<view>{{applyForm.updateTime}}</view>
						</view>
						<view class="cu-form-group">
							<view class="title">审核状态</view>
							<view v-if="applyForm.status==0" class="text-blue">审核中</view>
							<view v-if="applyForm.status==1" class="text-green">审核通过</view>
							<view v-if="applyForm.status==2" class="text-red">审核不通过</view>
						</view>
						<view v-if="applyForm.applyDetail" class="cu-form-group">
							<view class="title" style="min-width: 100px;">审核说明</view>
							<text>{{applyForm.applyDetail}}</text>
						</view>
					</view>
					<view class="cu-bar solid-bottom">
						<view class="action">
							<text class="cuIcon-shopfill text-red"></text> 店铺信息
						</view>
					</view>
					<view class="cu-form-group">
						<view class="title"><text class="text-red">*</text>店铺名称</view>
						<input class="text-right" maxlength="100" placeholder="请输入店铺名" name="name"
							:value="applyForm.name"></input>
					</view>
					<!-- 店铺头像 -->
					<view class="cu-form-group">
						<view class="title">店铺头像</view>
						<view class="grid col-4 grid-square flex-sub custom-grid" style="justify-content: flex-end;">
							<view class="bg-img" v-if="applyForm.imgUrl">
								<image :src="applyForm.imgUrl" mode="aspectFill" @tap="previewImage('imgUrl')"></image>
								<view class="cu-tag bg-red" @tap.stop="delImg('imgUrl')">
									<text class='cuIcon-close'></text>
								</view>
							</view>
							<view class="solids" @tap="chooseImage('imgUrl')" v-else>
								<text class='cuIcon-cameraadd'></text>
							</view>
						</view>
					</view>
					<view class="cu-form-group">
						<view class="title"><text class="text-red">*</text>店铺电话</view>
						<input class="text-right" maxlength="15" placeholder="请输入店铺号码" name="phone"
							:value="applyForm.phone"></input>
					</view>
					<view class="cu-form-group">
						<view class="title">详细地址</view>
		      	<view @tap="chooseLocation" class="picker">{{ applyForm.address || '点击选择地址' }}</view>
					</view>
					<view class="cu-form-group">
						<view class="title"><text class="text-red">*</text>营业执照</view>
						<view class="grid col-4 grid-square flex-sub custom-grid" style="justify-content: flex-end;">
							<view class="bg-img" v-if="applyForm.licenseUrl">
								<image :src="applyForm.licenseUrl" mode="aspectFill" @tap="previewImage('licenseUrl')"></image>
								<view class="cu-tag bg-red" @tap.stop="delImg('licenseUrl')">
									<text class='cuIcon-close'></text>
								</view>
							</view>
							<view class="solids" @tap="chooseImage('licenseUrl')" v-else>
								<text class='cuIcon-cameraadd'></text>
							</view>
						</view>
					</view>
					<view class="cu-form-group">
						<view class="title">详细介绍</view>
						<input class="text-right" maxlength="500" placeholder="请输入详细介绍" name="detail"
							:value="applyForm.detail"></input>
					</view>
					<view class="cu-bar  solid-bottom">
						<view class="action">
							<text class="cuIcon-profilefill text-red"></text> 店长信息
						</view>
					</view>
					<view class="cu-form-group">
						<view class="title"><text class="text-red">*</text>登录账号</view>
						<input class="text-right" maxlength="100" placeholder="请输入登录账号" name="userUsername"
							:value="applyForm.userUsername"></input>
					</view>
					<view class="cu-form-group">
						<view class="title"><text class="text-red">*</text>登录密码</view>
						<input class="text-right" maxlength="100" placeholder="请输入登录密码" name="userPassword"
							:value="applyForm.userPassword"></input>
					</view>
					<view class="cu-form-group">
						<view class="title"><text class="text-red">*</text>手机号</view>
						<input class="text-right" maxlength="100" placeholder="请输入店长手机号" name="userPhone"
							:value="applyForm.userPhone"></input>
					</view>
					<view class="cu-form-group">
						<view class="title"><text class="text-red">*</text>邮箱</view>
						<input class="text-right" maxlength="100" placeholder="请输入店长邮箱" name="userEmail"
							:value="applyForm.userEmail"></input>
					</view>
					<view class="cu-form-group">
						<view class="title"><text class="text-red">*</text>昵称</view>
						<input class="text-right" maxlength="100" placeholder="请输入店长昵称" name="userNickname"
							:value="applyForm.userNickname"></input>
					</view>
					<view class="cu-form-group">
						<view class="title"><text class="text-red">*</text>身份证正面</view>
						<view class="grid col-4 grid-square flex-sub custom-grid" style="justify-content: flex-end;">
							<view class="bg-img" v-if="applyForm.idCardFrontUrl">
								<image :src="applyForm.idCardFrontUrl" mode="aspectFill" @tap="previewImage('idCardFrontUrl')"></image>
								<view class="cu-tag bg-red" @tap.stop="delImg('idCardFrontUrl')">
									<text class='cuIcon-close'></text>
								</view>
							</view>
							<view class="solids" @tap="chooseImage('idCardFrontUrl')" v-else>
								<text class='cuIcon-cameraadd'></text>
							</view>
						</view>
					</view>
					<view class="cu-form-group">
						<view class="title"><text class="text-red">*</text>身份证背面</view>
						<view class="grid col-4 grid-square flex-sub custom-grid" style="justify-content: flex-end;">
							<view class="bg-img" v-if="applyForm.idCardBackUrl">
								<image :src="applyForm.idCardBackUrl" mode="aspectFill" @tap="previewImage('idCardBackUrl')"></image>
								<view class="cu-tag bg-red" @tap.stop="delImg('idCardBackUrl')">
									<text class='cuIcon-close'></text>
								</view>
							</view>
							<view class="solids" @tap="chooseImage('idCardBackUrl')" v-else>
								<text class='cuIcon-cameraadd'></text>
							</view>
						</view>
					</view>
					<view class="compile padding-bottom margin-top-xl">
						<button class="cu-btn  block margin-sm lg round bottom-btn" :class="'bg-'+theme.backgroundColor"
							formType="submit">{{applyForm.id?'清空':'提交'}}</button>
					</view>
					<view class="padding-sm text-center text-blue">
						<view @click="showQueryModal()">申请单查询</view>
					</view>
				</view>
			</view>
		</form>
		<view class="cu-modal" :class="queryModal ? ' show' : ''" style="z-index: 10!important">
			<view class="cu-dialog bg-white">
				<view class="cu-bar  justify-end">
					<view class="content text-bold">申请单查询</view>
					<view class="action" @click="hideQueryModal">
						<text class="cuIcon-close text-red"></text>
					</view>
				</view>
				<view class="padding-sm">
					<form @submit="querySubmit">
						<view class="cu-form-group ">
							<view class="title">申请单编号</view>
							<input placeholder="请输入申请单编号" name="id" v-model="queryForm.id"></input>
						</view>
						<view class="margin-left text-left margin-top" >
							<view class="margin-bottom-sm text-gray"><text class="cuIcon-search margin-right-xs"></text>申请记录查询</view>
							<view v-for="(item,index) in historeyRecord" class="padding-left margin-top-xs">
								<text class="text-gray" @click="queryRecord(item)">{{item}}</text><text @click="delQueryRecord(item)" class="cuIcon-delete margin-left"></text>
							</view>
							<view v-if="historeyRecord.length==0" class="text-gray text-sm text-center" >暂无记录</view>
						</view>
						<view class="padding-top flex flex-direction">
							<button class="cu-btn round margin-tb-sm lg" :class="'bg-'+theme.backgroundColor"
								form-type="submit">提交</button>
						</view>
					</form>
				</view>
			</view>
		</view>
		<view class="cu-modal" :class="tipsModal ? ' show' : ''" style="z-index: 10!important">
			<view class="cu-dialog bg-white">
				<view class="cu-bar  justify-end">
					<view class="content text-bold">提示</view>
					<view class="action" @click="tipsModal=false">
						<text class="cuIcon-close text-red"></text>
					</view>
				</view>
				<view class="padding-sm text-xl">
					<view style="text-align: start;">
						<text class="margin-lr"></text>我们将在三个工作日内联系您进行申请入驻资料信息确认，请复制保存好<text
							class="text-blue">申请编号</text>进行后续的查询操作。
					</view>
				</view>
				<view class="padding-sm flex flex-direction">
					<button class="cu-btn round lg" @click="tipsModal=false" :class="'bg-'+theme.backgroundColor">确认</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2018-2019
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	const app = getApp();
	const util = require("utils/util.js");
	import api from '@/utils/api'
	import validate from '@/utils/validate'
	import __config from 'config/env';
	import shopApplyUtil from './shop-apply-util.js';

	export default {
		data() {
			return {
				showBg: false, //是否显示背景色
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				curStep: 0,
				stepList: [{
					name: '填写申请单'
				}, {
					name: '审核申请单'
				}, {
					name: '审核完成'
				}],
				applyForm: {
					id: '',
					createTime: '',
					updateTime: '',
					status: '', //审核状态 0审核中 1审核通过 2审核不通过
					applyDetail: '', //审核细节说明
					name: '',
					phone: '',
					address: '',
					detail: '',
					userUsername: '',
					userPassword: '',
					userPhone: '',
					userEmail: '',
					userNickname: '',
					licenseUrl: '',
					idCardFrontUrl: '',
					idCardBackUrl: '',
					imgUrl: '',
				}, //申请表单
				userInfo: {},
				queryModal: false,
				queryForm: {},
				tipsModal: false,
				historeyRecord: [] //申请的历史记录
			};
		},
		components: {

		},
		props: {},
		onLoad(options) {
			this.userInfoGet()
		},
		//页面滑动 监听事件
		onPageScroll(e) {
			if (e.scrollTop > 20) {
				this.showBg = true;
			} else {
				this.showBg = false;
			}
		},
		onShow() {
		},
	methods: {
		chooseLocation() {
				console.log("选择地址");
				uni.chooseLocation({
					success: (res) => {
						console.log(res)
						this.applyForm.address = res.address
						this.applyForm.latitude = res.latitude
						this.applyForm.longitude= res.longitude
					}
        })
			},
			//获取商城用户信息
			userInfoGet() {
				api.userInfoGet().then(res => {
					this.userInfo = res.data;
				});
			},
			async shopApplySubmit(e) {
				if (this.applyForm.id) { //清空表单
					this.curStep = 0
					this.applyForm = {
						id: '',
						createTime: '',
						updateTime: '',
						status: '', //审核状态 0审核中 1审核通过 2审核不通过
						applyDetail: '', //审核细节说明
						name: '',
						phone: '',
						address: '',
						detail: '',
						userUsername: '',
						userPassword: '',
						userPhone: '',
						userEmail: '',
						userNickname: '',
						licenseUrl: '',
						idCardFrontUrl: '',
						idCardBackUrl: '',
						imgUrl: '',
					}
				} else {
					let value = {
						...e.detail.value,
						licenseUrl: this.applyForm.licenseUrl,
						idCardFrontUrl: this.applyForm.idCardFrontUrl,
						idCardBackUrl: this.applyForm.idCardBackUrl,
						imgUrl: this.applyForm.imgUrl,
						address: this.applyForm.address,
						latitude: this.applyForm.latitude ? Number(this.applyForm.latitude) : null,
						longitude: this.applyForm.longitude ? Number(this.applyForm.longitude) : null
					};
					// 验证申请表单 输入是否正确
					let validateResult = await shopApplyUtil.validateShopApplyForm(value, this.userInfo)
					if (validateResult) {
						value.tenantId = this.userInfo.tenantId
						console.log(value,"value")
						// return
						api.shopApply(value).then(res => {
							this.saveQueryRecord(res.data.id)
							this.applyForm = res.data
							
							if (this.applyForm.status == '0') {
								this.curStep = 1
							} else {
								this.curStep = 2
							}
							uni.showModal({
								content: '申请成功，等待审核。',
								showCancel: false,
								confirmColor: '#ff0000',
								success(res) {

								}
							});
						});
					}
				}
			},
			queryRecord(id){
				this.queryForm.id = id
				this.querySubmit()
			},
			delQueryRecord(id){
				uni.showModal({
					content: '确认要删除吗？',
					confirmColor: '#ff0000',
					success: (res)=>{
						if (res.confirm) {
							let index = this.historeyRecord.indexOf(id)
							this.historeyRecord.splice(index, 1);
							uni.setStorageSync('historeyApplyShopRecord'+this.userInfo.id, this.historeyRecord);
						}
					}
				});
				
			},
			saveQueryRecord(id){
				let value = id
				let searchHistory = uni.getStorageSync('historeyApplyShopRecord'+this.userInfo.id) ? uni.getStorageSync('historeyApplyShopRecord'+this.userInfo.id) : [];
				searchHistory.forEach(function(item, index) {
					let i = 4; //最多缓存5条
					if (item == value) {
						searchHistory.splice(index, 1);
						i++;
					}
					if (index >= i) {
						searchHistory.splice(index, 1);
					}
				});
				searchHistory.unshift(value);
				uni.setStorageSync('historeyApplyShopRecord'+this.userInfo.id, searchHistory);
				this.historeyRecord = searchHistory
			},
			showQueryModal() {
				this.historeyRecord = uni.getStorageSync('historeyApplyShopRecord'+this.userInfo.id) ? uni.getStorageSync('historeyApplyShopRecord'+this.userInfo.id) : [];
				this.queryModal = true
			},
			hideQueryModal() {
				this.queryModal = false
			},
			querySubmit(e) {
				if (!this.queryForm.id) {
					uni.showToast({
						title: '请输入申请单编号',
						icon: 'none',
						duration: 3000
					});
					return;
				}
				this.curStep = 0
				this.saveQueryRecord(this.queryForm.id)
				api.shopApplyQuery({
					tenantId: this.userInfo.tenantId,
					id: this.queryForm.id
				}).then(res => {
					//回显查询单数据，或者提示未找到申请单
					if (res.data) {
						this.applyForm = res.data
						if (this.applyForm.status == '0') {
							this.curStep = 1
						} else {
							this.curStep = 2
						}
						this.queryModal = false
					} else {
						uni.showToast({
							title: '没有找到该申请单的数据',
							icon: 'none',
							duration: 3000
						});
					}
				});
			},

			//复制内容
			copyData(value) {
				uni.setClipboardData({
					data: value,
					success: function() {
						uni.showToast({
							title: '复制成功',
							icon: 'none',
						});
					}
				});
			},
			chooseImage(type) {
				uni.chooseImage({
					count: 1,
					sizeType: ['compressed'],
					sourceType: ['album', 'camera'],
					success: (res) => {
						// 上传图片到服务器
						api.uploadFile(res.tempFilePaths[0], 'license', type).then(link => {
							this.applyForm[type] = link;
							uni.showToast({
								title: '上传成功',
								icon: 'success'
							});
						}).catch(() => {
							uni.showToast({
								title: '上传失败',
								icon: 'none'
							});
						});
					}
				});
			},
			delImg(type) {
				this.applyForm[type] = ''
			},
			previewImage(type) {
				// 获取当前图片url
				const current = this.applyForm[type];
				// 获取所有已上传的图片url数组
				const urls = [];
				if (this.applyForm.licenseUrl) urls.push(this.applyForm.licenseUrl);
				if (this.applyForm.idCardFrontUrl) urls.push(this.applyForm.idCardFrontUrl);
				if (this.applyForm.idCardBackUrl) urls.push(this.applyForm.idCardBackUrl);
				if (this.applyForm.imgUrl) urls.push(this.applyForm.imgUrl);
				uni.previewImage({
					current: current, // 当前显示图片的url
					urls: urls, // 需要预览的图片url列表
					indicator: 'number',
					loop: true
				});
			},
		}
	};
</script>

<style>
	.avatar-wrapper {
		background-color: #ffffff;
	}

	.avatar-wrapper::after {
		border-style: none;
	}

	.shop-apply-bg {
		width: 100%;
		height: 750rpx;
		margin: -180rpx auto;
	}
	
	.shop-apply-list{
		margin-top: -200rpx;
		border-radius: 0px 0px 20px 20px;
	}
	.custom-grid>view {
		margin-bottom: 0 !important;
	}
</style>
