/**
 * Copyright (C) 2018-2022
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
/**
 * 店铺入驻工具库
 */
import api from '@/utils/api'
import validate from '@/utils/validate'

// 验证提交的表单
async function validateShopApplyForm(value, userInfo){
	if (!value.name) {
		uni.showToast({
			title: '请输入店铺名',
			icon: 'none',
			duration: 3000
		});
		return false;
	}
	let nameValidateResult = await validateName(value.name,userInfo.tenantId)
	if (!nameValidateResult) {
		uni.showToast({
			title: '店铺名已经存在，请更换',
			icon: 'none',
			duration: 3000
		});
		return false;
	}
	if (!value.phone) {
		uni.showToast({
			title: '请输入店铺电话号码',
			icon: 'none',
			duration: 3000
		});
		return false;
	} 
	
	if (!value.licenseUrl) {
		uni.showToast({
			title: '请上传营业执照',
			icon: 'none',
			duration: 3000
		});
		return false;
	}
	
	if (!value.userUsername) {
		uni.showToast({
			title: '请输入登录账号',
			icon: 'none',
			duration: 3000
		});
		return false;
	}
	let userNameValidateResult = await validateUserName(value.name, userInfo.tenantId)
	if (!userNameValidateResult) {
		uni.showToast({
			title: '输入的登录账号已经存在，请更换',
			icon: 'none',
			duration: 3000
		});
		return false;
	}
	if (!value.userPassword) {
		uni.showToast({
			title: '请输入登录密码',
			icon: 'none',
			duration: 3000
		});
		return false;
	}
	if (!(value.userPassword.length>5&&value.userPassword.length<65)) {
		uni.showToast({
			title: '登录密码长度必须为6至64的范围内',
			icon: 'none',
			duration: 3000
		});
		return false;
	}
	if (!value.userPhone) {
		uni.showToast({
			title: '请输入手机号',
			icon: 'none',
			duration: 3000
		});
		return false;
	} else {
		if (!validate.validateMobile(value.userPhone)) {
			uni.showToast({
				title: '输入的店长手机号不正确',
				icon: 'none',
				duration: 3000
			});
			return false;
		}
		let userPhoneValidateResult = await validateUserPhone(value.userPhone, userInfo.tenantId)
		if (!userPhoneValidateResult) {
			uni.showToast({
				title: '输入的店长手机号已经存在，请更换',
				icon: 'none',
				duration: 3000
			});
			return false;
		}
	}
	if (!value.userEmail) {
		uni.showToast({
			title: '请输入店长邮箱',
			icon: 'none',
			duration: 3000
		});
		return false;
	} else {
		if (!validate.validateEmail(value.userEmail)) {
			uni.showToast({
				title: '店长邮箱格式不正确',
				icon: 'none',
				duration: 3000
			});
			return false;
		}
		let userEmailValidateResult = await validateUserEmail(value.userEmail, userInfo.tenantId)
		if (!userEmailValidateResult) {
			uni.showToast({
				title: '输入的店长邮箱已经存在，请更换',
				icon: 'none',
				duration: 3000
			});
			return false;
		}
	}
	if (!value.userNickname) {
		uni.showToast({
			title: '请输入店长昵称',
			icon: 'none',
			duration: 3000
		});
		return false;
	}

	if (!value.idCardFrontUrl) {
		uni.showToast({
			title: '请上传身份证正面照片',
			icon: 'none',
			duration: 3000
		});
		return false;
	}

	if (!value.idCardBackUrl) {
		uni.showToast({
			title: '请上传身份证背面照片',
			icon: 'none',
			duration: 3000
		});
		return false;
	}

	return true
}

 //验证店铺名称
function validateName(name, tenantId){
	return new Promise((resolve, reject) => {
		api.shopApplyShopInfoValidate({
			tenantId: tenantId,
			name: name
		}).then(res => {
			let data = res.data
			if (data > 0) {
				resolve(false)
			} else {
				resolve(true)
			}
		})
	})
}

 //验证登录账号的 登录账号名
function validateUserName(value){
	return new Promise((resolve, reject) => {
		api.shopApplyLoginUserValidate({
			username: value
		}).then(res => {
			let data = res.data
			if (data > 0) {
				resolve(false)
			} else {
				resolve(true)
			}
		})
	})
}
 //验证登录账号的手机号码
function validateUserPhone(value){
	return new Promise((resolve, reject) => {
		api.shopApplyLoginUserValidate({
			phone: value
		}).then(res => {
			let data = res.data
			if (data > 0) {
				resolve(false)
			} else {
				resolve(true)
			}
		})
	})
}
 //验证登录账号的 邮箱
function validateUserEmail(value){
	return new Promise((resolve, reject) => {
		api.shopApplyLoginUserValidate({
			email: value
		}).then(res => {
			let data = res.data
			if (data > 0) {
				resolve(false)
			} else {
				resolve(true)
			}
		})
	})
}
export default {
	validateShopApplyForm
}