<!--
  - Copyright (C) 2020-2023
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view>
		<cu-custom :bgColor="showDefaultPage||!pageDivData.pageComponent.backgroundColor?
			'bg-'+theme.backgroundColor:pageDivData.pageComponent.backgroundColor"
			:textColor="pageDivData.pageComponent.textColor" :isBack="true">
			<block slot="backText">返回</block>
			<block slot="content"><view class="cu-tag bg-white light sm radius margin-right-xs" v-if="shopInfo.saleType == 2"> 自营 </view>{{shopInfo.name}}</block>
		</cu-custom>
		<view :class="'cu-modal ' + (shopInfo.enable=='0'?'show':'')">
			<view class="cu-dialog">
				<view class="cu-bar bg-white justify-end">
					<view class="content">提示</view>
				</view>
				<view class="padding-xl">抱歉，该店铺不存在或已下架</view>
			</view>
		</view>
		<view v-if="showDefaultPage" class="cu-bar search top-home fixed shop-card"
			:class="'bg-'+theme.backgroundColor">
			<view class="search-form round">
				<text class="cuIcon-search"></text>
				<navigator class="response" hover-class="none" :url="'/extraJumpPages/base/search/index?shopId='+id">
					<input type="text" placeholder="请输入关键字"></input>
				</navigator>
			</view>
			<view class="collection round margin-right-sm text-sm text-center" @tap="userCollect">
				<text
					:class="'cuIcon-' + (shopInfo.collectId ? 'likefill text-white' : 'like text-white')"></text>{{shopInfo.collectId ? '已收藏' : '收藏'}}
			</view>
		</view>
		<!--店铺信息-->
		<view class="fixed">
			<view v-if="showDefaultPage" class="cu-card no-card article shop-information">
				<view class="cu-item">
					<view class="content shop-detail" :class="'bg-'+theme.backgroundColor">
						<image :src="shopInfo.imgUrl" mode="scaleToFill" class="margin-top-xs bg-white"></image>
						<view class="shop-text" style="width: 80%;">
							<view class="cuIcon-locationfill overflow-1 location margin-top-xs"><text
									class="address text-sm margin-left-xs">{{shopInfo.address}}</text></view>
							<view class="flex collect overflow-1 margin-top-xs" :hover-stop-propagation="true"
								@click="callPhone(shopInfo.phone)">
								<text class="cuIcon-mobilefill"></text><text
									class="phone text-sm margin-left-xs">{{shopInfo.phone}}</text>
							</view>
							<view class="flex justify-between align-center margin-top-xs" v-if="shopInfo">
								<text class="margin-left-xs text-sm">{{shopInfo.collectCount}} 人已收藏</text>
								<view class="shop-share flex align-center justify-between padding-right">
									<navigator :url="'/extraJumpPages/shop-store/index?shopId='+shopInfo.id"
										class="margin-lr flex align-center">
										<view class="cuIcon-shopfill text-sm margin-right-xs"></view>
										<view class="text-sm">查看门店</view>
									</navigator>
									<view class="flex align-center" @tap="shareShowFun">
										<view class="cuIcon-share text-sm margin-right-xs"></view>
										<view class="text-sm">分享</view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 首页 -->
			<view v-show="PageCur=='1'" class="margin-bottom-bar padding-bottom-xs">
				<view style="background-color: #f7f7f7;" v-if="!showDefaultPage">
					<!-- <view v-if="false"> -->
					<!-- 自定义页面组件 -->
					<block v-for="(temp, index) in pageDivData.pageComponent.componentsList" :key="index">
						<template v-if="temp.componentName === 'imageComponent'">
							<view>
								<div-image v-model="temp.data"></div-image>
							</view>
						</template>
						<template v-else-if="temp.componentName === 'swiperComponent'">
							<view>
								<div-swiper v-model="temp.data"></div-swiper>
							</view>
						</template>
						<template v-else-if="temp.componentName === 'navButtonComponent'">
							<view>
								<div-nav-button v-model="temp.data"></div-nav-button>
							</view>
						</template>
						<template v-else-if="temp.componentName === 'noticeComponent'">
							<view>
								<div-notice v-model="temp.data"></div-notice>
							</view>
						</template>
						<template v-else-if="temp.componentName === 'titleTextComponent'">
							<view>
								<div-title-text v-model="temp.data"></div-title-text>
							</view>
						</template>
						<template v-else-if="temp.componentName === 'goodsComponent'">
							<view>
								<div-goods v-model="temp.data"></div-goods>
							</view>
						</template>
						<template v-else-if="temp.componentName === 'goodsRowComponent'">
							<view>
								<div-goods-row @onMoreShopGoods="NavChangeCur2()" v-model="temp.data"></div-goods-row>
							</view>
						</template>
						<template v-else-if="temp.componentName === 'shopComponent'">
							<view>
								<div-shop v-model="temp.data"></div-shop>
							</view>
						</template>
						<template v-else-if="temp.componentName === 'goodsCategoryComponent'">
							<view>
								<div-goods-category v-model="temp.data"></div-goods-category>
							</view>
						</template>
						<template v-else-if="temp.componentName === 'couponComponent'">
							<view>
								<div-coupon v-model="temp.data" :shopId="id"></div-coupon>
							</view>
						</template>
						<template v-else-if="temp.componentName === 'bargainComponent'">
							<view>
								<div-bargain v-model="temp.data" :shopId="id"></div-bargain>
							</view>
						</template>
						<template v-else-if="temp.componentName === 'grouponComponent'">
							<view>
								<div-groupon v-model="temp.data" :shopId="id"></div-groupon>
							</view>
						</template>
						<template v-else-if="temp.componentName === 'seckillComponent'">
							<view>
								<div-seckill v-model="temp.data" :shopId="id"></div-seckill>
							</view>
						</template>
						<template v-else-if="temp.componentName === 'searchComponent'">
							<view>
								<div-search v-model="temp.data" :shopId="id" :collectId="shopInfo.collectId"
									@userCollect="userCollect"></div-search>
							</view>
						</template>
						<template v-else-if="temp.componentName === 'shopInfoComponent'">
							<view>
								<div-shop-info @shareShowFun="shareShowFun" v-model="temp.data"
									:shopId="id"></div-shop-info>
							</view>
						</template>
						<template v-else-if="temp.componentName === 'goodsNewComponent'">
							<view class="padding-lr-xs">
								<div-goods-new v-model="temp.data"></div-goods-new>
							</view>
						</template>
						<template v-else-if="temp.componentName === 'imageMultiComponent'">
							<view>
								<div-image-multi v-model="temp.data"></div-image-multi>
							</view>
						</template>
						<template v-else-if="temp.componentName === 'imageHotComponent'">
							<view>
								<div-image-hot v-model="temp.data"></div-image-hot>
							</view>
						</template>
					</block>

					<view :class="'cu-load bg-gray ' + (pageDivLoadmore?'loading':'over')"></view>
				</view>
				<view v-if="showDefaultPage">
					<!-- 默认的显示布局 -->
					<!-- 领取优惠券/秒杀/拼团/砍价 -->
					<view class="left-item bg-white padding-xs padding-top-sm">
						<view class="flex align-center">
							<navigator class="radius econds-kill" hover-class="none"
								:url="'/pageA/seckill/seckill-list/index?shopId='+id">
								<image
									src="https://joolun-base-test.oss-cn-zhangjiakou.aliyuncs.com/1/material/ed8ba4eb-7b32-4a5b-a635-c977d6127ffc.png">
								</image>
							</navigator>
							<navigator class="radius margin-left-xs spellGroup" hover-class="none"
								:url="'/pageA/groupon/groupon-list/index?shopId='+id">
								<image
									src="https://joolun-base-test.oss-cn-zhangjiakou.aliyuncs.com/1/material/1c6183f4-16de-4047-bd99-f7087949775a.png">
								</image>
							</navigator>
						</view>
						<view class="flex align-center margin-top-xs">
							<navigator class="radius bargain" hover-class="none"
								:url="'/pageA/bargain/bargain-list/index?shopId='+id">
								<image
									src="https://joolun-base-test.oss-cn-zhangjiakou.aliyuncs.com/1/material/edb0009e-2f8a-4fcc-82d1-05060913103d.png">
								</image>
							</navigator>
							<navigator class="radius margin-left-xs coupons" @tap="showModalCoupon">
								<image
									src="https://joolun-base-test.oss-cn-zhangjiakou.aliyuncs.com/1/material/9556be37-c6a7-4cb6-ad49-bb25f5635241.png">
								</image>
							</navigator>
						</view>
					</view>

					<view class="cu-bar bg-white justify-center">
						<view class="action text-bold" :class="'text-'+theme.themeColor">
							<text class="cuIcon-move"></text> <text class="cuIcon-like"></text>热卖中<text
								class="cuIcon-move"></text>
						</view>
					</view>
					<goods-card :goodsList="goodsListHot"></goods-card>

					<coupon-receive :couponInfoList="couponInfoList" :modalCoupon="modalCoupon"
						@changeModalCoupon="modalCoupon=$event"
						@receiveCouponChange="receiveCouponChange($event)"></coupon-receive>
				</view>
			</view>

			<!-- 全部商品 -->
			<view v-show="PageCur=='2'" class="margin-bottom-bar padding-bottom-xs">
				<view class="cu-bar justify-center bg-white shop-card">
					<view class="grid response text-center align-start">
						<view class="flex-sub padding-sm margin-xs radius">
							<view class="grid text-center" @tap="sortHandle" data-type="price">价格<view
									class="margin-left-xs">
									<view
										:class="'cuIcon-triangleupfill ' + (price=='asc' ? 'text-'+theme.themeColor : '')"
										data-type="price"></view>
									<view class="basis-df"></view>
									<view
										:class="'cuIcon-triangledownfill ' + (price=='desc' ? 'text-'+theme.themeColor : '')"
										data-type="price"></view>
								</view>
							</view>
						</view>
						<view class="flex-sub padding-sm margin-xs radius">
							<view class="grid text-center" @tap="sortHandle" data-type="sales">销量<view
									class="margin-left-xs">
									<view
										:class="'cuIcon-triangleupfill ' + (sales=='asc' ? 'text-'+theme.themeColor : '')"
										data-type="sales"></view>
									<view class="basis-df"></view>
									<view
										:class="'cuIcon-triangledownfill ' + (sales=='desc' ? 'text-'+theme.themeColor : '')"
										data-type="sales"></view>
								</view>
							</view>
						</view>
						<view class="flex-sub padding-sm margin-xs radius">
							<view :class="createTime=='desc' ? 'text-bold text-'+theme.themeColor : ''"
								@tap="sortHandle" data-type="createTime">新上架</view>
						</view>
					</view>
					<view class="action">
						<view class="text-xxl">
							<text :class="'cuIcon-' + (viewType ? 'list' : 'cascades')" @tap="viewTypeEdit"></text>
						</view>
					</view>
				</view>
				<view v-if="viewType" class="padding-xs">
					<goods-card :goodsList="goodsList"></goods-card>
				</view>
				<view v-if="!viewType" class="padding-xs bg-white">
					<goods-row :goodsList="goodsList"></goods-row>
				</view>
				<view :class="'cu-load bg-gray ' + (loadmore?'loading':'over')"></view>
			</view>
		</view>

		<!-- 分类 -->
		<view v-if="PageCur=='3'" class="margin-bottom-bar padding-sm">
			<view class="cu-card bg-white padding margin-bottom-sm radius" v-for="(item,index) in goodsCategoryShop"
				:key="index">
				<navigator hover-class="none"
					:url="'/pages/goods/goods-list/index?shopId='+id+'&categoryShopFirst='+item.id"
					class="flex justify-between">
					<view><text class="cuIcon-titles margin-left-xs" :class="'text-'+theme.themeColor"></text>
						<text class="text-bold text-black">{{item.name}}</text>
					</view>
					<view class="cuIcon-right"></view>
				</navigator>
				<view class="content margin-top-xs">
					<view class="grid margin-right-xs">
						<navigator hover-class="none"
							:url="'/pages/goods/goods-list/index?shopId='+id+'&categoryShopSecond='+item2.id"
							class="cu-item bg-gray margin-left-sm margin-top-xs padding-sm radius text-sm"
							style="width: 46%;" v-for="(item2,index) in item.children">{{item2.name}}</navigator>
					</view>
				</view>
			</view>
		</view>

		<view class="cu-bar tabbar bg-white shadow foot">
			<view class="action" @click="NavChange" data-cur="1"
				:class="PageCur=='1'?'text-'+theme.themeColor:'text-gray'"
				:style="[{color: PageCur=='1'? pageDivData.pageComponent.tabTextColor : ''}]">
				<view class='cuIcon-cu-image'>
					<text class="cuIcon-shop lg"></text>
				</view>
				<view>首页</view>
			</view>
			<view class="action" @click="NavChange" data-cur="2"
				:class="PageCur=='2'?'text-'+theme.themeColor:'text-gray'"
				:style="[{color: PageCur=='2'? pageDivData.pageComponent.tabTextColor : ''}]">
				<view class='cuIcon-cu-image'>
					<text class="cuIcon-goods lg"></text>
				</view>
				<view>全部商品</view>
			</view>
			<view class="action" @click="NavChange" data-cur="3"
				:class="PageCur=='3'?'text-'+theme.themeColor:'text-gray'"
				:style="[{color: PageCur=='3'? pageDivData.pageComponent.tabTextColor : ''}]">
				<view class='cuIcon-cu-image'>
					<text class="cuIcon-sort lg"></text>
				</view>
				<view>分类</view>
			</view>
			<view class="action" @click="NavChange" data-cur="4">
				<view class='cuIcon-cu-image'>
					<text class="cuIcon-service lg"></text>
				</view>
				<view :class="PageCur=='4'?'text-'+theme.themeColor:'text-gray'">客服</view>
			</view>
		</view>

		<!-- 分享组件 -->
		<share-component v-model="showShare" :shareParams="shareParams"></share-component>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2020-2022
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	import util from '@/utils/util'
	const app = getApp();
	import api from 'utils/api'
	import goodsCard from "components/goods-card/index";
	import goodsRow from "components/goods-row/index";
	import couponReceive from "components/coupon-receive/index";
	import shareComponent from "@/components/share-component/index"

	import divImage from "@/components/div-components-shop/div-image/div-image.vue";
	import divSwiper from "@/components/div-components-shop/div-swiper/div-swiper.vue";
	import divNavButton from "@/components/div-components-shop/div-nav-button/div-nav-button.vue";
	import divNotice from "@/components/div-components-shop/div-notice/div-notice.vue";
	import divTitleText from "@/components/div-components-shop/div-title-text/div-title-text.vue";
	import divGoods from "@/components/div-components-shop/div-goods/div-goods.vue";
	import divGoodsRow from "@/components/div-components-shop/div-goods-row/div-goods-row.vue";
	import divCoupon from "@/components/div-components-shop/div-coupon/div-coupon.vue";
	import divBargain from "@/components/div-components-shop/div-bargain/div-bargain.vue";
	import divGroupon from "@/components/div-components-shop/div-grouponinfo/div-grouponinfo.vue";
	import divSeckill from "@/components/div-components-shop/div-seckill/div-seckill.vue";
	import divSearch from "@/components/div-components-shop/div-search/div-search.vue";
	import divShopInfo from "@/components/div-components-shop/div-shopinfo/div-shopinfo.vue";
	import divGoodsNew from "@/components/div-components-shop/div-goods-new/div-goods-new";
	import divImageMulti from "@/components/div-components-shop/div-image-multi/div-image-multi.vue";
	import divImageHot from "@/components/div-components-shop/div-image-hot/div-image-hot.vue";


	const {
		base64src
	} = require("utils/base64src.js");

	export default {
		components: {
			goodsCard,
			goodsRow,
			couponReceive,
			shareComponent,
			divImage,
			divSwiper,
			divNavButton,
			divNotice,
			divTitleText,
			divGoods,
			divGoodsRow,
			divCoupon,
			divBargain,
			divGroupon,
			divSeckill,
			divSearch,
			divShopInfo,
			divGoodsNew,
			divImageMulti,
			divImageHot,
		},
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				showDefaultPage: false,
				pageDivLoadmore: true,
				pageDivData: {
					pageComponent: {
						componentsList: []
					}
				}, //首页自定义配置组件的数据
				PageCur: '1',
				id: '',
				shopInfo: {
					collectCount: 0
				},
				page: {
					searchCount: false,
					current: 1,
					size: 10,
					ascs: '',
					//升序字段
					descs: ''
				},
				parameter: {},
				loadmore: true,
				goodsList: [],
				viewType: true,
				price: '',
				sales: '',
				createTime: '',
				title: '',
				couponInfoList: [],
				modalCoupon: false,
				posterUrl: "",
				posterShow: false,
				posterConfig: "",
				shareShow: '',
				curLocalUrl: '',
				goodsCategoryShop: [],
				goodsListHot: [],
				showShare: false,
				shareParams: {}
			};
		},

		props: {},

		onLoad(options) {
			// 保存别人分享来的 userCode
			util.saveSharerUserCode(options);
			let id;
			if (options.scene) {
				//接受二维码中参数
				let scenes = decodeURIComponent(options.scene).split('&');
				id = scenes[0];
			} else {
				id = options.id;
			}
			this.id = id
			this.parameter.shopId = id
			app.initPage().then(res => {
				this.shopInfoGet();
				this.loadDataDivPage();
				this.couponInfoPage(id);
				this.goodsHot();
			});
		},
		onShareAppMessage: function() {
			const userInfo = uni.getStorageSync('user_info')
			let userCode = userInfo ? '&sharer_user_code=' + userInfo.userCode : ''
			let path = 'pages/shop/shop-detail/index?id=' + this.shopInfo.id + userCode;
			return {
				title: this.shopInfo.name,
				path: path,
				imageUrl: this.shopInfo.imgUrl,
				success: function(res) {
					if (res.errMsg == 'shareAppMessage:ok') {
						console.log(res.errMsg);
					}
				},
				fail: function(res) { // 转发失败
				}
			};
		},
		onReachBottom() {
			if (this.PageCur == '2' && this.loadmore) {
				this.page.current = this.page.current + 1;
				this.goodsPage();
			}
		},
		methods: {
			loadDataDivPage() {
				this.pageDivLoadmore = true;
				api.pagedeviseShop(this.parameter.shopId).then(res => {
					let pageDivData = res.data;
					if (pageDivData) {
						this.pageDivData = pageDivData;
						if (!pageDivData || !pageDivData.pageComponent || pageDivData.pageComponent.componentsList
							.length == 0) {
							// 如果没有设置自定义页面数据，那就显示默认原始页面
							this.getDefaultPageData();
						} else {
							this.showDefaultPage = false;
							this.pageDivLoadmore = false;
						}
					} else {
						// 如果没有设置自定义页面数据，那就显示默认原始页面
						// this.shopInfoGet();
						this.getDefaultPageData();
					}
				});
			},
			getDefaultPageData() {
				this.showDefaultPage = true;
			},
			shopInfoGet() {
				api.shopInfoGet(this.parameter.shopId).then(res => {
					let shopInfo = res.data;
					this.shopInfo = shopInfo
				});
			},
			NavChange: function(e) {
				let cur = e.currentTarget.dataset.cur
				if (cur == '4') {
					uni.navigateTo({
						url: '/pages/customer-service/customer-service-list/index?shopId=' + this.id
					});
				} else {
					this.PageCur = cur
				}
				if (cur == '2' && this.goodsList.length <= 0) {
					this.goodsPage();
				}
				if (cur == '3' && this.goodsCategoryShop.length <= 0) {
					this.goodsCategoryShopTree();
				}
			},
			NavChangeCur2() {
				this.PageCur = 2
				if (this.goodsList.length <= 0) {
					this.goodsPage();
				}
			},
			//热销单品
			goodsHot() {
				api.goodsPage({
					searchCount: false,
					current: 1,
					size: 6,
					descs: 'sale_num',
					shopId: this.id
				}).then(res => {
					this.goodsListHot = res.data.records;
				});
			},
			goodsPage() {
				api.goodsPage(Object.assign({}, this.page, util.filterForm(this.parameter))).then(res => {
					let goodsList = res.data.records;
					this.goodsList = [...this.goodsList, ...goodsList];
					if (goodsList.length < this.page.size) {
						this.loadmore = false;
					}
				});
			},
			//商品分类
			goodsCategoryShopTree() {
				api.goodsCategoryShopTree({
					shopId: this.id
				}).then(res => {
					this.goodsCategoryShop = res.data
				});
			},
			//查询店铺可用电子券
			couponInfoPage(shopId) {
				api.couponInfoPage({
					current: 1,
					size: 50,
					descs: 'create_time',
					shopId: shopId
				}).then(res => {
					this.couponInfoList = res.data.records;
				});
			},

			showModalCoupon() {
				this.modalCoupon = true;
			},

			hideModalCoupon() {
				this.modalCoupon = false;
			},

			viewTypeEdit() {
				this.viewType = !this.viewType;
			},

			sortHandle(e) {
				let type = e.target.dataset.type;

				switch (type) {
					case 'price':
						if (this.price == '') {
							this.price = 'asc';
							this.page.descs = '';
							this.page.ascs = 'price_down';
						} else if (this.price == 'asc') {

							this.price = 'desc';
							this.page.descs = 'price_down';
							this.page.ascs = '';
						} else if (this.price == 'desc') {
							this.price = '';
							this.page.descs = '';
							this.page.ascs = '';
						}
						this.sales = '';
						this.createTime = '';
						break;

					case 'sales':
						if (this.sales == '') {
							this.sales = 'desc';
							this.page.descs = 'sale_num';
							this.page.ascs = '';
						} else if (this.sales == 'desc') {
							this.sales = 'asc';
							this.page.descs = '';
							this.page.ascs = 'sale_num';

						} else if (this.sales == 'asc') {
							this.sales = '';
							this.page.descs = '';
							this.page.ascs = '';
						}
						this.price = '';
						this.createTime = '';
						break;

					case 'createTime':
						if (this.createTime == '') {
							this.createTime = 'desc';
							this.page.descs = 'create_time';
							this.page.ascs = '';
						} else if (this.createTime == 'desc') {
							this.createTime = '';
							this.page.descs = '';
							this.page.ascs = '';
						}
						this.price = '';
						this.sales = '';
						break;
				}

				this.relod();
			},

			receiveCouponChange(obj) { //更新单条数据
				this.couponInfoList[obj.index] = obj.item;
				this.couponInfoList.splice(); //确保页面刷新成功
			},

			relod() {
				this.loadmore = true;
				this.goodsList = [];
				this.page.current = 1;
				this.goodsPage();
			},

			callPhone(phone) {
				uni.makePhoneCall({
					phoneNumber: phone
				});
			},
			//收藏
			userCollect() {
				let shopInfo = this.shopInfo;
				let collectId = shopInfo.collectId;

				if (collectId) {
					api.userCollectDel(collectId).then(res => {
						uni.showToast({
							title: '已取消收藏',
							icon: 'success',
							duration: 2000
						});
						shopInfo.collectId = null;
						shopInfo.collectCount = shopInfo.collectCount - 1
						this.shopInfo = shopInfo;
					});
				} else {
					api.userCollectAdd({
						type: '2',
						relationIds: [shopInfo.id]
					}).then(res => {
						uni.showToast({
							title: '收藏成功',
							icon: 'success',
							duration: 2000
						});
						shopInfo.collectId = res.data[0].id;
						shopInfo.collectCount = shopInfo.collectCount + 1
						this.shopInfo = shopInfo;
					});
				}
			},
			shareShowFun() {
				// 分享海报需要配置的参数
				let desc = '长按识别小程序码';
				let shareImg = this.shopInfo.imgUrl;
				// #ifdef H5 || APP-PLUS
				desc = '长按识别二维码';
				// h5的海报分享的图片有的有跨域问题，所以统一转成base64的
				// 之所以不在组件里面转换是因为无法区分哪张image图片需要处理，一般处理主图
				shareImg = util.imgUrlToBase64(shareImg);
				// #endif
				//海报配置请参考 https://github.com/jasondu/wxa-plugin-canvas
				let posterConfig = {
					width: 750,
					height: 1280,
					backgroundColor: '#fff',
					debug: false,
					blocks: [{
						width: 690,
						height: 808,
						x: 30,
						y: 183,
						borderWidth: 2,
						borderColor: '#f0c2a0',
						borderRadius: 20
					}, {
						width: 634,
						height: 74,
						x: 59,
						y: 770,
						backgroundColor: '#fff',
						opacity: 0.5,
						zIndex: 100
					}],
					texts: [{
						x: 40,
						y: 113,
						baseLine: 'top',
						text: '发现一个好店铺，推荐给你呀',
						fontSize: 38,
						color: '#080808'
					}, {
						x: 92,
						y: 810,
						fontSize: 38,
						baseLine: 'middle',
						text: this.shopInfo.name,
						width: 570,
						lineNum: 1,
						color: '#080808',
						zIndex: 200
					}, {
						x: 82,
						y: 900,
						fontSize: 28,
						baseLine: 'middle',
						text: this.shopInfo.address,
						width: 570,
						lineNum: 1,
						color: '#666666',
						zIndex: 200
					}, {
						x: 82,
						y: 950,
						fontSize: 28,
						baseLine: 'middle',
						text: this.shopInfo.phone,
						width: 570,
						lineNum: 1,
						color: '#666666',
						zIndex: 200
					}, {
						x: 540,
						y: 950,
						fontSize: 28,
						baseLine: 'middle',
						text: this.shopInfo.collectCount + '人已收藏',
						width: 570,
						lineNum: 1,
						color: '#666666',
						zIndex: 200
					}, {
						x: 360,
						y: 1065,
						baseLine: 'top',
						text: desc,
						fontSize: 38,
						color: '#080808'
					}, {
						x: 360,
						y: 1123,
						baseLine: 'top',
						text: '超值好货快来购买',
						fontSize: 28,
						color: '#929292'
					}],
					images: [{
						width: 634,
						height: 634,
						x: 59,
						y: 210,
						url: shareImg
					}, {
						width: 230,
						height: 230,
						x: 92,
						y: 1020,
						url: null,
						qrCodeName: 'qrCodeName', // 二维码、小程序码唯一区分标识
					}]
				};
				let userInfo = uni.getStorageSync('user_info');
				if (userInfo && userInfo.headimgUrl) {
					//如果有头像则显示
					posterConfig.images.push({
						width: 62,
						height: 62,
						x: 40,
						y: 30,
						borderRadius: 62,
						url: userInfo.headimgUrl
					});
					posterConfig.texts.push({
						x: 123,
						y: 61,
						baseLine: 'middle',
						text: userInfo.nickName,
						fontSize: 32,
						color: '#8d8d8d'
					});
				}
				this.shareParams = {
					title: '发现一个好店铺，推荐给你呀',
					desc: this.shopInfo.name,
					imgUrl: this.shopInfo.imgUrl,
					scene: this.shopInfo.id,
					page: 'pages/shop/shop-detail/index',
					posterConfig: posterConfig
				}
				this.showShare = true;
			},
		}
	};
</script>
<style>
	.shop-card {
		box-shadow: unset !important;
	}

	.cuIcon-triangledownfill {
		margin-top: -22rpx
	}

	.top-home {
		top: unset !important;
	}

	.shop-information {
		height: 160rpx;
		margin-top: 100rpx;
	}

	.collection {
		width: 120rpx;
		height: 48rpx;
		line-height: 48rpx;
		border: solid 1rpx #FFFFFF;
	}

	.shop-share {
		margin-right: -30rpx;
	}

	.right-item {
		width: 100%;
		margin-left: -10rpx;
	}

	.location {
		width: 480rpx;
	}

	.shop-detail {
		height: 160rpx;
	}

	.shop-detail image {
		width: 120rpx !important;
		height: 120rpx !important;
	}

	.show-bg {
		height: 84%;
		margin-top: 120rpx;
	}

	.image-box {
		height: 90%;
	}

	.show-btn {
		margin-top: -130rpx;
	}

	.econds-kill {
		width: 40%;
		height: 100rpx;
	}

	.econds-kill image {
		width: 100%;
		height: 100rpx;
	}

	.spellGroup {
		width: 700rpx;
		height: 100rpx;
	}

	.spellGroup image {
		width: 100%;
		height: 100rpx;
	}

	.bargain {
		width: 700rpx;
		height: 100rpx;
	}

	.bargain image {
		width: 100%;
		height: 100rpx;
	}

	.coupons {
		width: 40%;
		height: 100rpx;
	}

	.coupons image {
		width: 100%;
		height: 100rpx;
	}
</style>
