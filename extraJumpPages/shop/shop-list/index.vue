<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true">
			<block slot="backText">返回</block>
			<block slot="content">店铺列表</block>
		</cu-custom>
		<view class="cu-bar search fixed shop-search" :class="'bg-'+theme.backgroundColor">
			<view class="search-form round" @click="goNavigatePage('/extraJumpPages/base/search/index?shopId='+parameter.shopId)" >
				<text class="cuIcon-search"></text>
				<text class="response">
					<text class="text-dm text-gray">{{parameter.name||'请输入关键字'}}</text>
				</text>
			</view>
		</view>
			<view class="justify-center bg-white solid-bottom shop-nav">
				<view v-if="parameter.name" class="bg-white nav">
					<view class="flex text-center">
						<navigator class="cu-item flex-sub" hover-class="none" open-type="redirect" :url="'/pages/goods/goods-list/index?name='+parameter.name" >
							商品
						</navigator>
						<view class="cu-item flex-sub cur" :class="'text-'+theme.themeColor">
							店铺
						</view>
					</view>
				</view>
			</view>
		<view class="cu-card bg-white">
			<view class="bg-white radius shop-card" v-for="(item, index) in shopInfoList" :key="index">
				<navigator class="flex padding-top-sm margin-left-sm justify-between" hover-class="none" :url="'/extraJumpPages/shop/shop-detail/index?id=' + item.id">
					<view class="round flex">
						<image :src="item.imgUrl" class="round head-image"></image>
						<view class="margin-left-sm  text-bold padding-top-xs"><view class="cu-tag bg-red light sm radius margin-right-xs saleType" v-if="item.saleType == 2"> 自营 </view>{{item.name}}</view>
					</view>
					<view class="margin-right-sm">
						<text class="round enter-store" :class="'bg-'+theme.themeColor">进入店铺</text>
					</view>
				</navigator>
				<view class="grid margin-left-sm margin-top-xs">
					<block v-for="(item2, index) in item.listGoodsSpu" :key="index">
						<navigator hover-class="none" :url="'/pages/goods/goods-detail/index?id=' + item2.id" class="goods-item">
							<image class="top radius img-box" :src="item2.picUrls[0]"></image>
							<view style="height: 72rpx;">
								<text class="overflow-2  text-sm">{{item2.name}}</text>
							</view>
							<view class="flex  justify-between align-center">
								<text class="text-gray text-sm">已售{{item2.saleNum}}</text>
								<text class="text-bold text-sm text-red flex ">￥{{item2.priceDown}}</text>
							</view>
						</navigator>
					</block>
					<view v-if="item.listGoodsSpu.length <= 0" class="no-goods">
						<image src="/static/public/img/no-item.png" class="no-item margin-top"></image>
						<view class="text-sm text-gray text-center">店主很懒，还没有上架商品哦～</view>
					</view>
				</view>
			</view>
		</view>
		<view :class="'cu-load bg-white ' + (loadmore?'loading':'over')"></view>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2018-2019
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	const util = require("utils/util.js");
	const app = getApp();
	import api from 'utils/api'

	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				page: {
					searchCount: false,
					current: 1,
					size: 10,
					ascs: '',
					//升序字段
					descs: ''
				},
				parameter: {},
				loadmore: true,
				shopInfoList: []
			};
		},

		//收藏
		userCollect() {
			let shopInfo = this.shopInfo;
			let collectId = shopInfo.collectId;

			if (collectId) {
				api.userCollectDel(collectId).then(res => {
					uni.showToast({
						title: '已取消收藏',
						icon: 'success',
						duration: 2000
					});
					shopInfo.collectId = null;
					shopInfo.collectCount = shopInfo.collectCount - 1
					this.shopInfo = shopInfo;
				});
			} else {
				api.userCollectAdd({
					type: '2',
					relationIds: [shopInfo.id]
				}).then(res => {
					uni.showToast({
						title: '收藏成功',
						icon: 'success',
						duration: 2000
					});
					shopInfo.collectId = res.data[0].id;
					shopInfo.collectCount = shopInfo.collectCount + 1
					this.shopInfo = shopInfo;
				});
			}
		},

		components: {

		},
		props: {},

		onShow() {},

		onLoad: function(options) {
			if (options.name) {
				this.parameter.name = options.name;
			}
			app.initPage().then(res => {
				this.shopInfoPage();
			});
		},

		onReachBottom() {
			if (this.loadmore) {
				this.page.current = this.page.current + 1;
				this.shopInfoPage();
			}
		},

		onPullDownRefresh() {
			// 显示顶部刷新图标
			uni.showNavigationBarLoading();
			this.refresh(); // 隐藏导航栏加载框

			uni.hideNavigationBarLoading(); // 停止下拉动作

			uni.stopPullDownRefresh();
		},

		methods: {
			goNavigatePage(url){
				util.goNavigatePage(url)
			},
			shopInfoPage() {
				api.shopInfoPageWithSpu(Object.assign({}, this.page, util.filterForm(this.parameter))).then(res => {
					let shopInfoList = res.data.records;
					this.shopInfoList = [...this.shopInfoList, ...shopInfoList];
					if (shopInfoList.length < this.page.size) {
						this.loadmore = false;
					}
				});
			},

			refresh() {
				this.loadmore = true;
				this.shopInfoList = [];
				this.page.current = 1;
				this.shopInfoPage();
			}

		}
	};
</script>

<style>
	.shop-search {
		box-shadow:unset !important;
		top: unset !important;
		min-height: 100rpx !important
	}

	.shop-nav {
		top: unset !important;
		margin-top: 100rpx;
	}
	
	.shop-card {
		width: 94%;
		height: 460rpx;
		margin: 20rpx auto;
		box-shadow:0px 5px 10px #e5e5e5;
	}

	.head-image {
		width: 60rpx;
		height: 60rpx;
	}

	.enter-store {
		font-size: 24rpx;
		padding: 10rpx 20rpx 10rpx 20rpx;
		line-height: 50rpx;
	}

	.img-box {
		height: 200rpx;
	}

	.goods-item {
		padding: 10rpx;
		width: 220rpx;
		height: 220rpx;
	}

	.goods-name {
		height: 65rpx;
	}
	
	.no-goods{
		margin: 30rpx auto;
	}
	
	.no-item{
		width: 400rpx;
		height: 200rpx;
	}
	
	.text-dm {
		font-size: 26upx;
	}
</style>
