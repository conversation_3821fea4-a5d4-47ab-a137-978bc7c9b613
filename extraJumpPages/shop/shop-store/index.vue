<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true">
			<block slot="backText">返回</block>
			<block slot="content">门店列表</block>
		</cu-custom>
		<view class="cu-card margin-lr-xs">
			<view class="bg-white radius-lg shop-store-card margin-tb-sm padding" v-for="(item, index) in shopStoreList"
				:key="index">
				<view class="flex align-center">
					<image :src="item.imgUrl" class="round head-image"></image>
					<view class="margin-left-sm text-bold text-lg">{{item.name}}</view>
				</view>
				<view class="margin-top-sm text-gray">
					<view @click="goShopStore(item)">{{item.address}}</view>
					<view class="flex align-center justify-between margin-top-sm">
						<view class="flex align-center">
							<image src="../../../static/public/img/phone.png" class="item-img"></image>
							<view class="text-gray margin-left-xs">{{item.phone}}</view>
						</view>
						<view class="flex align-center">
							<image src="../../../static/public/img/createTime.png" class="item-img"></image>
							<view class="text-gray margin-left-xs">
								{{item.businessHours?item.businessHours.split(',').join('-'):'-'}}</view>
						</view>
					</view>
					<view @click="goShopStore(item)" class="margin-top-sm flex align-center">
						<view class="cuIcon-locationfill margin-right-xs"></view>
						<view class="text-left">
							{{item.address}}
							<text v-if="item.juli">(距你{{item.juli | formatDistance}})</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view :class="'cu-load ' + (loadmore?'loading':'over')"></view>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2018-2019
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */

	import util from "@/utils/util.js";
	import api from '@/utils/api'

	export default {
		filters: {
			formatDistance(value) {
				if (!value) return ''
				if (value < 1000) {
					return value + 'm'
				} else {
					return (value / 1000).toFixed(2) + 'km'
				}
			}
		},
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: getApp().globalData.theme, //全局颜色变量
				page: {
					searchCount: false,
					current: 1,
					size: 10,
					ascs: 'juli',
					//升序字段
					descs: ''
				},
				parameter: {},
				loadmore: true,
				loadmoreTimeOut: 3, // 3秒后自动获取
				shopStoreList: []
			};
		},
		onLoad: function(options) {
			if (options.shopId) {
				this.parameter.shopId = options.shopId;
			}
			uni.showLoading({
				title: '正在获取门店信息'
			})
			// 根据位置获取门店列表
			this.getLocation()
		},
		onShow() {},
		onReachBottom() {
			if (this.loadmore) {
				this.page.current = this.page.current + 1;
				this.shopStorePage();
			}
		},
		onPullDownRefresh() {
			// 显示顶部刷新图标
			uni.showNavigationBarLoading();
			this.refresh(); // 隐藏导航栏加载框
			uni.hideNavigationBarLoading(); // 停止下拉动作
			uni.stopPullDownRefresh();
		},
		methods: {
			getLocation() {
				let that = this
				uni.getLocation({
					isHighAccuracy: true, //高精度定位
					success: function(res) {
						that.parameter.longitude = res.longitude
						that.parameter.latitude = res.latitude
						console.log('当前位置success：', res);
						// console.log('当前位置的纬度：' + res.latitude);
					},
					complete: function(res) {
						console.log('当前位置complete：', res);
						getApp().initPage().then(res => {
							that.shopStoreList = []
							that.shopStorePage();
						});
					},
					fail: function(res) {
						console.log('当前位置fail：', res);
						uni.showToast({
							icon: "none",
							title: "获取位置信息失败，请检查是否开启手机定位或应用权限！"
						})
					}
				});
				// 在较新的浏览器上，H5 端获取定位信息，要求部署在 https 服务上，本地预览（localhost）仍然可以使用 http 协议。
				// 国产安卓手机上，H5若无法定位，检查手机是否开通位置服务、GPS，ROM是否给该浏览器位置权限、浏览器是否对网页弹出请求给予定位的询问框。
			},
			goNavigatePage(url) {
				util.goNavigatePage(url)
			},
			shopStorePage() {
				if (this.parameter.shopId) {
					api.shopStorePage(Object.assign({}, this.page, this.parameter)).then(res => {
						let shopStoreList = res.data.records;
						this.shopStoreList = [...this.shopStoreList, ...shopStoreList];
						if (shopStoreList.length < this.page.size) {
							this.loadmore = false;
						}
						uni.hideLoading()
					});
				} else {
					this.loadmore = false;
					uni.hideLoading()
				}
			},
			refresh() {
				this.loadmore = true;
				this.shopStoreList = [];
				this.page.current = 1;
				this.shopStorePage();
			},
			goShopStore(item) {
				// if(!item)return;
				// 需要配置地图key ，key要收费。。
				// https://uniapp.dcloud.net.cn/api/location/open-location.html#openlocation
				// uni.openLocation({
				// 	latitude: Number(item.latitude),
				// 	longitude: Number(item.longitude),
				// 	success: function () {
				// 		console.log('success');
				// 	},
				// 	fail: (res)=>{
				// 		console.log('fail',res);
				// 	}
				// });
			}
		}
	};
</script>

<style>
	.item-img {
		opacity: 0.5;
		width: 42rpx;
		height: 42rpx;
	}

	.head-image {
		width: 48rpx;
		height: 48rpx;
	}
</style>