<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true">
			<block slot="backText">返回</block>
			<block slot="content">签到积分明细</block>
		</cu-custom>
		<view class="text-center padding points" :class="'bg-'+theme.backgroundColor">
			<view class="text-xl">
				<text class="cuIcon-medalfill">当前 <text class="text-bold">{{userInfo.pointsCurrent}}</text> 个积分</text>
			</view>
			<view class="margin-top-sm">
				<text style="font-weight: 300;">通过签到获取积分记录</text>
			</view>
		</view>
		<view class="cu-list menu">
			<view class="cu-item" v-for="(item, index) in pointsRecord" :key="index">
				<view class="content padding-tb-sm">
					<view class="text-sm padding-right-sm">{{item.description}}</view>
					<view class="text-gray text-sm">{{item.createTime}}</view>
				</view>
				<view class="action">
					<text class="text-bold text-green margin-right-xs" v-if="item.amount > 0">+{{item.amount}}</text>
					<text class="text-bold text-red margin-right-xs" v-if="item.amount < 0">{{item.amount}}</text>
				</view>
			</view>
		</view>
		<view :class="'cu-load bg-gray ' + (loadmore?'loading':'over')"></view>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2018-2019
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	const util = require("utils/util.js");
	const app = getApp();
	import api from 'utils/api'

	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				page: {
					searchCount: false,
					current: 1,
					size: 15,
					ascs: '',
					//升序字段
					descs: 'create_time'
				},
				parameter: {
					recordType: '7'
				},
				loadmore: true,
				pointsRecord: [],
				userInfo: {
					pointsCurrent: ''
				}
			};
		},

		components: {},
		props: {},

		onLoad(options) {
			// 保存别人分享来的 userCode
			util.saveSharerUserCode(options);
			app.initPage().then(res => {
				this.userInfoGet();
				this.pointsRecordPage();
			});
		},

		onShow(options) {},

		onReachBottom() {
			if (this.loadmore) {
				this.page.current = this.page.current + 1;
				this.pointsRecordPage();
			}
		},

		methods: {
			//获取商城用户信息
			userInfoGet() {
				api.userInfoGet().then(res => {
					this.userInfo = res.data
				});
			},

			pointsRecordPage() {
				api.pointsRecordPage(Object.assign({}, this.page, util.filterForm(this.parameter))).then(res => {
					let pointsRecord = res.data.records;
					this.pointsRecord = [...this.pointsRecord, ...pointsRecord];
					if (pointsRecord.length < this.page.size) {
						this.loadmore = false;
					}
				});
			}
		}
	};
</script>

<style>
	
</style>
