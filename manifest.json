{
    "name" : "Jo<PERSON><PERSON>un-plus商城",
    "appid" : "__UNI__94938C3",
    "description" : "",
    "versionName" : "2.0.5",
    "versionCode" : 20500,
    "transformPx" : false,
    "app-plus" : {
        "usingComponents" : true,
        "nvueCompiler" : "uni-app",
        "compilerVersion" : 3,
        "safearea" : {
            "top" : {
                "offset" : "auto"
            },
            "bottom" : {
                "offset" : "auto"
            }
        },
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        "modules" : {
            "Payment" : {},
            "Push" : {},
            "Share" : {},
            "VideoPlayer" : {},
            "Camera" : {},
            "Geolocation" : {}
        },
        "distribute" : {
            "android" : {
                "permissionPhoneState" : {
                    "request" : "none",
                    "prompt" : "为保证您正常、安全地使用，需要获取设备识别码（部分手机提示为获取手机号码）使用权限，请允许。"
                },
                "permissions" : [
                    "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ],
                "abiFilters" : [ "armeabi-v7a", "arm64-v8a" ]
            },
            "ios" : {
                "privacyDescription" : {
                    "NSLocalNetworkUsageDescription" : "获取商品相关的数据内容",
                    "NSUserTrackingUsageDescription" : "我们使用的第三方库(极光推送)需要跟踪使用情况,便于购物时买卖双方的即时交流沟通。我们个人不跟踪用户的活动。",
                    "NSPhotoLibraryUsageDescription" : "用户修改头像或商品评价",
                    "NSPhotoLibraryAddUsageDescription" : "用户修改头像或商品评价",
                    "NSCameraUsageDescription" : "用户修改头像或商品评价",
                    "NSLocationAlwaysAndWhenInUseUsageDescription" : "根据当前位置显示附近的门店"
                },
                "dSYMs" : false
            },
            "sdkConfigs" : {
                "ad" : {},
                "share" : {
                    "weixin" : {
                        "appid" : "wxc85c48c1dd42655a",
                        "UniversalLinks" : "https://www.joolun.com/"
                    }
                },
                "push" : {},
                "oauth" : {},
                "payment" : {
                    "weixin" : {
                        "__platform__" : [ "ios", "android" ],
                        "appid" : "wxc85c48c1dd42655a",
                        "UniversalLinks" : "https://www.joolun.com/"
                    },
                    "alipay" : {
                        "__platform__" : [ "ios", "android" ]
                    }
                },
                "geolocation" : {
                    "system" : {
                        "__platform__" : [ "ios", "android" ]
                    }
                }
            },
            "icons" : {
                "android" : {
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png"
                    }
                }
            }
        },
        "uniStatistics" : {
            "enable" : true,
            "debug " : true
        }
    },
    "quickapp" : {},
    "mp-weixin" : {
        "appid" : "wx215cbd9581f5449d",
        "setting" : {
            "urlCheck" : false,
            "minified" : true,
            "es6" : false
        },
        "usingComponents" : true,
        "permission" : {
            "scope.userLocation" : {
                "desc" : "你的位置信息将用于小程序位置接口的效果展示"
            }
        },
        "requiredPrivateInfos" : [ "getLocation", "chooseLocation", "chooseAddress" ],
        "uniStatistics" : {
            "enable" : true
        }
    },
    "mp-alipay" : {
        "usingComponents" : true,
        "uniStatistics" : {
            "enable" : true
        }
    },
    "mp-baidu" : {
        "usingComponents" : true,
        "uniStatistics" : {
            "enable" : true
        }
    },
    "mp-toutiao" : {
        "usingComponents" : true,
        "uniStatistics" : {
            "enable" : true
        }
    },
    "h5" : {
        "devServer" : {
            "disableHostCheck" : true,
            "port" : 8085,
            //转发代理
            "proxy" : {
                "/mall" : {
                    "target" : "http://localhost:8082"
                },
                "/upms" : {
                    "target" : "http://localhost:8082"
                }
            }
        },
        "router" : {
            "mode" : "history"
        },
        "title" : "JooLun多店版",
        "domain" : "joolun.com",
        "uniStatistics" : {
            "enable" : true
        }
    },
    "uniStatistics" : {
        "version" : "2",
        "enable" : true
    },
    "mp-jd" : {
        "uniStatistics" : {
            "enable" : true
        }
    },
    "mp-kuaishou" : {
        "uniStatistics" : {
            "enable" : true
        }
    },
    "mp-lark" : {
        "uniStatistics" : {
            "enable" : true
        }
    },
    "mp-qq" : {
        "uniStatistics" : {
            "enable" : true
        }
    },
    "quickapp-webview-huawei" : {
        "uniStatistics" : {
            "enable" : true
        }
    },
    "quickapp-webview-union" : {
        "uniStatistics" : {
            "enable" : true
        }
    }
}
