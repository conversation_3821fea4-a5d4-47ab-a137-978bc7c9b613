<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view :class="'bg-'+theme.backgroundColor">
		<cu-custom :isBack="true" :bgColor="showBg?'bg-'+theme.backgroundColor:''">
			<block slot="backText">返回</block>
			<block slot="content">疯狂砍价</block>
		</cu-custom>
		<view class="bargain">
			<image class="bargain-bg" src="https://minio.joolun.com/joolun/1/material/b22b9037-a8e2-4ea6-b3cf-fc6ba09be829.png"></image>
			<view class="article no-card bargain-list">
				<view class="bargain-item margin-top-sm padding-bottom padding-top" v-for="(item, index) in bargainInfoList" :key="index">
					<navigator  class="padding-lr flex align-center"v-if="theme.showType!=2" hover-class="none" :url="'/extraJumpPages/shop/shop-detail/index?id=' + item.shopInfo.id">
						<view class="cu-avatar sm radius margin-right-xs" :style="'background-image:url(' + item.shopInfo.imgUrl + ')'"></view>
						<view class="cu-tag bg-red light sm radius margin-right-xs" v-if="item.shopInfo.saleType == 2">
							自营 </view>
						<text class="text-black text-bold">{{item.shopInfo.name}}</text>
						<text class="cuIcon-right text-sm"></text>
					</navigator>
					<view class="flex padding-xs padding-bottom margin-top-sm align-center">
						<image :src="item.picUrl" mode="aspectFill" class="row-img margin-left-sm"></image>
						<view class="margin-left-sm bargain-information">
							<view class="text-black text-df overflow-1 padding-right-sm">{{item.name}}</view>
							<view class="flex justify-start margin-top-sm align-center">
								<view class="text-price text-bold text-xl text-red">{{item.bargainPrice}}</view>
								<view class="text-price text-decorat text-df text-gray margin-left-sm">{{item.goodsSku?item.goodsSku.salesPrice:''}}</view>
								<view class="cu-tag bg-red radius sm margin-left" v-if="item.goodsSpu&&item.goodsSpu.freightTemplat.type == '2'">包邮</view>
							</view>
							<view class="flex margin-top-sm">
								<view class="text-sm text-gray">已有{{item.launchNum}}人参与</view>
							</view>
						</view>
					</view>
					<view class="flex justify-center padding-top-xs">
						<navigator class="cu-btn round shadow-blur btn-enter" :class="'bg-'+theme.backgroundColor" hover-class="none" :url="'/pageA/bargain/bargain-detail/index?id=' + item.id"><text class="cuIcon-forward"><text class="margin-left-xs">前去砍价</text></text></navigator>
					</view>
				</view>
			</view>
			<view :class="'cu-load ' + (loadmore?'loading':'over') + ' bg-'+theme.backgroundColor"></view>
		</view>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2018-2019
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	const util = require("utils/util.js");
	const app = getApp();
	import api from 'utils/api'
	import couponInfo from "components/coupon-info/index";

	export default {
		data() {
			return {
				showBg: false, //是否显示背景色
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				page: {
					searchCount: false,
					current: 1,
					size: 10,
					ascs: 'sort',
					//升序字段
					descs: ''
				},
				parameter: {},
				loadmore: true,
				bargainInfoList: []
			};
		},

		components: {
			couponInfo
		},
		props: {},

		//页面滑动 监听事件
		onPageScroll(e){
			if (e.scrollTop > 20) {
				this.showBg = true;
			} else {
				this.showBg = false;
			}
		},
		onShow() {},

		onLoad: function(options) {
			if(options.shopId){
				this.parameter.shopId = options.shopId
			}
			app.initPage().then(res => {
				this.bargainInfoPage();
			});
		},

		onReachBottom() {
			if (this.loadmore) {
				this.page.current = this.page.current + 1;
				this.bargainInfoPage();
			}
		},

		onPullDownRefresh() {
			// 显示顶部刷新图标
			uni.showNavigationBarLoading();
			this.refresh(); // 隐藏导航栏加载框

			uni.hideNavigationBarLoading(); // 停止下拉动作

			uni.stopPullDownRefresh();
		},

		methods: {
			bargainInfoPage() {
				api.bargainInfoPage(Object.assign({}, this.page, util.filterForm(this.parameter))).then(res => {
					let bargainInfoList = res.data.records;
					this.bargainInfoList = [...this.bargainInfoList, ...bargainInfoList];
					if (bargainInfoList.length < this.page.size) {
						this.loadmore = false;
					}
				});
			},

			refresh() {
				this.loadmore = true;
				this.bargainInfoList = [];
				this.page.current = 1;
				this.bargainInfoPage();
			}

		}
	};
</script>
<style>
	.row-img {
		width: 200rpx !important;
		height: 200rpx !important;
		border-radius: 10rpx;
	}

	.bargain{
		min-height: 1450rpx;
	}

	.bargain-item{
		background-color: #FFFFFF;
		width: 90%;
		margin: auto;
		border-radius: 10rpx;
		margin-bottom: 40rpx;
	}

	.bargain-bg{
		width: 100vw;
		height: 560rpx;
		margin: -50rpx 20rpx;
	}
	
	.bargain-information{
		width: 460rpx;
	}

	.bargain-list{
		z-index: 999999;
	}

	.btn-enter{
		width: 580rpx;
		height: 76rpx;
	}
</style>
