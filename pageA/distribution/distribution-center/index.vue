<template>
	<view class="">
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true">
			<block slot="backText">返回</block>
			<block slot="content">分销中心</block>
		</cu-custom>
		<!-- 如果没有成为分销员 -->
		<view v-if="!distribution">
			<!-- 指定分销 -->
			<view class="distribution-image-bg" v-if="distributionConfig.distributionModel==1" :class="'bg-'+theme.backgroundColor">
				<image class="distribution-image" src="https://minio.joolun.com/joolun/1/material/bbb0e407-7f41-4fe1-af81-fdd155ffd5d1.png"></image>
				<view class="text-center distribution-text">
					<view class="text-xl text-yellow">您当前还未获得分销权限</view>
					<view class="margin-top text-df text-yellow text-height">请联系商城管理员即可开启分销权限<br/></view>
					<navigator open-type="navigateBack" class="cu-item arrow margin-top-xl" hover-class="none">
						<button class="cu-btn back-home text-red">点击返回</button>
					</navigator>
				</view>
			</view>
			<!-- 人人分销 -->
			<view class="distribution-image-bg" v-else-if="distributionConfig.distributionModel==2" :class="'bg-'+theme.backgroundColor">
				<image class="distribution-image" src="https://minio.joolun.com/joolun/1/material/bbb0e407-7f41-4fe1-af81-fdd155ffd5d1.png"></image>
				<view class="text-center distribution-text">
					<view class="text-xl text-yellow">您当前还未获得分销权限</view>
					<view class="margin-top text-df text-yellow text-height">分享商品链接或活动海报给好友<br/>好友注册并登录商城后成功支付订单<br/>即可自动获得分销权限</view>
					<navigator open-type="navigateBack" class="cu-item arrow margin-top-xl" hover-class="none">
						<button class="cu-btn back-home text-red">点击返回</button>
					</navigator>
				</view>
			</view>
			<!-- 满额分销 -->
			<view class="distribution-image-bg" style="" v-else-if="distributionConfig.distributionModel==3" :class="'bg-'+theme.backgroundColor">
				<image class="distribution-image" src="https://minio.joolun.com/joolun/1/material/bbb0e407-7f41-4fe1-af81-fdd155ffd5d1.png"></image>
				<view class="text-center distribution-text">
					<view class="text-xl text-yellow">您当前还未获得分销权限</view>
					<view class="margin-top text-df text-yellow text-height">您当前在商城的消费金额为<text class="text-price">{{userConsumptionRecord.totalAmount}}</text>元<br/>当消费满<text class="text-price">{{distributionConfig.fullAmount}}</text>元，即可获得分销权限<br/>快去商城购置商品获取分销权限吧！</view>
					<navigator open-type="navigateBack" class="cu-item arrow margin-top-xl" hover-class="none">
						<button class="cu-btn back-home text-red">点击返回</button>
					</navigator>
				</view>
			</view>
		</view>
		<!-- 如果是分销员 -->
		<view  v-else>
			<view class="flex justify-between" style="width: 100%; height: 280rpx;" :class="'bg-'+theme.backgroundColor">
				<view class="cu-item flex align-center margin-left" style="margin-top: -50rpx;">
					<view class="cu-avatar round df" 
						:style="userInfo.headimgUrl?'background-image:url(' + userInfo.headimgUrl + ')':''">
						{{!userInfo.headimgUrl ? '头' : ''}}</view>
					<view class="flex align-center margin-left-sm">
						<view class="text-lg">{{userInfo.nickName}}</view>
						<view class="margin-left" v-if="userInfo.phone">{{ userInfo.phone }}</view>
					</view>
				</view>
				<image style="width: 280rpx; height: 280rpx;" src="https://minio.joolun.com/joolun/1/material/3fe86c5b-c6e6-470e-b026-d306e78a8259.png"></image>
			</view>
			<view class="margin-lr-sm bg-white" style="margin-top: -120rpx; border-radius: 20rpx;">
				<view class="flex justify-center margin-top-xs">
					<view class="margin-top-xl justify-center">
						<view class="text-sm">您当前可提现金额（元）</view>
						<view class="distribution-money text-scarlet text-bold margin-top-sm">{{money}}</view>
					</view>
					
				</view>
				<view class="flex justify-between padding-lr margin-top-sm">
					<view class="text-center">
						<view class="text-sm">您累计获得佣金（元）</view>
						<view class="text-bold text-black text-xxl margin-top-xs">{{distribution.commissionTotal}}</view>
					</view>
					<view class="text-center">
						<view class="text-sm">您累计提现佣金（元）</view>
						<view class="text-bold text-black text-xxl margin-top-xs">{{distribution.commissionWithdrawal}}</view>
					</view>
				</view>
				<navigator class="cu-item flex justify-center" url="/pageA/distribution/distribution-withdraw/index" hover-class="none">
					<button class="cu-btn bg-white withdraw round margin-tb-sm" :class="'bg-'+theme.backgroundColor">提现</button>
				</navigator>
			</view>
			<view class="cu-list margin-lr-sm margin-top bg-white statistics">
				<navigator class="flex align-center justify-between padding-tb padding-lr-sm" url="/pageA/distribution/distribution-card/index" hover-class="none">
					<view class="content flex align-center">
						<image class="distribution-icon" src="../../../static/public/img/user-center/user-article.png"></image>
						<view class="text-df margin-left-xs">
							<view class="text-df margin-left-xs">推广名片</view>
							<view class="text-sm text-gray margin-left-xs">推广赚佣金，共同创造收益</view>
						</view>
					</view>
					<view class="cuIcon-right"></view>
				</navigator>
				<navigator class="flex align-center justify-between padding-tb padding-lr-sm" url="/pageA/distribution/distribution-withdraw-list/index" hover-class="none">
					<view class="content flex align-center">
						<image class="distribution-icon" src="../../../static/public/img/user-center/daifukuan.png"></image>
						<view class="text-df margin-left-xs">
							<view class="text-df margin-left-xs">提现记录</view>
							<view class="text-sm text-gray margin-left-xs">查看我的提现记录</view>
						</view>
					</view>
					<view class="cuIcon-right"></view>
				</navigator>
				<navigator class="flex align-center justify-between padding-tb padding-lr-sm" url="/pageA/distribution/distribution-promotion-statistical/index" hover-class="none">
					<view class="content flex align-center">
						<image class="distribution-icon" src="../../../static/public/img/user-center/daipinglun.png"></image>
						<view class="text-df margin-left-xs">
							<view class="text-df margin-left-xs">推广统计</view>
							<view class="text-sm text-gray margin-left-xs">查看我的团队成员</view>
						</view>
					</view>
					<view class="cuIcon-right"></view>
				</navigator>
				<navigator class="flex align-center justify-between padding-tb padding-lr-sm" url="/pageA/distribution/distribution-order-list/index" hover-class="none">
					<view class="content flex align-center">
						<image class="distribution-icon" src="../../../static/public/img/user-center/daishouhuo.png"></image>
						<view class="text-df margin-left-xs">
							<view class="text-df margin-left-xs">分销订单</view>
							<view class="text-sm text-gray margin-left-xs">我的分销收益明细</view>
						</view>
					</view>
					<view class="cuIcon-right"></view>
				</navigator>
				<navigator class="flex align-center justify-between padding-tb padding-lr-sm" url="/pageA/distribution/distribution-promotion-ranking/index" hover-class="none">
					<view class="content flex align-center">
						<image class="distribution-icon" src="../../../static/public/img/user-center/usergrade.png"></image>
						<view class="text-df margin-left-xs">
							<view class="text-df margin-left-xs">推广排行</view>
							<view class="text-sm text-gray margin-left-xs">我的收益排行</view>
						</view>
					</view>
					<view class="cuIcon-right"></view>
				</navigator>
			</view>
		</view>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2018-2021
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	const util = require("utils/util.js");
	const numberUtil = require("utils/numberUtil.js");
	const app = getApp();
	import api from 'utils/api'
	
	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				userInfo: {},
				distribution: undefined,
				money: 0,//当前佣金金额
				userConsumptionRecord: {
					totalAmount: 0//已消费金额
				},//用户消费记录
				distributionConfig: {
					fullAmount: 0
				}
			}
		},
		onLoad(){
			this.userInfo = uni.getStorageSync('user_info')
		},
		onShow(){
			this.initData();
		},
		methods: {
			initData(){
				api.distributionuser().then(res => {
					if(res.data) {//是分销员
						this.distribution = res.data
						let money = this.distribution.commissionTotal - this.distribution.commissionWithdrawal
						this.money = numberUtil.numberFormat(money, 2)
					}
				});
				api.userRecord().then(res => {
					if(res.data) {
						this.userConsumptionRecord = res.data
					}
				});
				api.distributionConfig().then(res => {
					if(res.data) {
						this.distributionConfig = res.data
					}
				});
			}
		}
	}
</script>

<style>
	page{
		
	}
	
	.distribution-text{
		width: 100%;
	}
	
	.distribution-image-bg{
		position: relative;
		height: 100vh;
	}
	
	.distribution-image{
		width: 100%;
		position: fixed;
		height: 79vh;
	}
	
	.text-height{
		line-height: 50rpx;
	}
	
	.distribution-text{
		position: fixed;
		padding-top: 800rpx;
	}
	
	.withdraw{
		width:360rpx;
		height: 88rpx;
	}
	
	.distribution-money{
		font-size: 80rpx;
		font-family: 'Trebuchet MS', 'Lucida Sans Unicode', 'Lucida Grande', 'Lucida Sans', Arial, sans-serif;
	}
	
	.distribution-icon{
		width: 56rpx !important; 
		height: 56rpx !important;
	}
	
	.statistics{
		border-radius: 20rpx;
	}
</style>
