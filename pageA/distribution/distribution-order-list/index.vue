<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true">
			<block slot="backText">返回</block>
			<block slot="content">分销订单</block>
		</cu-custom>
		<view :class="'bg-'+theme.backgroundColor">
			<view class="title-text text-center">
				<view v-if="distributionOrderTotal>0">
					<view class="">您当前累计推广订单</view>
					<view class="text-bold title-text-1">{{distributionOrderTotal}}</view>
				</view>
				<view v-else>您还没有订单记录呢，<br>赶快加油噢！</view>
			</view>
			<image class="title-image" src="https://minio.joolun.com/joolun/1/material/2cdc2016-5e85-46bb-b58a-bbbfb4ba74c4.png"></image>
			<view class="cu-bar search padding-bottom-xl">
				<view class="search-form round">
					<text class="cuIcon-search"></text>
					<input :adjust-position="false" type="text" v-model="parameter.orderId" placeholder="订单号" confirm-type="search"></input>
				</view>
				<view class="action">
					<button class="cu-btn round text-gray" @click="search">搜索</button>
				</view>
			</view>
		</view>
		<view class="bg-white radius promotion-list">
			<view class="flex padding-top padding-lr justify-center">
				<view class="cu-capsule radius">
					<view @click="tabChange(1)" class='cu-tag padding padding-lr-xl'
						:class="tabVal==1?'bg-'+theme.backgroundColor:'line-'+theme.themeColor">
						一级（{{distributionOrderTotal1}}）
					</view>
					<view  @click="tabChange(2)" class="cu-tag padding padding-lr-xl"
						:class="tabVal==2?'bg-'+theme.backgroundColor:'line-'+theme.themeColor">
						二级（{{distributionOrderTotal2}}）
					</view>
				</view>
			</view>
			<view class="padding" v-show="tabVal==1" >
				<view class="cu-list menu-avatar radius padding" style="box-shadow:0px 0px 50px #ebebeb;" v-for="(item, index) in distributionOrderList1" :key="index">
					<view class="flex justify-between align-center">
						<view class="flex align-center">
							<view class="cu-avatar round lg"
							:style="item.userInfo.headimgUrl?'background-image:url(' + item.userInfo.headimgUrl + ')':''">{{!item.userInfo.headimgUrl ? '头' : ''}}</view>
							<view class="margin-left-sm">{{item.userInfo.nickName}}</view>
						</view>
						<view class="text-gray padding-right-sm">{{item.commissionStatus | filterStatus}}
							<text class="text-price text-xl text-red text-bold margin-left">{{item.commission}}</text>
						</view>
					</view>
					<view class="margin-top-sm padding-bottom-sm">
						<view class="text-gray text-sm">订单编号：{{item.orderInfo.orderNo}}</view>
						<view class="text-gray text-sm margin-top-sm">下单时间：{{item.createTime}}</view>
					</view>
				</view>
				<view :class="'cu-load ' + (loadmore1?'loading':'over')"></view>
			</view>
			<view class="padding" v-show="tabVal==2" >
				<view class="cu-list menu-avatar radius padding" style="box-shadow:0px 0px 50px #ebebeb;" v-for="(item, index) in distributionOrderList2" :key="index">
					<view class="flex justify-between align-center">
						<view class="flex align-center">
							<view class="cu-avatar round lg"
								:style="item.userInfo.headimgUrl?'background-image:url(' + item.userInfo.headimgUrl + ')':''">{{!item.userInfo.headimgUrl ? '头' : ''}}</view>
							<view class="margin-left-sm">{{item.userInfo.nickName}}</view>
						</view>
						<view class="text-gray padding-right-sm">
							<text class="text-sm">{{item.commissionStatus | filterStatus}}</text>
							<text class="text-price text-xl text-red text-bold margin-left">{{item.commission}}</text>
						</view>
					</view>
					<view class="font-weight margin-top-sm padding-bottom-sm">
						<view class="text-gray text-sm">订单编号：{{item.orderInfo.orderNo}}</view>
						<view class="text-gray text-sm margin-top-sm">下单时间：{{item.createTime}}</view>
					</view>
				</view>
				<view :class="'cu-load ' + (loadmore2?'loading':'over')"></view>
			</view>
		</view>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2020-2022
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	const util = require("utils/util.js");
	const app = getApp();
	import api from 'utils/api'

	export default {

		filters:{
			filterStatus(val){
				val = parseInt(val) - 1
				let stauts =  ['冻结', '已解冻']
				return stauts[val]
			}
		},
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				tabVal: 1,
				scrollLeft: 0,
				userInfo: undefined,
				distribution: {},
				distributionOrderTotal: 0,
				distributionOrderTotal1: 0,
				distributionOrderTotal2: 0,
				distributionOrderList1: [],
				distributionOrderList2: [],
				page1: {
					current: 1,
					size: 10,
					//升序字段
					descs: 'create_time'
				},
				page2: {
					current: 1,
					size: 10,
					//升序字段
					descs: 'create_time'
				},
				parameter: {
					orderId: ''
				},
				loadmore1: true,
				loadmore2: true,
			}
		},
		onLoad(){
			this.userInfo = uni.getStorageSync('user_info')
			this.initData()
		},

		onReachBottom() {
			if(this.tabVal==1){
				if (this.loadmore1) {
					this.page1.current = this.page1.current + 1;
					this.getDistributionOrderData(this.tabVal);
				}
			}else{
				if (this.loadmore2) {
					this.page2.current = this.page2.current + 1;
					this.getDistributionOrderData(this.tabVal);
				}
			}
		},

		methods: {
			tabChange(val){
				this.tabVal = val;
				if(val==1){
					this.page1.distributionLevel = val
				}else{
					this.page2.distributionLevel = val
				}
			},
			search(e) {
				this.relod()
				// this.getDistributionOrderData();
			},
			relod() {
				if(this.tabVal==1){
					this.loadmore1 = true;
					this.distributionOrderList1 = []
					this.page1.current = 1;
				}else{
					this.loadmore2 = true;
					this.distributionOrderList2 = []
					this.page2.current = 1;
				}
				this.getDistributionOrderData(this.tabVal);
			},
			getDistributionOrderData(tabVal){
				let page = tabVal == 1 ? this.page1 : this.page2
				api.distributionorderPage(Object.assign({}, page, util.filterForm(this.parameter))).then(res => {
					let dataList = res.data.records;
					if(tabVal==1){
						this.distributionOrderList1 = [...this.distributionOrderList1, ...dataList];
						if(this.distributionOrderTotal1<=0){
							this.distributionOrderTotal1 = res.data.total
							this.distributionOrderTotal = this.distributionOrderTotal1 + this.distributionOrderTotal2
						}
						if (dataList.length < this.page1.size) {
							this.loadmore1 = false;
						}
					}else{
						this.distributionOrderList2 = [...this.distributionOrderList2, ...dataList];
						if(this.distributionOrderTotal2<=0){
							this.distributionOrderTotal2 = res.data.total
							this.distributionOrderTotal = this.distributionOrderTotal1 + this.distributionOrderTotal2
						}
						if (dataList.length < this.page2.size) {
							this.loadmore2 = false;
						}
					}
				});
			},
			initData(){
				api.distributionuser().then(res => {
					console.log(res)
					if(res.data) {//是分销员
						this.distribution = res.data
					}
				});
				this.tabChange(2)
				this.getDistributionOrderData(2);
				this.tabChange(1)
				this.getDistributionOrderData(1);
			}
		}
	}
</script>

<style>
	page{
		background-color: #fff;
	}

	.title-text{
		position: absolute;
		top: 230rpx;
		left: 50rpx;
		color: #ffebac;
	}

	.title-text-1{
		font-size: 68rpx;
		color: #ffebac;
	}

	.title-image{
		width: 100vh;
		height: 286rpx;
	}

	.promotion-list{
		border-radius: 30rpx 30rpx 0 0;
		margin-top: -30rpx;
	}

	.font-weight{
		font-weight: 300;
	}
</style>
