<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true">
			<block slot="backText">返回</block>
			<block slot="content">推广统计</block>
		</cu-custom>
		<view :class="'bg-'+theme.backgroundColor">
			<view class="title-text text-center">
				<view>您当前的推广人数</view>
				<view class="text-bold title-text-1">{{distributionPromotionTotal}}</view>
			</view>
			<image class="title-image" src="https://minio.joolun.com/joolun/1/material/07dd880d-e719-4a3f-8e7d-ea2f627eb0b5.png"></image>
			<view class="cu-bar search padding-bottom-xl">
				<view class="search-form round">
					<text class="cuIcon-search"></text>
					<input :adjust-position="false" type="text" placeholder="会员名称" v-model="parameter.nickName" confirm-type="search"></input>
				</view>
				<view class="action">
					<button class="cu-btn round text-gray" @click="search">搜索</button>
				</view>
			</view>
		</view>
		<view class="bg-white radius promotion-list">
			<view class="flex padding-top padding-lr justify-center">
				<view class="cu-capsule radius">
					<view @click="tabChange(1)" class='cu-tag padding padding-lr-xl'
						:class="tabVal==1?'bg-'+theme.backgroundColor : (' line-'+theme.themeColor)">
						一级（{{distributionPromotionTotal1}}）
					</view>
					<view  @click="tabChange(2)" class="cu-tag padding padding-lr-xl"
						:class="tabVal==2?'bg-'+theme.backgroundColor : (' line-'+theme.themeColor)">
						二级（{{distributionPromotionTotal2}}）
					</view>
				</view>
			</view>
			<view class="margin-top" v-show="tabVal==1">
				<view class="cu-list menu-avatar" v-for="item in distributionPromotionList1" :key="item.id">
					<view class="flex justify-between align-center solid-bottom padding-left padding-right padding-bottom">
						<view class="flex align-center">
							<view class="cu-avatar round lg "
								:style="item.headimgUrl?'background-image:url(' + item.headimgUrl + ')':''">
								{{!item.headimgUrl ? '头' : ''}}</view>
							<view class="text-sm margin-left-sm">{{item.nickName}}</view>
						</view>
						<view class="text-sm text-red margin-right-sm">{{item.distributionUser?'分销员':'非分销员'}}</view>
					</view>
				</view>
				<view :class="'cu-load ' + (loadmore1?'loading':'over')" style=""></view>
			</view>
			<view class="margin-top" v-show="tabVal==2">
				<view class="cu-list menu-avatar" v-for="item in distributionPromotionList2" :key="item.id">
					<view class="flex justify-between align-center solid-bottom padding-left padding-right padding-bottom">
						<view class="flex align-center">
							<view class="cu-avatar round lg"
								:style="item.headimgUrl?'background-image:url(' + item.headimgUrl + ')':''">
								{{!item.headimgUrl ? '头' : ''}}</view>
							<view class="text-sm margin-left-sm">{{item.nickName}}</view>
						</view>
						<view class="text-sm text-red margin-right-sm">{{item.distributionUser?'分销员':'非分销员'}}</view>
					</view>
				</view>
				<view :class="'cu-load ' + (loadmore2?'loading':'over')"></view>
			</view>
		</view>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2018-2019
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	const util = require("utils/util.js");
	const app = getApp();
	import api from 'utils/api'
	
	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				tabVal: 1,
				userInfo: {},
				distributionPromotionTotal: 0,
				distributionPromotionTotal1: 0,
				distributionPromotionTotal2: 0,
				distributionPromotionList1: [],
				distributionPromotionList2: [],
				page1: {
					current: 1,
					size: 10,
					//升序字段
					descs: 'create_time'
				},
				page2: {
					current: 1,
					size: 10,
					//升序字段
					descs: 'create_time'
				},
				parameter: {
					nickName: ''
				},
				loadmore1: true,
				loadmore2: true,
			}
		},
		
		onLoad(){
			this.userInfo = uni.getStorageSync('user_info')
			this.initData()
		},
		
		onReachBottom() {
			if(this.tabVal==1){
				if (this.loadmore1) {
					this.page1.current = this.page1.current + 1;
					this.getDistributionPromotionData(this.tabVal);
				}
			}else{
				if (this.loadmore2) {
					this.page2.current = this.page2.current + 1;
					this.getDistributionPromotionData(this.tabVal);
				}
			}
			
		},
		
		methods: {
			tabChange(val){
				this.tabVal = val;
				if(val==1){
					this.parameter.parentId = this.userInfo.id
					this.parameter.parentSecondId = null
				}else{
					this.parameter.parentId = null
					this.parameter.parentSecondId = this.userInfo.id
				}
				// this.relod();
			},
			search(e) {
				
				this.relod()
				// this.getDistributionOrderData();
			},
			
			relod() {
				if(this.tabVal==1){
					this.loadmore1 = true;
					this.distributionPromotionList1 = []
					this.page1.current = 1;
				}else{
					this.loadmore2 = true;
					this.distributionPromotionList2 = []
					this.page2.current = 1;
				}
				this.getDistributionPromotionData(this.tabVal);
			},
			getDistributionPromotionData(tabVal){
				let page = tabVal == 1 ? this.page1 : this.page2
				api.distributionPromotionPage(Object.assign({}, page, util.filterForm(this.parameter))).then(res => {
					let dataList = res.data.records;
					if(tabVal==1){
						this.distributionPromotionList1 = [...this.distributionPromotionList1, ...dataList];
						if(this.distributionPromotionTotal1<=0){
							this.distributionPromotionTotal1 = res.data.total
							this.distributionPromotionTotal = this.distributionPromotionTotal1 + this.distributionPromotionTotal2
						}
						if (dataList.length < this.page1.size) {
							this.loadmore1 = false;
						}
					}else{
						this.distributionPromotionList2 = [...this.distributionPromotionList2, ...dataList];
						if(this.distributionPromotionTotal2<=0){
							this.distributionPromotionTotal2 = res.data.total
							this.distributionPromotionTotal = this.distributionPromotionTotal1 + this.distributionPromotionTotal2
						}
						
						if (dataList.length < this.page2.size) {
							this.loadmore2 = false;
						}
					}
					
				});
			},
			initData(){
				api.distributionuser(this.userInfo.id).then(res => {
					if(res.data) {//是分销员
						this.distribution = res.data
					} else {//不是分销员
						
					}
				});
				this.tabChange(2)
				this.getDistributionPromotionData(2);
				this.tabChange(1)
				this.getDistributionPromotionData(1);
			}
		}
	}
</script>

<style>
	page{
		background-color: #FFFFFF;
	}
	
	.title-text{
		position: absolute; 
		top: 280rpx; 
		left: 50rpx;
		color: #ffebac;
	}
	
	.title-text-1{
		font-size: 68rpx;
		color: #ffebac;
	}
	
	.title-image{
		width: 100vh;
		height: 350rpx;
	}
	
	.promotion-list{
		border-radius: 30rpx 30rpx 0 0; 
		margin-top: -30rpx;
	}
</style>
