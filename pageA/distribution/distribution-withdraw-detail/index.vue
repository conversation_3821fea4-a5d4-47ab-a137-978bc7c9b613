<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true">
			<block slot="backText">返回</block>
			<block slot="content">提现详情</block>
		</cu-custom>
		<view class="bg-white padding withdraw-bg">
			<view class="text-center">
				<view class="margin-top-sm">{{userwithdrawRecord.paymentMethod | filterPaymentMethod}}</view>
				<view class="text-xxl text-red margin-top-sm text-bold text-price">{{userwithdrawRecord.applyAmount}}</view>
				<view class="margin-top-sm">{{userwithdrawRecord.status | filterStatus}}</view>
			</view>
			<view class="cu-list padding">
				
				<view class="cu-item flex justify-between margin-top">
					<view class="text-gray text-sm">提现明细</view>
					<view class="text-sm">{{userwithdrawRecord.paymentDetail}}</view>
				</view>
				<!-- <view class="cu-item flex justify-between margin-top">
					<view class="text-gray text-sm">提现方式</view>
					<view class="text-sm">{{userwithdrawRecord.paymentMethod | filterPaymentMethod}}</view>
				</view> -->
				<view class="cu-item flex justify-between margin-top">
					<view class="text-gray text-sm">提现类型</view>
					<view class="text-sm">{{userwithdrawRecord.withdrawType | filterWithdrawType}}</view>
				</view>
				<view class="cu-item flex justify-between margin-top">
					<view class="text-gray text-sm">备注</view>
					<view class="text-sm">{{userwithdrawRecord.remarks?userwithdrawRecord.remarks:'无'}}</view>
				</view>
				<view class="cu-item flex justify-between margin-top">
					<view class="text-gray text-sm">创建时间</view>
					<view class="text-sm">{{userwithdrawRecord.createTime}}</view>
				</view>
				<view class="cu-item flex justify-between margin-top">
					<view class="text-gray text-sm">审核明细</view>
					<view class="text-sm">{{userwithdrawRecord.verifyDetail?userwithdrawRecord.verifyDetail:'无'}}</view>
				</view>
			</view>
		</view>
		<view class="padding-lr-xl flex justify-end">
			<navigator :url="'/pageA/distribution/distribution-withdraw/index?id='+userwithdrawRecord.id" hover-class="none">
				<text class="text-sm text-blue" v-if="userwithdrawRecord.status==0" >修改</text>
			</navigator>
		</view>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2018-2019
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	const util = require("utils/util.js");
	const app = getApp();
	import api from 'utils/api'
	
	export default {
		filters:{
			filterStatus(val){
				let stauts =  ['审核中', '审核通过', '审核不通过']
				return stauts[val]
			},
			filterPaymentMethod(val){
				val = parseInt(val) - 1
				let methods =  ['银行卡', '微信', '支付宝']
				return methods[val]
			},
			filterWithdrawType(val){
				val = parseInt(val) - 1
				let types =  ['分销拥金提现']
				return types[val]
			}
		},
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				id: '',
				userwithdrawRecord: {}
			}
		},
		onLoad(e){
			if(e){
				this.id = e.id
			}
		},
		onShow(){
			this.initData()
		},
		methods: {
			initData(){
				api.userwithdrawRecord(this.id).then(res => {
					if(res.data) {
						this.userwithdrawRecord = res.data
					}
				});
			}
		}
	}
</script>

<style>
	.withdraw-bg{
		width: 96%;
		margin: 30rpx auto; 
		border-radius: 20rpx;
	}
</style>
