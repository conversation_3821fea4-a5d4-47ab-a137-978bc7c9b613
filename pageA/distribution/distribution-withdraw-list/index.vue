<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true">
			<block slot="backText">返回</block>
			<block slot="content">提现记录</block>
		</cu-custom>
		<view class="text-center withdraw-list-bg" :class="'bg-'+theme.backgroundColor">
			<view class="title-text">
				<view class="">您当前已累计提现（元）</view>
				<view class="margin-top-xs">
					<text class="margin-right-xs"></text>
					<text class="text-bold title-text-1">{{distribution.commissionWithdrawal}}</text>
				</view>
			</view>
			<image class="title-image" src="https://minio.joolun.com/joolun/1/material/cbe4f8de-c733-44c1-a634-7d4b6aaa967c.png"></image>
		</view>
		<view class="cu-list menu-avatar withdraw-list bg-white">
			<navigator class="cu-item margin-top padding-bottom" v-for="(item,index) in distributionWithdrawList" :key="index"
				:url="'/pageA/distribution/distribution-withdraw-detail/index?id='+item.id" hover-class="none">
				<view class="cu-avatar round lg"
					:style="userInfo.headimgUrl?'background-image:url(' + userInfo.headimgUrl + ')':''">
					{{!userInfo.headimgUrl ? '头' : ''}}
				</view>
				<view class="content">
					<view>{{item.paymentDetail}}</view>
					<view class="text-gray text-sm flex">{{item.createTime}}</view>
				</view>
				<view>
					<view class="flex justify-end text-scarlet text-sm margin-right-sm">{{item.status | filterStatus}}</view>
					<view class="text-xl text-bold text-scarlet text-right margin-right-sm text-price">{{item.applyAmount}}</view>
				</view>
				<text class="cuIcon-right text-gray text-xs"></text>
			</navigator>
		</view>
		<view class="margin-top120" :class="'cu-load ' + (loadmore?'loading':'over')"></view>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2018-2019
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	const util = require("utils/util.js");
	const app = getApp();
	import api from 'utils/api'

	export default {
		filters: {
			filterStatus(val) {
				let stauts = ['审核中', '审核通过', '审核不通过']
				return stauts[val]
			}
		},
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				userInfo: undefined,
				distribution: {},
				distributionWithdrawList: [],
				page: {
					current: 1,
					size: 10,
					//升序字段
					descs: 'create_time'
				},
				parameter: {
					orderId: ''
				},
				loadmore: true,
			}
		},
		onLoad() {
			this.userInfo = uni.getStorageSync('user_info')
			this.initData()
		},
		onShow() {
			this.distributionWithdrawList = []
			this.getDistributionWithdrawListData();
		},
		onReachBottom() {
			if (this.loadmore) {
				this.page.current = this.page.current + 1;
				this.getDistributionWithdrawListData();
			}
		},
		methods: {
			getDistributionWithdrawListData() {
				api.userwithdrawRecordPage(Object.assign({}, this.page, util.filterForm(this.parameter))).then(res => {
					let dataList = res.data.records;
					this.distributionWithdrawList = [...this.distributionWithdrawList, ...dataList];
					if (dataList.length < this.page.size) {
						this.loadmore = false;
					}
				});
			},
			initData() {
				api.distributionuser(this.userInfo.id).then(res => {
					if (res.data) { //是分销员
						this.distribution = res.data
					}
				});
			}
		}
	}
</script>

<style>
	page{
		background-color: #FFFFFF;
	}
	
	.withdraw-list-bg {
	}

	.title-text {
		position: absolute;
		top: 220rpx;
		z-index: 1024;
		text-align: center;
		width: 100%;
	}

	.title-text-1 {
		font-size: 88rpx;
		font-family: 'Trebuchet MS', 'Lucida Sans Unicode', 'Lucida Grande', 'Lucida Sans', Arial, sans-serif;
	}

	.title-image {
		width: 100vh;
		height: 390rpx;
	}

	.withdraw-list {
		border-radius: 30rpx !important;
		margin: -30rpx auto;
		border-radius: 10rpx;
	}

	.margin-top120 {
		margin-top: 120rpx;
	}
</style>
