<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true">
			<block slot="backText">返回</block>
			<block slot="content">退款订单</block>
		</cu-custom>
		
		<!-- 搜索框 -->
		<view class="search-container">
			<view class="search-box">
				<image class="search-icon" src="/static/myImg/xiaoxi.png" mode="aspectFit"></image>
				<input class="search-input" placeholder="输入关键字搜索" v-model="searchKeyword" />
			</view>
		</view>
		
		<!-- 状态筛选 -->
		<scroll-view scroll-x class="status-tabs">
			<view class="tab-container">
				<view 
					class="tab-item" 
					:class="{ active: currentTab === index }"
					v-for="(tab, index) in statusTabs" 
					:key="index"
					@click="switchTab(index)"
				>
					{{ tab.name }}
				</view>
			</view>
		</scroll-view>
		
		<!-- 退款订单列表 -->
		<view class="refund-container">
			<view class="refund-item" v-for="(item, index) in filteredRefundList" :key="index" @click="goToDetail(item)">
				<!-- 订单头部 -->
				<view class="refund-header">
					<view class="store-info">
						<view class="store-icon"></view>
						<text class="store-name">{{ item.storeName }}</text>
					</view>
					<!-- <text class="refund-status" :class="getStatusClass(item.status)">{{ item.statusText }}</text> -->
				</view>
				
				<!-- 商品信息 -->
				<view class="product-section">
					<image class="product-image" :src="getImageSrc(item.pic, index)" mode="aspectFit" @error="handleImageError($event, index)"></image>
					<view class="product-info">
						<text class="product-name">{{ item.name }}</text>
						<text class="product-spec">{{ item.spec }}</text>
						<view class="price-section">
							<text class="price">¥{{ item.price }}</text>
							<text class="quantity">×{{ item.quantity }}</text>
						</view>
					</view>
				</view>
				
				<!-- 退款信息 -->
				<view class="refund-summary">
					<view class="refund-row">
						<text class="refund-label">退款</text>
						<text class="refund-amount">已退款 ¥{{ item.refundAmount }}</text>
					</view>
				</view>
				
				<!-- 操作按钮 -->
				<view class="action-section" v-if="item.status === '已同意'">
					<view class="action-btn" @click.stop="handleEvaluate(item)">评价</view>
				</view>
			</view>
			
			<view class="load-more" v-if="!hasMore">
				<text class="load-text">没有更多了</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			theme: getApp().globalData.theme,
			CustomBar: this.CustomBar,
			searchKeyword: '',
			currentTab: 0,
			statusTabs: [
				{ name: '全部', value: '' },
				{ name: '申核中', value: '申核中' },
				{ name: '已同意', value: '已同意' },
				{ name: '驳回', value: '驳回' }
			],
			defaultImage: '/static/public/img/no_pic.png',
			imageErrorFlags: {},
			hasMore: false,
			refundList: [
				{
					id: 1,
					storeName: '慢病服务包',
					name: '消毒棉片',
					spec: '11盒',
					price: '22.00',
					quantity: 1,
					pic: 'https://crm.dcydkj.com/upload/2/20230824/7e0fccb0793ff59ed14979e693a9992d.png',
					refundAmount: '25.00',
					status: '已同意',
					statusText: '通过',
					refundId: '250520141852750617',
					orderNum: '250520131500723878',
					applyTime: '2025-05-20 14:18:52',
					refundType: '退款',
					refundReason: '退款',
					auditNote: '同意'
				}
			]
		};
	},
	
	computed: {
		filteredRefundList() {
			let list = this.refundList;
			
			// 状态筛选
			if (this.statusTabs[this.currentTab].value) {
				list = list.filter(item => item.status === this.statusTabs[this.currentTab].value);
			}
			
			// 关键字搜索
			if (this.searchKeyword.trim()) {
				const keyword = this.searchKeyword.trim().toLowerCase();
				list = list.filter(item => 
					item.name.toLowerCase().includes(keyword) ||
					item.orderNum.includes(keyword) ||
					item.refundId.includes(keyword)
				);
			}
			
			return list;
		}
	},
	
	onLoad(options) {
		// 如果从订单详情页传入了订单信息，可以在这里处理
		if (options.orderData) {
			const orderData = JSON.parse(options.orderData);
			console.log('订单数据:', orderData);
		}
	},
	
	methods: {
		// 切换状态标签
		switchTab(index) {
			this.currentTab = index;
		},
		
		// 跳转到退款详情
		goToDetail(item) {
			uni.navigateTo({
				url: '/pageA/refundDetail/index?refundData=' + JSON.stringify(item)
			});
		},
		
		// 获取状态样式类
		getStatusClass(status) {
			switch (status) {
				case '已同意':
					return 'status-approved';
				case '申核中':
					return 'status-pending';
				case '驳回':
					return 'status-rejected';
				default:
					return '';
			}
		},
		
		// 获取图片源
		getImageSrc(picUrl, index) {
			if (this.imageErrorFlags[index]) {
				return this.defaultImage;
			}
			return picUrl || this.defaultImage;
		},
		
		// 处理图片加载错误
		handleImageError(e, index) {
			this.$set(this.imageErrorFlags, index, true);
			console.log('图片加载失败:', e);
		},
		
		// 处理评价
		handleEvaluate(item) {
			uni.showToast({
				title: '跳转到评价页面',
				icon: 'none'
			});
		}
	}
};
</script>

<style scoped lang="scss">
.search-container {
	padding: 20rpx;
	background: white;
	
	.search-box {
		display: flex;
		align-items: center;
		background: #f5f5f5;
		border-radius: 32rpx;
		padding: 16rpx 24rpx;
		
		.search-icon {
			width: 32rpx;
			height: 32rpx;
			margin-right: 16rpx;
		}
		
		.search-input {
			flex: 1;
			font-size: 28rpx;
			color: #333;
			
			&::placeholder {
				color: #999;
			}
		}
	}
}

.status-tabs {
	background: white;
	border-bottom: 1rpx solid #f0f0f0;
	
	.tab-container {
		display: flex;
		padding: 0 20rpx;
		white-space: nowrap;
		
		.tab-item {
			padding: 24rpx 32rpx;
			font-size: 28rpx;
			color: #666;
			position: relative;
			
			&.active {
				color: #ff4757;
				font-weight: 500;
				
				&::after {
					content: '';
					position: absolute;
					bottom: 0;
					left: 50%;
					transform: translateX(-50%);
					width: 40rpx;
					height: 4rpx;
					background: #ff4757;
					border-radius: 2rpx;
				}
			}
		}
	}
}

.refund-container {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

.refund-item {
	background: white;
	border-radius: 16rpx;
	margin-bottom: 20rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
	
	.refund-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 24rpx;
		border-bottom: 1rpx solid #f0f0f0;
		
		.store-info {
			display: flex;
			align-items: center;
			
			.store-icon {
				width: 32rpx;
				height: 32rpx;
				background: #ff6b35;
				border-radius: 50%;
				margin-right: 16rpx;
				position: relative;
				
				&::after {
					content: '';
					position: absolute;
					top: 50%;
					left: 50%;
					transform: translate(-50%, -50%);
					width: 16rpx;
					height: 16rpx;
					background: white;
					border-radius: 2rpx;
				}
			}
			
			.store-name {
				font-size: 28rpx;
				color: #333;
				font-weight: 500;
			}
		}
		
		.refund-status {
			font-size: 26rpx;
			font-weight: 500;
			
			&.status-approved {
				color: #52c41a;
			}
			
			&.status-pending {
				color: #faad14;
			}
			
			&.status-rejected {
				color: #ff4d4f;
			}
		}
	}
	
	.product-section {
		display: flex;
		padding: 24rpx;
		
		.product-image {
			width: 120rpx;
			height: 120rpx;
			border-radius: 12rpx;
			margin-right: 24rpx;
			background: #f8f8f8;
		}
		
		.product-info {
			flex: 1;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			
			.product-name {
				font-size: 28rpx;
				color: #333;
				font-weight: 500;
				line-height: 1.4;
				margin-bottom: 8rpx;
			}
			
			.product-spec {
				font-size: 24rpx;
				color: #999;
				margin-bottom: 16rpx;
			}
			
			.price-section {
				display: flex;
				justify-content: space-between;
				align-items: center;
				
				.price {
					font-size: 32rpx;
					color: #ff4757;
					font-weight: 600;
				}
				
				.quantity {
					font-size: 28rpx;
					color: #666;
				}
			}
		}
	}
	
	.refund-summary {
		padding: 24rpx;
		border-top: 1rpx solid #f0f0f0;
		background: #fafafa;
		
		.refund-row {
			display: flex;
			justify-content: space-between;
			align-items: center;
			
			.refund-label {
				font-size: 26rpx;
				color: #333;
			}
			
			.refund-amount {
				font-size: 26rpx;
				color: #ff4757;
				font-weight: 500;
			}
		}
	}
	
	.action-section {
		padding: 24rpx;
		display: flex;
		justify-content: flex-end;
		
		.action-btn {
			padding: 12rpx 32rpx;
			border: 1rpx solid #ff4757;
			border-radius: 32rpx;
			font-size: 26rpx;
			color: #ff4757;
			background: white;
		}
	}
}

.load-more {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 40rpx 0;
	
	.load-text {
		font-size: 24rpx;
		color: #999;
	}
}
</style>
