<!--
  - Copyright (C) 2018-2022
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true">
			<block slot="backText">返回</block>
			<block slot="content">我的秒杀</block>
		</cu-custom>
		<scroll-view scroll-x class="bg-white nav fixed">
			<view class="flex text-center">
				<view :class="'cu-item flex-sub ' + (index==tabCur?'cur text-'+theme.themeColor:'')" v-for="(item, index) in orderStatus"
				 :key="index" @tap="tabSelect" :data-index="index" :data-key="item.key">{{item.value}}</view>
			</view>
		</scroll-view>
		<view class="margin-top-bar">
			<view class="cu-card article">
				<view class="cu-item" v-for="(item, index) in orderList" :key="index">
					<view class="bg-white flex justify-between padding-lr padding-top padding-bottom-xs">
						<navigator v-if="theme.showType!='2'" hover-class="none" :url="'/extraJumpPages/shop/shop-detail/index?id=' + item.shopInfo.id">
							<view class="cu-avatar sm radius" :style="'background-image:url(' + item.shopInfo.imgUrl + ')'"></view>
							<text class="text-black text-bold margin-left-sm">{{item.shopInfo.name}}</text>
							<text class="cuIcon-right text-sm"></text>
						</navigator>
						<view v-else class="text-gray">订单编号：{{item.id}}</view>
						<view class="text-red text-sm">{{item.statusDesc}}</view>
					</view>
					<navigator hover-class="none" :url="'/pages/order/order-detail/index?id=' + item.id" class="cu-item" v-for="(item2, index2) in item.listOrderItem"
					 :key="index2">
						<view class="content align-center">
							<image :src="item2.picUrl ? item2.picUrl : '/static/public/img/no_pic.png'" mode="aspectFill" class="row-img margin-top-xs"></image>
							<view class="desc row-info block">
								<view class="text-black margin-top-xs overflow-2">
									<text class="cu-tag bg-red sm radius margin-right-xs" v-if="item.orderType != '0'">{{item.orderType == '1' ? '砍价' : item.orderType == '2' ? '拼团' : item.orderType == '3' ? '秒杀' : ''}}</text>
									<text class="text-df">{{item2.spuName}}</text></view>
								<view class="text-gray text-sm overflow-2 margin-top-xs" v-if="item2.specInfo">{{item2.specInfo}}</view>
								<view class="flex justify-between align-center margin-top-xs">
									<view class="text-price text-xl text-bold text-red">{{item2.paymentPrice}}</view>
									<view class="text-gray text-l">x{{item2.quantity}}</view>
								</view>
								<view class="margin-top-xs flex justify-between align-center">
									<text class="flex-sub text-sm text-gray">创建时间:</text>
									<view class="text-sm text-gray">{{item2.createTime}}</view>
								</view>
							</view>
						</view>
						<view class="cu-item text-right padding-sm margin-right-sm" @tap.stop>
							<navigator class="cu-btn line sm text-orange" v-if="item2.status != '0'" :url="'/pages/order/order-refunds/form/index?orderItemId=' + item2.id">{{item2.statusDesc}}</navigator>
						</view>
					</navigator>
					<order-operate class="response" :orderInfo="item" @orderCancel="orderCancel($event,index)" @orderReceive="orderCancel($event,index)"
					 @orderDel="orderDel($event,index)" @unifiedOrder="unifiedOrder($event,index)" :data-index="index">
					</order-operate>
				</view>
			</view>
			<view :class="'cu-load bg-gray ' + (loadmore?'loading':'over')"></view>
		</view>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2018-2022
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	const util = require("utils/util.js");
	const app = getApp();
	import api from 'utils/api'
	import orderOperate from "components/order-operate/index";

	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				tabCur: 0,
				orderStatus: [{
					value: '全部秒杀',
					key: ''
				}, {
					value: '待付款',
					key: '0'
				}, {
					value: '待发货',
					key: '1'
				}, {
					value: '待收货',
					key: '2'
				}, {
					value: '已完成',
					key: '4'
				}],
				page: {
					searchCount: false,
					orderType: 3,
					current: 1,
					size: 10,
					ascs: '',
					//升序字段
					descs: 'create_time'
				},
				parameter: {},
				loadmore: true,
				orderList: []
			};
		},

		components: {
			orderOperate
		},
		props: {},

		onShow() {
			app.initPage().then(res => {
				this.loadmore = true;
				this.orderList = [];
				this.page.current = 1;
				this.orderPage();
			});
		},
		onLoad: function(options) {
			if (options.status || options.status == 0) {
				let that = this;
				this.parameter.status = options.status;
				this.orderStatus.forEach(function(status, index) {
					if (status.key == options.status) {
						that.tabCur = index;
					}
				});
			}
		},

		onReachBottom() {
			if (this.loadmore) {
				this.page.current = this.page.current + 1;
				this.orderPage();
			}
		},

		onPullDownRefresh() {
			// 显示顶部刷新图标
			uni.showNavigationBarLoading();
			this.refresh(); // 隐藏导航栏加载框
			uni.hideNavigationBarLoading(); // 停止下拉动作
			uni.stopPullDownRefresh();
		},

		methods: {
			orderPage() {
				api.orderPage(Object.assign({}, this.page, util.filterForm(this.parameter))).then(res => {
					let orderList = res.data.records;
					this.orderList = [...this.orderList, ...orderList];
					if (orderList.length < this.page.size) {
						this.loadmore = false;
					}
				});
			},

			refresh() {
				this.loadmore = true;
				this.orderList = [];
				this.page.current = 1;
				this.orderPage();
			},

			tabSelect(e) {
				let dataset = e.currentTarget.dataset;
				if (dataset.index != this.tabCur) {
					this.tabCur = dataset.index;
					this.parameter.status = dataset.key;
					this.refresh();
				}
			},

			orderCancel(item, index) {
				api.orderGet(this.orderList[index].id).then(res => {
					this.orderList[index] = res.data;
					this.orderList.splice(); //当页面不渲染时调用此方法可以刷新页面
				});

			},

			orderDel(item, index) {
				this.orderList.splice(index, 1);
			},

			unifiedOrder(item, index) {
				let orderList = this.orderList;
				api.orderGet(orderList[index].id).then(res => {
					this.orderList[index] = res.data;
					this.orderList = this.orderList;
				});
			}

		}
	};
</script>
<style scoped>
	.row-img {
		width: 200rpx !important;
		height: 200rpx !important;
		border-radius: 10rpx
	}

	.nav {
		top: unset !important;
	}
</style>
