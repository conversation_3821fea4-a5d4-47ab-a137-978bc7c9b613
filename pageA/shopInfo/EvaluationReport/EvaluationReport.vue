<template>
    <view class="EvaluationReport">
        <cu-custom :isBack="true" :bgColor="'bg-'+theme.backgroundColor">
            <block slot="backText">返回</block>
            <block slot="content">评价举报</block>
        </cu-custom>
        <view class="top-show-info w100 df flr jc-fs alc bc1">
            <text class="fc5">请选择您要举报的原因（经核实有问题的评价将被删除）</text>
        </view>
        <view class="sel-evaluation-box w100">
            <view class="evaluation-list w100 df flc jc-fs alc bc1">
                <block v-for="(item,index) in evaluationData" :key="index">
                    <view class="evaluation-item w100 df flr jc-sb alc">
                        <view class="item-text dfc">
                            <text class="fc5">{{ item.label }}</text>
                        </view>
                        <view class="item-pic dfc">
                            <image></image>
                        </view>
                    </view>
                </block>
            </view>
        </view>
    </view>
</template>

<script name="EvaluationReport">
const app = getApp()
export default {
    data() {
        return {
            theme: app.globalData.theme,
            evaluationData: [
                {
                    label: "广告",
                    value: 0
                },
                {
                    label: "脏话",
                    value: 1
                },
                {
                    label: "色情",
                    value: 2
                },
                {
                    label: "违法",
                    value: 3
                },
                {
                    label: "抄袭",
                    value: 4
                },
                {
                    label: "其他",
                    value: 5
                }
            ]
        }
    },
    onLoad() {
    },
    methods: {}
}
</script>

<style lang="scss" scoped>
.EvaluationReport {
    .top-show-info {
        padding: 28rpx;
        text {
            font-weight: 500;
            font-size: 26rpx;
        }
    }
    .sel-evaluation-box {
        padding: 20rpx;
        .evaluation-list {
            padding: 0 20rpx;
            border-radius: 20rpx;
            .evaluation-item {
                padding: 28rpx 15rpx;
                border-bottom: 2rpx solid #EDEDED;
                .item-text {
                    text {
                        font-size: 28rpx;
                        font-weight: 500;
                    }
                }
                .item-pic {
                    image {
                        width: 34rpx;
                        height: 34rpx;
                    }
                }
            }
            .evaluation-item {
                border-bottom: none;
            }
        }
    }
}
</style>