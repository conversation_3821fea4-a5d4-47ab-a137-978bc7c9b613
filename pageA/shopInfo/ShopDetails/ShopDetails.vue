<template>
    <view class="ShopDetails">
        <BaseNavigationBar type="2" title="店铺首页"></BaseNavigationBar>
        <view class="shop-top-con w100" :style="{ marginTop: `-${CustomBar}px`, paddingTop: `${CustomBar}px` }">
            <view class="shop-info-box w100 df flr jc-sb alc">
                <view class="info-box df flr jc-fs alc">
                    <view class="box-img dfc">
                        <image></image>
                    </view>
                    <view class="box-text df flc jcc als">
                        <view class="title dfc">
                            <text class="fwb fc5">慢病服务小店</text>
                        </view>
                        <view class="sub-title dfc">
                            <text class="fc10">已售10万+</text>
                        </view>
                    </view>
                </view>
                <view class="customer-service-box df flr jcc alc">
                    <view class="service-icon dfc">
                        <image></image>
                    </view>
                    <view class="service-text dfc">
                        <text class="fc1">客服</text>
                    </view>
                </view>
            </view>
            <view class="shop-service w100 df flr jc-fs alc">
                <view class="left-con df flr jc-fs alc">
                    <view class="left-icon dfc">
                        <image></image>
                    </view>
                    <view class="left-text dfc">
                        <text class="fc5">店铺服务</text>
                    </view>
                </view>
                <view class="right-text dfc">
                    <text class="fc10">全场包邮/七天无理由退换/48小时发货</text>
                </view>
            </view>
        </view>
        <view class="shop-message-box w100 df flc jc-fs alc">
            <view class="tabs-list w100 df flr jc-sb alc bc1">
                <block v-for="(item,index) in shopInfoData['data']" :key="index">
                    <view class="tabs-item df flc jc-fs alc" @click="selCurCon(index)">
                        <view class="item-text dfc" :class="shopInfoData['curInd'] === index?'item-texts':''">
                            <text>{{ item.label }}</text>
                        </view>
                        <view class="item-line" :class="shopInfoData['curInd'] === index?'bc2':''"></view>
                    </view>
                </block>
            </view>
            <view class="tabs-item-box tabs-item-box-one w100 df flc jc-fs alc bc1" v-if="shopInfoData['curInd'] === 0">
                <view class="qualification-info w100 df flc jc-fs alc">
                    <view class="qualification-top-con w100 df flc jc-fs alc bc3">
                        <view class="top-title-con w100 df flr jc-fs alc">
                            <view class="title-icon dfc">
                                <image></image>
                            </view>
                            <view class="title-text dfc">
                                <text class="fc5">慢病服务小店</text>
                            </view>
                        </view>
                        <view class="experience-info w100 df flr jc-fs alc">
                            <view class="experience-title dfc">
                                <text class="fc5">综合体验</text>
                            </view>
                            <uni-rate :readonly="true" size="14" :value="4" allow-half color="#DDDDDD" active-color="#FC7828" />
                        </view>
                    </view>
                    <view class="qualification-bot-con w100 df flr jc-sb alc bc4">
                        <view class="bot-text dfc">
                            <text class="fc5">店铺资质</text>
                        </view>
                        <view class="bot-right-con df flr jc-fe alc">
                            <view class="right-text dfc">
                                <text class="fc10">店铺已上传营业执照</text>
                            </view>
                            <view class="right-icon dfc">
                                <image src="@/static/public/icon/youjiantou.png"></image>
                            </view>
                        </view>
                    </view>
                </view>
                <view class="service-guarantee w100 df flc jc-fs alc">
                    <view class="service-title w100 dfc">
                        <text class="fc11">服务保障</text>
                    </view>
                    <view class="service-box-list w100 df flc jc-fs alc bc1">
                        <block v-for="(item,index) in serviceGuaranteeData" :key="index">
                            <view class="service-item w100 df flr jc-fs alc">
                                <view class="item-icon dfc">
                                    <image></image>
                                </view>
                                <view class="item-info df flc jc-fs als">
                                    <view class="info-title dfc">
                                        <text class="fc5">{{ item['label'] }}</text>
                                    </view>
                                    <view class="info-sub-title dfc">
                                        <text class="fc12">{{ item['value'] }}</text>
                                    </view>
                                </view>
                            </view>
                        </block>
                    </view>
                </view>
                <view class="store-introduction w100 df flc jc-fs alc bc4">
                    <view class="store-title w100 dfc">
                        <text class="fc5">店铺简介</text>
                    </view>
                    <u-parse :content="123"></u-parse>
                </view>
            </view>
            <view class="tabs-item-box tabs-item-box-two w100 df flc jc-fs alc bc1" v-else-if="shopInfoData['curInd'] === 1">
                <view class="goods-type-list w100 df flr jc-fs alc">
                    <block v-for="(item,index) in goodsTypeData['list']" :key="index">
                        <view class="goods-type-item df flr jc-fs alc">
                            <view class="item-text dfc">
                                <text class="fc2">{{ item.label }}</text>
                            </view>
                            <view class="item-icon dfc" v-show="item['isShowIcon']">
                                <image src="@/static/public/img/jiagefudong.png" v-if="item['type'] === 0"></image>
                                <image src="@/static/public/img/jiagefudong.png1" v-else-if="item['type'] === 1"></image>
                                <image src="@/static/public/img/jiagefudong.png2" v-else-if="item['type'] === 2"></image>
                            </view>
                        </view>
                    </block>
                </view>
                <goodsCard></goodsCard>
            </view>
            <view class="tabs-item-box tabs-item-box-thr w100 df flc jc-fs alc" v-else-if="shopInfoData['curInd'] === 2">
                <view class="class-box-list w100 df flc jc-fs alc">
                    <block v-for="(item,index) in classData" :key="index">
                        <view class="class-box-item w100 dfc">
                            <Accordion class="w100" :title="item['label']">
                                <view class="show-list">
                                    <block v-for="(ite,ind) in item['list']" :key="ind">
                                        <view class="show-item dfc">
                                            <text class="fc5">{{ ite['label'] }}</text>
                                        </view>
                                    </block>
                                </view>
                            </Accordion>
                        </view>
                    </block>
                </view>
            </view>
            <view class="tabs-item-box tabs-item-box-fou w100 df flc jc-fs alc" v-else-if="shopInfoData['curInd'] === 3">
                <view class="evaluation-class w100 df flrw jc-fs alc bc1">
                    <block v-for="(item,index) in 10" :key="index">
                        <view class="class-item dfc bc5" :class="index === 0?'class-items':''">
                            <text class="fc2">评价 <text class="fc12">1000</text></text>
                        </view>
                    </block>
                </view>
                <scroll-view class="evaluation-list-box w100">
                    <view class="evaluation-list w100 df flc jc-fs alc">
                        <block v-for="(item,index) in 4" :key="index">
                            <view class="evaluation-item w100 df flc jc-fs alc bc1">
                                <view class="item-info-show-one w100 df flr jc-fs alc">
                                    <view class="user-pic dfc">
                                        <image></image>
                                    </view>
                                    <view class="user-info df flc jc-fs als">
                                        <view class="nick-name-info df flr jc-fs alc">
                                            <view class="name-text dfc">
                                                <text class="fc2">用户昵称</text>
                                            </view>
                                            <view class="user-label dfc">
                                                <text class="fc7">该店购买了3次</text>
                                            </view>
                                        </view>
                                        <view class="user-goods-info dfc">
                                            <text class="fc14">购买5次商品名称；规格一；规格二</text>
                                        </view>
                                    </view>
                                </view>
                                <view class="item-info-show-two df flr jc-fs alc">
                                    <text class="fc2">六星好评，多一星不怕你骄傲，还在犹豫的朋友赶紧下
                                        手，良心推荐，真的很不错，性价比也很高以后还在这
                                        家买。总之：满意！满意！满意！</text>
                                </view>
                                <view class="item-info-show-thr w100">
                                    <block v-for="(ite,ind) in 6" :key="index">
                                        <view class="thr-pic w100 dfc">
                                            <image class="w100"></image>
                                        </view>
                                    </block>
                                </view>
                                <view class="item-info-show-fou w100 df flr jc-sb alc">
                                    <view class="fou-time dfc">
                                        <text class="fc12">2024-11-12</text>
                                    </view>
                                    <view class="fou-right-con df flr jc-fe alc">
                                        <BaseIconText class="icon-text-con" text="5"></BaseIconText>
                                        <BaseIconText class="icon-text-con" text="评论"></BaseIconText>
                                        <BaseClickMore class="icon-text-con" @change="selCurChange"></BaseClickMore>
                                    </view>
                                </view>
                            </view>
                        </block>
                    </view>
                </scroll-view>
            </view>
        </view>
    </view>
</template>

<script name="ShopDetails">
import BaseClickMore from "@/components/BaseClickMore/BaseClickMore.vue";
import BaseIconText from "@/components/BaseIconText/BaseIconText.vue";
import Accordion from "@/components/Accordion/Accordion.vue";
import BaseNavigationBar from "@/components/BaseNavigationBar/BaseNavigationBar.vue";
import uParse from '@/components/u-parse/u-parse.vue';
import goodsCard from "@/components/goods-card/index.vue";
import {go} from "@/utils/customUtil";

export default {
    components: {
        BaseClickMore,
        Accordion,
        uParse,
        goodsCard,
        BaseIconText,
        BaseNavigationBar
    },
    data() {
        return {
            CustomBar: this.CustomBar,
            shopInfoData: {
                curInd: 3,
                data: [
                    {
                        label: "保障",
                        value: 0
                    },
                    {
                        label: "全部商品",
                        value: 1
                    },
                    {
                        label: "分类",
                        value: 2
                    },
                    {
                        label: "评价",
                        value: 3
                    }
                ]
            },
            serviceGuaranteeData: [
                {
                    label: "全场包邮",
                    value: "所有商品包邮（偏远地区除外）"
                },
                {
                    label: "7天无理由退换货",
                    value: "满足相应条件时，消费者可申请“7天无理由退换货”"
                },
                {
                    label: "假一赔十",
                    value: "购买商品后，假一赔十"
                },
                {
                    label: "极速退款",
                    value: "购买商品后，可申请极速退款"
                }
            ],
            goodsTypeData: {
                curInd: 0,
                list: [
                    {
                        label: "默认",
                        value: 0,
                        type: 0,
                        isShowIcon: false
                    },
                    {
                        label: "上新",
                        value: 1,
                        type: 0,
                        isShowIcon: true
                    },
                    {
                        label: "销量",
                        value: 2,
                        type: 0,
                        isShowIcon: true
                    },
                    {
                        label: "价格",
                        value: 3,
                        type: 0,
                        isShowIcon: true
                    }
                ]
            },
            classData: [
                {
                    label: "特价专区（20）",
                    value: 0,
                    list: [
                        {
                            label: "子分类1",
                            value: 0
                        },
                        {
                            label: "子分类2",
                            value: 1
                        },
                        {
                            label: "子分类3",
                            value: 2
                        },
                        {
                            label: "子分类4",
                            value: 3
                        },
                        {
                            label: "子分类5",
                            value: 4
                        },
                        {
                            label: "子分类6",
                            value: 5
                        },
                        {
                            label: "子分类7",
                            value: 6
                        },
                        {
                            label: "子分类8",
                            value: 7
                        },
                        {
                            label: "子分类9",
                            value: 8
                        },
                        {
                            label: "子分类10",
                            value: 9
                        }
                    ]
                },
                {
                    label: "分类A（20）",
                    value: 1,
                    list: [
                        {
                            label: "子分类1",
                            value: 0
                        },
                        {
                            label: "子分类2",
                            value: 1
                        },
                        {
                            label: "子分类3",
                            value: 2
                        },
                        {
                            label: "子分类4",
                            value: 3
                        },
                        {
                            label: "子分类5",
                            value: 4
                        },
                        {
                            label: "子分类6",
                            value: 5
                        },
                        {
                            label: "子分类7",
                            value: 6
                        },
                        {
                            label: "子分类8",
                            value: 7
                        },
                        {
                            label: "子分类9",
                            value: 8
                        },
                        {
                            label: "子分类10",
                            value: 9
                        }
                    ]
                },
                {
                    label: "分类b（20）",
                    value: 2,
                    list: [
                        {
                            label: "子分类1",
                            value: 0
                        },
                        {
                            label: "子分类2",
                            value: 1
                        },
                        {
                            label: "子分类3",
                            value: 2
                        },
                        {
                            label: "子分类4",
                            value: 3
                        },
                        {
                            label: "子分类5",
                            value: 4
                        },
                        {
                            label: "子分类6",
                            value: 5
                        },
                        {
                            label: "子分类7",
                            value: 6
                        },
                        {
                            label: "子分类8",
                            value: 7
                        },
                        {
                            label: "子分类9",
                            value: 8
                        },
                        {
                            label: "子分类10",
                            value: 9
                        }
                    ]
                }
            ]
        }
    },
    onLoad() {
    },
    methods: {
        selCurChange() {
            go('/pageA/ShopInfo/EvaluationReport/EvaluationReport')
        },
        // 选中当前
        selCurCon(index) {
            this.shopInfoData.curInd = index;
        }
    }
}
</script>

<style lang="scss" scoped>
.ShopDetails {
    .shop-top-con {
        //height: 333rpx;
        background: linear-gradient(-30deg, #FAE3D6 0%, #FFDCC7 100%);
        padding: 20rpx;
        .shop-info-box {
            padding-top: 50rpx;
            .info-box {
                .box-img {
                    margin-right: 20rpx;
                    image {
                        width: 80rpx;
                        height: 80rpx;
                        border-radius: 50%;
                    }
                }
                .box-text {
                    .title {
                        margin-bottom: 17rpx;
                        text {
                            font-size: 30rpx;
                        }
                    }
                    .sub-title {
                        text {
                            font-size: 24rpx;
                        }
                    }
                }
            }
            .customer-service-box {
                background-color: #FF6203;
                border-radius: 50rpx;
                padding: 12rpx 24rpx;
                .service-icon {
                    margin-right: 8rpx;
                    image {
                        width: 24rpx;
                        height: 22rpx;
                    }
                }
                .service-text {
                    text {
                        font-size: 26rpx;
                    }
                }
            }
        }
        .shop-service {
            margin-top: 27rpx;
            background-color: rgba(255,255,255,.6);
            border-radius: 20rpx;
            padding: 20rpx;
            .left-con {
                margin-right: 31rpx;
                .left-icon {
                    margin-right: 11rpx;
                    image {
                        width: 27rpx;
                        height: 26rpx;
                    }
                }
                .left-text {
                    text {
                        font-size: 26rpx;
                    }
                }
            }
            .right-text {
                text {
                    font-size: 24rpx;
                }
            }
        }
    }
    .shop-message-box {
        margin-top: 20rpx;
        .tabs-list {
            padding: 20rpx 90rpx;
            border-top-left-radius: 20rpx;
            border-top-right-radius: 20rpx;
            .tabs-item {
                .item-text {
                    text {
                        font-weight: 500;
                        font-size: 28rpx;
                        color: #2A2A2A;
                    }
                }
                .item-texts {
                    text {
                        font-weight: bold;
                        font-size: 28rpx;
                        color: #FF6203;
                    }
                }
                .item-line {
                    width: 50rpx;
                    height: 4rpx;
                    margin-top: 12rpx;
                }
            }
        }
        .tabs-item-box {
            .qualification-info {
                margin: 20rpx 0;
                .qualification-top-con {
                    padding: 24rpx 20rpx;
                    border-top-left-radius: 20rpx;
                    border-top-right-radius: 20rpx;
                    .top-title-con {
                        margin-bottom: 30rpx;
                        .title-icon {
                            margin-right: 17rpx;
                            image {
                                width: 60rpx;
                                height: 60rpx;
                                border-radius: 50%;
                            }
                        }
                    }
                    .experience-info {
                        .experience-title {
                            margin-right: 30rpx;
                            text {
                                font-size: 26rpx;
                                font-weight: 500;
                            }
                        }
                    }
                }
                .qualification-bot-con {
                    padding: 22rpx 21rpx;
                    border-bottom-left-radius: 20rpx;
                    border-bottom-right-radius: 20rpx;
                    .bot-text {
                        text {
                            font-weight: 500;
                            font-size: 26rpx;
                        }
                    }
                    .bot-right-con {
                        .right-text {
                            margin-right: 21rpx;
                            text {
                                font-weight: 500;
                                font-size: 24rpx;
                            }
                        }
                        .right-icon {
                            image {
                                width: 12rpx;
                                height: 20rpx;
                            }
                        }
                    }
                }
            }
            .service-guarantee {
                padding: 20rpx;
                background: linear-gradient(0deg, #FFDE87 0%, #FFE6A3 100%);
                border-radius: 20rpx;
                .service-title {
                    margin-bottom: 28rpx;
                    text {
                        font-weight: bold;
                        font-size: 36px;
                    }
                }
                .service-box-list {
                    padding: 40rpx 30rpx;
                    border-radius: 20rpx;
                    .service-item {
                        margin-bottom: 65rpx;
                        .item-icon {
                            margin-right: 15rpx;
                            image {
                                width: 32rpx;
                                height: 36rpx;
                            }
                        }
                        .item-info {
                            .info-title {
                                text {
                                    font-weight: bold;
                                    font-size: 30rpx;
                                }
                            }
                            .info-sub-title {
                                text {
                                    font-weight: 500;
                                    font-size: 24rpx;
                                }
                            }
                        }
                    }
                    .service-item:last-child {
                        margin-bottom: 0;
                    }
                }
            }
            .store-introduction {
                border-radius: 20rpx;
                padding: 30rpx;
                margin: 20rpx 0 0 0;
                .store-title {
                    margin-bottom: 30rpx;
                    text {
                        font-weight: bold;
                        font-size: 36rpx;
                    }
                }
            }
        }
        .tabs-item-box-one {
            padding: 0 20rpx 24rpx 20rpx;
        }
        .tabs-item-box-two {
            .goods-type-list {
                padding-left: 20rpx;
                margin: 30rpx 0;
                .goods-type-item {
                    margin-right: 70rpx;
                    .item-text {
                        margin-right: 10rpx;
                        text {
                            font-weight: 500;
                            font-size: 26rpx;
                        }
                    }
                    .item-icon {
                        image {
                            width: 11rpx;
                            height: 20rpx;
                        }
                    }
                }
                .goods-type-item:last-child {
                    margin-right: 0;
                }
            }
        }
        .tabs-item-box-thr {
            padding: 20rpx;
            .class-box-list {
                .class-box-item {
                    border-radius: 20rpx;
                    margin-bottom: 20rpx;
                    .show-list {
                        display: grid;
                        grid-template-columns: repeat(2, 1fr);
                        grid-gap: 10rpx;
                        padding: 20rpx;
                        .show-item {
                            padding: 20rpx;
                            background-color: #F5F5F5;
                            border-radius: 10rpx;
                            text {
                                font-weight: 500;
                                font-size: 26rpx;
                            }
                        }
                    }
                }
                .class-box-item:last-child {
                    margin-bottom: 0;
                }
            }
        }
        .tabs-item-box-fou {
            .evaluation-class {
                margin: 20rpx 0;
                padding: 16rpx 20rpx 6rpx 20rpx;
                .class-item {
                    padding: 16rpx 18rpx;
                    border-radius: 10rpx;
                    margin: 0 10rpx 10rpx 0;
                    text {
                        font-weight: 500;
                        font-size: 26rpx;
                    }
                }
                .class-items {
                    background-color: #FFEFE5;
                    text {
                        color: #FF6203;
                    }
                }
            }
            .evaluation-list-box {
                .evaluation-list {
                    padding: 16rpx 20rpx;
                    .evaluation-item {
                        padding: 30rpx 20rpx;
                        border-radius: 20rpx;
                        margin-bottom: 16rpx;
                        .item-info-show-one {
                            .user-pic {
                                margin-right: 17rpx;
                                image {
                                    width: 80rpx;
                                    height: 80rpx;
                                    border-radius: 50%;
                                }
                            }
                            .user-info {
                                .nick-name-info {
                                    margin-bottom: 20rpx;
                                    .name-text {
                                        margin-right: 20rpx;
                                        text {
                                            font-weight: 500;
                                            font-size: 28rpx;
                                        }
                                    }
                                    .user-label {
                                        border-radius: 4rpx;
                                        border: 2rpx solid #FF6203;
                                        padding: 6rpx 8rpx;
                                        text {
                                            font-weight: 500;
                                            font-size: 22rpx;
                                        }
                                    }
                                }
                                .user-goods-info {
                                    text {
                                        font-weight: 500;
                                        font-size: 24rpx;
                                    }
                                }
                            }
                        }
                        .item-info-show-two {
                            margin: 40rpx 0 30rpx 0;
                            text {
                                font-weight: 500;
                                font-size: 28rpx;
                            }
                        }
                        .item-info-show-thr {
                            display: grid;
                            grid-template-columns: repeat(3, 1fr);
                            grid-gap: 10rpx;
                            .thr-pic {
                                image {
                                    height: 216rpx;
                                    border-radius: 10rpx;
                                }
                            }
                        }
                        .item-info-show-fou {
                            margin-top: 24rpx;
                            .fou-time {
                                text {
                                    font-weight: 500;
                                    font-size: 26rpx;
                                }
                            }
                            .fou-right-con {
                                .icon-text-con {
                                    margin-right: 60rpx;
                                }
                                .icon-text-con:last-child {
                                    margin-right: 0;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>