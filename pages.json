{
    "pages": [
        {
            "path": "pages/home/<USER>",
            "style": {
                "enablePullDownRefresh": true
            }
        },
        {
            "path": "pages/login/index"
        },
        {
            "path": "pages/login/register"
        },
        // {
        //     "path": "pages/base/search/index"
        // },
        {
            "path": "pages/goods/goods-category/index"
        },
        {
            "path": "pages/goods/goods-category/index2"
        },
        {
            "path": "pages/goods/goods-list/index"
        },
        {
            "path": "pages/goods/goods-detail/index",
            "style": {
                "navigationBarTextStyle": "black"
                // 导航栏字体颜色
            }
        },
        {
            "path": "pages/shopping-cart/index"
        },
        {
            "path": "pages/order/order-confirm/index"
        },
        {
            "path": "pages/order/order-list/index",
            "style": {
                "enablePullDownRefresh": true
            }
        },
        {
            "path": "pages/order/order-detail/index"
        },
        {
            "path": "pages/order/order-logistics/index"
        },
        {
            "path": "pages/order/order-pay-result/index"
        },
        {
            "path": "pages/user/user-center/index"
        },
        {
            "path": "pages/user/user-center/index2"
        },
        {
            "path": "pages/user/user-info/index"
        },
        {
            "path": "pages/user/user-address/list/index"
        },
        {
            "path": "pages/user/user-address/form/index"
        },
        {
            "path": "pages/user/user-collect/index"
        },
        {
            "path": "pages/user/user-footprint/index"
        },
        {
            "path": "pages/user/user-appraises/index"
        },
        {
            "path": "pages/appraises/form/index"
        },
        {
            "path": "pages/appraises/list/index"
        },
        {
            "path": "pages/order/order-refunds/form/index"
        },
        {
            "path": "pages/order/order-refunds/submit/index"
        },
        {
            "path": "pages/user/user-points-record/index"
        },
        // {
        //     "path": "pages/coupon/coupon-user-list/index",
        //     "style": {
        //         "enablePullDownRefresh": true
        //     }
        // },
        //        {
        //            "path": "pages/seckill/seckill-list/index"
        //        },
        //        {
        //            "path": "pages/seckill/seckill-detail/index",
        //            "style": {
        //                "navigationBarTextStyle": "black"
        //                // 导航栏字体颜色
        //            }
        //        },
        //        {
        //            "path": "pages/seckill/seckill-order-confirm/index"
        //        },
        //        {
        //            "path": "pages/seckill/seckill-user-list/index"
        //        },
        // {
        //     "path": "pages/coupon/coupon-list/index",
        //     "style": {
        //         "enablePullDownRefresh": true
        //     }
        // },
//        {
//            "path": "pages/bargain/bargain-list/index",
//            "style": {
//                "enablePullDownRefresh": true
//            }
//        },
//        {
//            "path": "pages/bargain/bargain-detail/index"
//        },
//        {
//            "path": "pages/bargain/bargain-user-list/index",
//            "style": {
//                "enablePullDownRefresh": true
//            }
//        },
//        {
//            "path": "pages/bargain/bargain-order-confirm/index"
//        },
        //        {
        //            "path": "pages/groupon/groupon-list/index",
        //            "style": {
        //                "enablePullDownRefresh": true
        //            }
        //        },
        //        {
        //            "path": "pages/groupon/groupon-detail/index"
        //        },
        //        {
        //            "path": "pages/groupon/groupon-user-list/index",
        //            "style": {
        //                "enablePullDownRefresh": true
        //            }
        //        },
        //        {
        //            "path": "pages/groupon/groupon-user-detail/index"
        //        },
        //        {
        //            "path": "pages/groupon/groupon-order-confirm/index"
        //        },
        {
            "path": "pages/live/room-list/index"
        },
        // {
        //     "path": "pages/shop/shop-detail/index"
        // },
        // {
        //     "path": "pages/shop/shop-list/index"
        // },
        // {
        //     "path": "pages/shop/shop-store/index"
        // },
        {
            "path": "pages/message/list/index"
        },
        {
            "path": "pages/message/chat/index"
        },
        {
            "path": "pages/customer-service/customer-service-list/index"
        },
        {
            "path": "pages/public/webview/webview"
        },
        {
            "path": "pages/article/article-list/index"
        },
        {
            "path": "pages/article/article-info/index"
        },
        // {
        //     "path": "pages/signrecord/signrecord-info/index"
        // },
        // {
        //     "path": "pages/signrecord/signrecord-list/index"
        // },
        //        {
        //            "path": "pages/distribution/distribution-center/index"
        //        },
        //        {
        //            "path": "pages/distribution/distribution-withdraw/index"
        //        },
        //        {
        //            "path": "pages/distribution/distribution-card/index"
        //        },
        //        {
        //            "path": "pages/distribution/distribution-withdraw-list/index"
        //        },
        //        {
        //            "path": "pages/distribution/distribution-withdraw-detail/index"
        //        },
        //        {
        //            "path": "pages/distribution/distribution-promotion-statistical/index"
        //        },
        //        {
        //            "path": "pages/distribution/distribution-order-list/index"
        //        },
        //        {
        //            "path": "pages/distribution/distribution-promotion-ranking/index"
        //        },
        {
            "path": "pages/pay/index"
        }
        // {
        //     "path": "pages/shop/shop-apply/index"
        // }
    ],
    "tabBar": {
        "list": [
            {
                "pagePath": "pages/home/<USER>",
                "text": "首页",
                "iconPath": "/static/public/img/icon-1/1-001.png",
                "selectedIconPath": "/static/public/img/icon-1/1-002.png"
            },
            {
                "pagePath": "pages/goods/goods-category/index",
                "text": "分类",
                "iconPath": "/static/public/img/icon-1/2-001.png",
                "selectedIconPath": "/static/public/img/icon-1/2-002.png"
            },
            {
                "pagePath": "pages/message/list/index",
                "text": "消息",
                "iconPath": "/static/public/img/icon-1/3-001.png",
                "selectedIconPath": "/static/public/img/icon-1/3-002.png"
            },
            {
                "pagePath": "pages/shopping-cart/index",
                "text": "购物车",
                "iconPath": "/static/public/img/icon-1/4-001.png",
                "selectedIconPath": "/static/public/img/icon-1/4-002.png"
            },
            {
                "pagePath": "pages/user/user-center/index2",
                "text": "我的",
                "iconPath": "/static/public/img/icon-1/5-001.png",
                "selectedIconPath": "/static/public/img/icon-1/5-002.png"
            }
        ]
    },
    "globalStyle": {
        "navigationStyle": "custom"
    },
    "condition": {
        //模式配置，仅开发期间生效
        "current": 0,
        //当前激活的模式(list 的索引项)
        "list": [
            {
                "name": "",
                //模式名称
                "path": "",
                //启动页面，必选
                "query": ""
                //启动参数，在页面的onLoad函数里面得到
            }
        ]
    },
    "subPackages": [
        {
            "root": "extraJumpPages",
            "pages": [
                {
                    "path": "ChannelRecommendation/ChannelRecommendation"
                },
                {
                    "path": "scienceVideoPage/scienceVideoList"
                },
                {
                    "path": "scienceVideoPage/scienceVideoInfo"
                },
                {
                    "path": "coupon/coupon-user-list/index",
                    "style": {
                        "enablePullDownRefresh": true
                    }
                },
                {
                    "path": "coupon/coupon-list/index",
                    "style": {
                        "enablePullDownRefresh": true
                    }
                },
                {
                    "path": "base/search/index"
                },
                {
                    "path": "signrecord/signrecord-info/index"
                },
                {
                    "path": "signrecord/signrecord-list/index"
                },
                {
                    "path": "shop/shop-apply/index"
                },
                {
                    "path": "shop/shop-detail/index"
                },
                {
                    "path": "shop/shop-list/index"
                },
                {
                    "path": "shop/shop-store/index"
                }
            ]
        },
        {
            "root": "pageA",
            "pages": [
                {
                    "path": "bargain/bargain-list/index",
                    "style": {
                        "enablePullDownRefresh": true
                    }
                },
                {
                    "path": "bargain/bargain-detail/index"
                },
                {
                    "path": "bargain/bargain-user-list/index",
                    "style": {
                        "enablePullDownRefresh": true
                    }
                },
                {
                    "path": "bargain/bargain-order-confirm/index"
                },
                {
                    "path": "groupon/groupon-list/index",
                    "style": {
                        "enablePullDownRefresh": true
                    }
                },
                {
                    "path": "groupon/groupon-detail/index"
                },
                {
                    "path": "groupon/groupon-user-list/index",
                    "style": {
                        "enablePullDownRefresh": true
                    }
                },
                {
                    "path": "groupon/groupon-user-detail/index"
                },
                {
                    "path": "groupon/groupon-order-confirm/index"
                },
                {
                    "path": "seckill/seckill-list/index"
                },
                {
                    "path": "seckill/seckill-detail/index",
                    "style": {
                        "navigationBarTextStyle": "black"
                        // 导航栏字体颜色
                    }
                },
                {
                    "path": "seckill/seckill-order-confirm/index"
                },
                {
                    "path": "seckill/seckill-user-list/index"
                },
                {
                    "path": "distribution/distribution-center/index"
                },
                {
                    "path": "distribution/distribution-withdraw/index"
                },
                {
                    "path": "distribution/distribution-card/index"
                },
                {
                    "path": "distribution/distribution-withdraw-list/index"
                },
                {
                    "path": "distribution/distribution-withdraw-detail/index"
                },
                {
                    "path": "distribution/distribution-promotion-statistical/index"
                },
                {
                    "path": "distribution/distribution-order-list/index"
                },
                {
                    "path": "distribution/distribution-promotion-ranking/index"
                },
                {
                    "path": "shopInfo/ShopDetails/ShopDetails"
                },
                {
                    "path": "shopInfo/EvaluationReport/EvaluationReport"
                },
                {
                    "path": "histroyOrder/index",
                    "style": {
                        "enablePullDownRefresh": true
                    }
                },
                {
                    "path": "histroyOrderDetail/index"
                },
                // {
                //     "path": "refundOrder/index"
                // },
                {
                    "path": "refundDetail/index"
                }
            ]
        },
		{
		    "root": "subPageFlow",
		    "pages": [
		        {
		            "path": "pages/flowIndex/index",
					"style": {
						"enablePullDownRefresh": true
					}
		        },
                {
                    "path": "pages/my/index",
                    "style": {
                        "enablePullDownRefresh": true
                    }
                },
                {
                    "path": "pages/orderDetail/index",
                    "style": {
                        "enablePullDownRefresh": true
                    }
                },
                {
                    "path": "pages/myWallet/index",
                    "style": {
                        "enablePullDownRefresh": true
                    }
                },
                {
                    "path": "pages/bill/index",
                    "style": {
                        "enablePullDownRefresh": true
                    }
                },
                {
                    "path": "pages/withdrawalSetting/index",
                    "style": {
                        "enablePullDownRefresh": false
                    }
                }
		    ]
		}
    ]
}
