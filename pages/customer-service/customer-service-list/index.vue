<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true">
			<block slot="backText">返回</block>
			<block slot="content">客服列表</block>
		</cu-custom>

		<view class="cu-list menu-avatar">
			<view @click="openPage(item)" class="cu-item" v-for="(item, index) in customerServiceData" :key="index">
				<view class="cu-avatar round lg" :style="'background-image:url('+item.avatar+');'">
					<text v-if="!item.avatar" class="cuIcon-people"></text>
				</view>
				<view class="content">
					<view>{{item.nickName?item.nickName:'客服'}}</view>
				</view>
			</view>
		</view>
		<view :class="'cu-load bg-gray ' + (loadmore?'loading':'over')"></view>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2018-2019
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	const util = require("utils/util.js");
	const app = getApp();
	import api from 'utils/api'
	import JimUtil from '@/utils/jim-util' // IM工具库

	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				loadmore: true,
				customerServiceData: [],
				params: '', // url 跳转时额外的参数
			};
		},

		components: {

		},
		props: {},

		onLoad(options) {
			// 保存别人分享来的 userCode
			util.saveSharerUserCode(options);
			let shopId = options.shopId
			if (options.goodsSpuId) {
				this.params = '&goodsSpuId=' + options.goodsSpuId;
				this.goodsSpuId = options.goodsSpuId;
			}
			if (options.orderInfoId) {
				this.params = '&orderInfoId=' + options.orderInfoId;
			}
			app.initPage().then(res => {
				this.customerServiceList(shopId)
			});
		},

		methods: {
			openPage(item) {
				const chatUserId = JimUtil.GetIMuserSerID(item);
				if (!chatUserId) {
					uni.showToast({
						title: '查找客服数据失败',
						icon: 'none'
					})
					return
				}

				uni.navigateTo({
					url: `/pages/message/chat/index?chatUserId=${chatUserId}&showName=${item.nickName}${this.params}`,
				});
			},
			customerServiceList(shopId) {
				api.customerServiceList(shopId).then(res => {
					let customerServiceData = res.data;
					this.customerServiceData = customerServiceData;
					this.loadmore = false;
				});
			}
		}
	};
</script>
<style>

</style>
