<!--
  - Copyright (C) 2020-2022
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="false">
			<block slot="backText">返回</block>
			<block slot="content">商品分类</block>
		</cu-custom>

		<view class="cu-bar search fixed" :class="'bg-'+theme.backgroundColor">
			<view class="search-form round">
				<text class="cuIcon-search"></text>
				<navigator class="response" hover-class="none" url="/extraJumpPages/base/search/index">
					<input type="text" placeholder="请输入关键字"></input>
				</navigator>
			</view>
		</view>
		<view class="verticalbox flex">
			<scroll-view class="VerticalNav nav" scroll-y scroll-with-animation :scroll-top="VerticalNavTop"
				style="height:calc(100vh - 280rpx)">
				<view :class="'cu-item ' + (index==TabCur?'cur text-'+theme.themeColor:'')"
					v-for="(item, index) in goodsCategory" :key="index" @tap="tabSelect" :data-id="index">{{item.name}}
				</view>
			</scroll-view>
			<scroll-view class="verticalmain" scroll-y scroll-with-animation style="height:calc(100vh - 280rpx)"
				:scroll-into-view="'main-' + MainCur" @scroll="VerticalMain">
				<view class="padding-tb-xs padding-lr-sm" v-for="(item, index) in goodsCategory" :key="index"
					:id="'main-' + index">
					<view class="cu-bar bg-white">
						<view class="action">
							<text class="cuIcon-titles" :class="'text-'+theme.themeColor"></text><text
								class="text-df">{{item.name}}</text>
						</view>
					</view>
					<view class="cu-bar bg-white solid-bottom">
						<view class="cate-list flex flex-wrap">
							<image v-if="item.picUrl" class="img-banner radius" :src="item.picUrl"
								@click="jumpPage(item.page)"></image>
							<view v-if="item&&item.children&&item.children.length>0" v-for="(item2, index2) in item.children" :key="index2"
								class="cate text-xs text-center">
								<navigator hover-class="none"
									:url="'/pages/goods/goods-list/index?categorySecond=' + item2.id + '&title=' + item2.name">
									<image class="cate-img"
										:src="item2.picUrl ? item2.picUrl : '/static/public/img/no_pic.png'"></image>
									<view class="cate-type text-sm">{{item2.name}}</view>
								</navigator>
							</view>
							<view class="padding response text-center" v-if="item&&!item.children">暂无数据</view>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2020-2022
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	const app = getApp();
	const util = require("utils/util.js");
	import api from 'utils/api'

	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				TabCur: 0,
				MainCur: 0,
				VerticalNavTop: 0,
				goodsCategory: [],
				load: true
			};
		},

		components: {},
		props: {},

	onLoad (options) {
			// 保存别人分享来的 userCode
			util.saveSharerUserCode(options);
			app.initPage().then(res => {
				this.goodsCategoryTree();
			});
		},

		onShow() {
			let that = this
			//获取本地参数，选中指定分类
			uni.getStorage({
				key: 'param-goods-category-index',
				success: function(res) {
					uni.removeStorageSync('param-goods-category-index'); //清空参数
					let index = res.data;
					that.tabSelect({ //选中指定分类
						currentTarget: {
							dataset: {
								id: index
							}
						}
					})
				}
			})
			//更新购物车tabar角标数量
			uni.setTabBarBadge({
				index: 3,
				text: app.globalData.shoppingCartCount + ''
			});
			this.setTabCurHandelr()
		},

		methods: {
			setTabCurHandelr () {
				if (uni.getStorageSync('param-goods-category-id')) {
				// 找到index
					const idx = this.goodsCategory.findIndex(item => item.id === uni.getStorageSync('param-goods-category-id'))
					if (idx !== -1) {
						this.tabSelect({
							currentTarget: {
								dataset: {
								id: idx
							}
							}
						})
						uni.removeStorageSync('param-goods-category-id')
					}
				}
			},
			jumpPage(url) {
				if (url) {
					uni.navigateTo({
						url: url
					});
				}
			},
			goodsCategoryTree() {
				api.goodsCategoryTree().then(res => {
					this.goodsCategory = res.data;
					console.log(this.goodsCategory, 'goodsCategory')
					this.setTabCurHandelr()
				});
			},

			tabSelect(e) {
				this.TabCur = e.currentTarget.dataset.id;
				this.MainCur = e.currentTarget.dataset.id;
				this.VerticalNavTop = (e.currentTarget.dataset.id - 1) * 50;
			},

			VerticalMain(e) {
				let that = this;
				let list = this.goodsCategory;
				let tabHeight = 0;

				if (this.load) {
					for (let i = 0; i < list.length; i++) {
						let view = uni.createSelectorQuery().select("#main-" + i);
						view.fields({
							size: true
						}, data => {
							list[i].top = tabHeight;
							tabHeight = tabHeight + data.height;
							list[i].bottom = tabHeight;
						}).exec();
					}

					that.load = false;
					that.goodsCategory = list;
				}

				let scrollTop = e.detail.scrollTop + 20;
				for (let i = 0; i < list.length; i++) {
					if (scrollTop > list[i].top && scrollTop < list[i].bottom) {

						that.VerticalNavTop = (i - 1) * 50;
						that.TabCur = i;
						return false;
					}
				}
			}

		}
	};
</script>
<style scoped>
	.nav .cu-item{
		height: auto;
		line-height: 22px;
		padding-top: 14px;
		padding-bottom: 14px;
		padding-left: 5px;
		padding-right: 5px;
	}
	
	.VerticalNav.nav {
		width: 220rpx;
		white-space: initial;
	}

	.VerticalNav.nav .cu-item {
		width: 100%;
		text-align: center;
		background-color: #fff;
		margin: 0;
		border: none;
		position: relative;
	}

	.VerticalNav.nav .cu-item.cur {
		background-color: #f1f1f1;
	}

	.VerticalNav.nav .cu-item.cur::after {
		content: "";
		width: 8rpx;
		height: 40rpx;
		border-radius: 10rpx 0 0 10rpx;
		position: absolute;
		background-color: currentColor;
		top: 0;
		right: 0rpx;
		bottom: 0;
		margin: auto;
	}

	.verticalbox {
		margin-top: 100rpx;
	}

	.search {
		top: unset !important;
	}

	.img-banner {
		width: 94%;
		height: 148rpx;
		margin: auto;
	}

	.cate-list {
		width: 100%;
	}

	.cate {
		width: 150rpx;
		margin: 15rpx;
	}

	.cate-img {
		width: 140rpx;
		height: 140rpx;
	}
</style>
