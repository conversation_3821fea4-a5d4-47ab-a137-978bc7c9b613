<template>
    <view class="index2">
        <cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="false">
            <block slot="backText">返回</block>
            <block slot="content">商品分类</block>
        </cu-custom>
        <view class="search-info w100 bc1">
            <view class="search-box w100 df flr jc-fs alc">
                <view class="search-icon dfc">
                    <text class="cuIcon-search " :class="'text-'+theme.themeColor"></text>
                </view>
                <view class="enter-con dfc">
                    <input type="text" placeholder="搜索商品"/>
                </view>
            </view>
        </view>
        <view class="shop-info-con w100 df flr jc-fs als">
            <scroll-view class="category-list" scroll-y scroll-with-animation :scroll-top="VerticalNavTop" style="height:calc(100vh - 240rpx)">
                <block v-for="(item,index) in goodsCategory" :key="index">
                    <view class="category-item w100 df flr jc-fs alc fs0" :class="index === TabCur?'category-items':''" @tap="tabSelect" :data-id="index">
                        <view class="item-icon"></view>
                        <view class="item-text w100 dfc fs0">
                            <text class="single-line-hide">{{ item.name }}</text>
                        </view>
                    </view>
                </block>
            </scroll-view>
            <view class="shop-info-box w100 df flc jc-fs alc" style="height:calc(100vh - 240rpx)">
                <view class="shop-type-con w100 df flc jc-fs alc bc1">
                    <view class="type-one-con w100 df flr jc-sb alc">
                        <block v-for="(item,index) in 4" :key="index">
                            <view class="type-one-item dfc">
                                <text>{{ item }}</text>
                            </view>
                        </block>
                    </view>
                    <view class="type-con-line w100"></view>
                    <view class="type-two-con w100 df flr jc-sb alc">
                        <block v-for="(item,index) in classTwoList" :key="index">
                            <view class="type-two-item dfc">
                                <view class="type-two-text dfc">
                                    <text>{{ item['label'] }}</text>
                                </view>
                                <view class="type-two-img df flc jcc alc" v-show="item['isShowImg']">
                                    <image src="@/static/public/img/jiagefudong.png" v-if="item['type'] === 0"></image>
                                    <image src="@/static/public/img/jiagefudong1.png" v-else-if="item['type'] === 1"></image>
                                    <image src="@/static/public/img/jiagefudong2.png" v-else-if="item['type'] === 2"></image>
                                </view>
                            </view>
                        </block>
                    </view>
                </view>
                <scroll-view class="shop-info-box-list w100" scroll-y style="height:calc(100vh - 240rpx - 210rpx)">
                    <view class="shop-list w100 df flc jc-fs alc">
                        <block v-for="(item,index) in 10" :key="index">
                            <view class="shop-item w100 bc1 df flc jc-fs alc">
                                <view class="item-img dfc">
                                    <image class="@/static/logo.png"></image>
                                </view>
                                <view class="item-info-con w100 df flc jc-fs alc">
                                    <view class="info-title w100 df flr jc-fs alc">
                                        <text></text>
                                    </view>
                                    <view class="bot-info w100 df flr jc-sb alc">
                                        <view class="bot-left-info df flc jc-fs alc">
                                            <view class="info-price dfc">
                                                <text>￥<text>199</text></text>
                                            </view>
                                            <view class="info-text dfc">
                                                <text>已售239件</text>
                                            </view>
                                        </view>
                                        <view class="bot-right-icon dfc">
                                            <image src="@/static/myImg/gouwuche.png"></image>
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </block>
                    </view>
                </scroll-view>
            </view>
        </view>
    </view>
</template>

<script name="index2">
const app = getApp();
const util = require("utils/util.js");
import api from '@/utils/api'

export default {
    data() {
        return {
            CustomBar: this.CustomBar,
            theme: app.globalData.theme, //全局颜色变量
            TabCur: 0,
            MainCur: 0,
            VerticalNavTop: 0,
            goodsCategory: [],
            load: true,
            classOneList: [],
            classTwoList: [
                {
                    label: "全部",
                    value: ""
                },
                {
                    label: "上新",
                    value: "1",
                    type: 0,
                    isShowImg: true
                },
                {
                    label: "销量",
                    value: "2",
                    type: 1,
                    isShowImg: true
                },
                {
                    label: "价格",
                    value: "3",
                    type: 2,
                    isShowImg: true
                }
            ]
        };
    },
    components: {},
    props: {},
    onLoad(options) {
        // 保存别人分享来的 userCode
        util.saveSharerUserCode(options);
        app.initPage().then(res => {
            this.goodsCategoryTree();
        });
    },

    onShow() {
        let that = this
        //获取本地参数，选中指定分类
        uni.getStorage({
            key: 'param-goods-category-index',
            success: function (res) {
                uni.removeStorageSync('param-goods-category-index'); //清空参数
                let index = res.data;
                that.tabSelect({ //选中指定分类
                    currentTarget: {
                        dataset: {
                            id: index
                        }
                    }
                })
            }
        })
        //更新购物车tabar角标数量
        uni.setTabBarBadge({
            index: 3,
            text: app.globalData.shoppingCartCount + ''
        });
    },

    methods: {
        judgingNum(str) {
            return typeof str === 'number'
        },
        jumpPage(url) {
            if (url) {
                uni.navigateTo({
                    url: url
                });
            }
        },
        goodsCategoryTree() {
            api.goodsCategoryTree().then(res => {
                this.goodsCategory = res.data;
            });
        },

        tabSelect(e) {
            this.TabCur = e.currentTarget.dataset.id;
            this.MainCur = e.currentTarget.dataset.id;
            this.VerticalNavTop = (e.currentTarget.dataset.id - 1) * 50;
        },

        VerticalMain(e) {
            let that = this;
            let list = this.goodsCategory;
            let tabHeight = 0;

            if (this.load) {
                for (let i = 0; i < list.length; i++) {
                    let view = uni.createSelectorQuery().select("#main-" + i);
                    view.fields({
                        size: true
                    }, data => {
                        list[i].top = tabHeight;
                        tabHeight = tabHeight + data.height;
                        list[i].bottom = tabHeight;
                    }).exec();
                }

                that.load = false;
                that.goodsCategory = list;
            }

            let scrollTop = e.detail.scrollTop + 20;
            for (let i = 0; i < list.length; i++) {
                if (scrollTop > list[i].top && scrollTop < list[i].bottom) {

                    that.VerticalNavTop = (i - 1) * 50;
                    that.TabCur = i;
                    return false;
                }
            }
        }

    }
};
</script>

<style lang="scss" scoped>
.index2 {
    .search-info {
        padding: 12rpx 20rpx;

        .search-box {
            padding: 18rpx 20rpx;
            border-radius: 60rpx;
            background-color: #F6F6F6;

            .search-icon {
                margin-right: 11rpx;
            }
        }

        .enter-con {
            input::placeholder {
                color: #848484;
                font-size: 28rpx;
            }
        }
    }
    .shop-info-con {
        .category-list {
            width: 220rpx;
            background-color: #FFFFFF;
            .category-item {
                padding: 32rpx 20rpx;
                position: relative;
                background-color: #FFFFFF;
                .item-icon {
                    width: 8rpx;
                    height: 40rpx;
                    position: absolute;
                    top: 32rpx;
                    left: 0;
                }
                .item-text {
                    text {
                        font-size: 28rpx;
                        color: #2A2A2A;
                        font-weight: 500;
                    }
                }
            }
            .category-items {
                background-color: #F2F2F2;
                .item-icon {
                    width: 8rpx;
                    height: 40rpx;
                    background: #FF6203;
                    border-radius: 4px;
                }
                .item-text {
                    text {
                        font-weight: bold;
                        font-size: 32rpx;
                        color: #FF6203;
                    }
                }
            }
        }
        .shop-info-box {
            padding: 16rpx;
            .shop-type-con {
                padding: 16rpx;
                border-radius: 10rpx;
                margin-bottom: 16rpx;
                .type-one-con {
                    padding: 20rpx 0;
                    .type-one-item {
                        text {
                            font-weight: 500;
                            font-size: 28rpx;
                            color: #2A2A2A;
                        }
                    }
                }
                .type-con-line {
                    height: 2rpx;
                    background-color: #F2F2F2;
                }
                .type-two-con {
                    padding: 20rpx 0;
                    .type-two-item {
                        .type-two-text {
                            margin-right: 8rpx;
                            text {
                                font-weight: 500;
                                font-size: 28rpx;
                                color: #2A2A2A;
                            }
                        }
                        .type-two-img {
                            image {
                                width: 11rpx;
                                height: 20rpx;
                            }
                        }
                    }
                }
            }
            .shop-info-box-list {
                height: 100%;
                .shop-list {
                    display: grid;
                    grid-template-columns: repeat(2, 1fr);
                    grid-gap: 16rpx;
                    .shop-item {
                        border-radius: 10rpx;
                        .item-img {
                            image {
                                width: 100%;
                                height: 276rpx;
                                border-radius: 10rpx;
                            }
                        }
                        .item-info-con {
                            border-bottom-left-radius: 10rpx;
                            border-bottom-right-radius: 10rpx;
                            .info-title {
                                text {
                                    font-weight: 500;
                                    font-size: 30rpx;
                                    color: #242424;
                                }
                            }
                            .bot-info {
                                padding: 20rpx;
                                .bot-left-info {
                                    .info-price {
                                        text {
                                            font-weight: 400;
                                            font-size: 24rpx;
                                            color: #E03F3F;
                                            text {
                                                font-weight: 400;
                                                font-size: 38rpx;
                                                color: #FF6203;
                                            }
                                        }
                                    }
                                    .info-text {
                                        text {
                                            font-weight: 500;
                                            font-size: 24rpx;
                                            color: #777777;
                                        }
                                    }
                                }
                                .bot-right-icon {
                                    image {
                                        width: 60rpx;
                                        height: 60rpx;
                                        border-radius: 50%;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>