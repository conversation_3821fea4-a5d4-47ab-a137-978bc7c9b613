<!--
  - Copyright (C) 2020-2022
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
    <view>
        <cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="false">
            <!-- 自定义的图片 -->
            <block slot="left">
                <div-base-navigator
                        v-if="pageDivData.pageComponent.topNavComponent&&pageDivData.pageComponent.topNavComponent.leftImageShow==1"
                        :pageUrl="pageDivData.pageComponent.topNavComponent.leftImagePageUrl" hover-class="none"
                        class="flex align-center justify-center">
                    <image v-if="pageDivData.pageComponent.topNavComponent.leftImageUrl"
                           :src="pageDivData.pageComponent.topNavComponent.leftImageUrl" :style="{
			                      width: `${pageDivData.pageComponent.topNavComponent.leftImageWidth}px`,
			                      height: `${pageDivData.pageComponent.topNavComponent.leftImageHeight}px`,
			                      marginLeft: `${pageDivData.pageComponent.topNavComponent.leftImageMarginLeft}px`,
			                   }"></image>
                </div-base-navigator>
            </block>
            <block slot="content">
                <view
                        :style="{color: pageDivData.pageComponent.topNavComponent&&pageDivData.pageComponent.topNavComponent.titleColor?pageDivData.pageComponent.topNavComponent.titleColor:''}">
                    {{
                        pageDivData.pageComponent.topNavComponent && pageDivData.pageComponent.topNavComponent.title ? pageDivData.pageComponent.topNavComponent.title : '首页'
                    }}
                </view>
            </block>
        </cu-custom>
        <!-- 骨架屏 -->
        <home-skeleton :loading="loading"></home-skeleton>
        <view class="bg-grey-light" v-if="!showDefaultAppPage">
            <!-- <view v-if="false"> -->
            <!-- 小程序不能用component标签所以采用下面方法自定义页面组件 -->
            <block v-for="(temp, index) in pageDivData.pageComponent.componentsList" :key="index">
                <template v-if="temp.componentName === 'searchComponent'">
                    <view>
                        <div-search v-model="temp.data"></div-search>
<!--                        <DivSearchCopy v-model="temp.data"></DivSearchCopy>-->
                    </view>
                </template>
                <template v-else-if="temp.componentName === 'imageComponent'">
                    <view>
                        <div-image v-model="temp.data"></div-image>
                    </view>
                </template>
                <template v-else-if="temp.componentName === 'swiperComponent'">
                    <view>
                        <div-swiper v-model="temp.data"></div-swiper>
                    </view>
                </template>
                <template v-else-if="temp.componentName === 'navButtonComponent'">
                    <view>
                        <div-nav-button v-model="temp.data"></div-nav-button>
                    </view>
                </template>
                <template v-else-if="temp.componentName === 'noticeComponent'">
                    <view style="padding: 10rpx;">
                        <div-notice v-model="temp.data"></div-notice>
                    </view>
                </template>
                <template v-else-if="temp.componentName === 'titleTextComponent'">
                    <view>
                        <div-title-text v-model="temp.data"></div-title-text>
                    </view>
                </template>
                <template v-else-if="temp.componentName === 'goodsComponent'">
                    <view>
                        <div-goods v-model="temp.data"></div-goods>
                    </view>
                </template>
                <template v-else-if="temp.componentName === 'goodsRowComponent'">
                    <view>
                        <div-goods-row v-model="temp.data"></div-goods-row>
                    </view>
                </template>
                <template v-else-if="temp.componentName === 'shopComponent'">
                    <view style="padding: 10rpx;">
                        <div-shop v-model="temp.data"></div-shop>
                    </view>
                </template>
                <template v-else-if="temp.componentName === 'goodsCategoryComponent'">
                    <view>
                        <div-goods-category v-model="temp.data"></div-goods-category>
<!--                        <DivGoodsCategoryCopy v-model="temp.data"></DivGoodsCategoryCopy>-->
                    </view>
                </template>
                <template v-else-if="temp.componentName === 'couponComponent'">
                    <view style="padding: 10rpx;">
                        <div-coupon v-model="temp.data"></div-coupon>
                    </view>
                </template>
                <template v-else-if="temp.componentName === 'bargainComponent'">
                    <view>
                        <div-bargain v-model="temp.data"></div-bargain>
                    </view>
                </template>
                <template v-else-if="temp.componentName === 'grouponComponent'">
                    <view>
                        <div-groupon v-model="temp.data"></div-groupon>
                    </view>
                </template>
                <template v-else-if="temp.componentName === 'seckillComponent'">
                    <view>
                        <div-seckill v-model="temp.data"></div-seckill>
                    </view>
                </template>
                <template v-else-if="temp.componentName === 'goodsNewComponent'">
                    <view style="padding: 20rpx;">
                        <div-goods-new v-model="temp.data"></div-goods-new>
                    </view>
                </template>
                <template v-else-if="temp.componentName === 'imageMultiComponent'">
                    <view>
                        <div-image-multi v-model="temp.data"></div-image-multi>
                    </view>
                </template>
                <template v-else-if="temp.componentName === 'imageHotComponent'">
                    <view>
                        <div-image-hot v-model="temp.data"></div-image-hot>
                    </view>
                </template>
                <template v-else-if="temp.componentName === 'HealthServices'">
                    <view style="padding: 20rpx;">
                        <divHealthService v-model="temp.data"></divHealthService>
                    </view>
                </template>
                <template v-else-if="temp.componentName === 'SciencePopularizationVideo'">
                    <view style="padding: 20rpx;">
                        <divSciencePopularizationVideo v-model="temp.data"
                                                       @clickInfo="scienceClickInfo"></divSciencePopularizationVideo>
                    </view>
                </template>
                <template v-else-if="temp.componentName === 'HealthEducation'">
                    <view style="padding: 20rpx;">
                        <divHealthEducation v-model="temp.data"></divHealthEducation>
                    </view>
                </template>
            </block>

            <view :class="'cu-load bg-gray ' + (loadmore3?'loading':'over')"></view>
        </view>
        <!-- 如果没有自定义页面就用自带页面 -->
        <view v-if="showDefaultAppPage">
            <view class="cu-bar search" :class="'bg-'+theme.backgroundColor">
                <view class="search-form round">
                    <text class="cuIcon-search"></text>
                    <navigator class="response" hover-class="none" url="/extraJumpPages/base/search/index">
                        <input type="text" placeholder="请输入商品名"></input>
                    </navigator>
                </view>
            </view>
            <view class="cu-bar   " :class="'bg-'+theme.backgroundColor" style="min-height: 60rpx;">
                <view class="" style="width: 90%;">
                    <scroll-view scroll-x class=" nav text-white text-sm" scroll-with-animation
                                 :scroll-left="scrollLeft">
                        <view class="cu-item " :class="index==TabCur ? ' cur text-bold text-white text-lg' : ''"
                              v-for="(item,index) in firstCategoryData" :key="index" @tap="tabSelect"
                              :data-index="index">
                            {{ item.name }}
                        </view>
                    </scroll-view>
                </view>
                <view class="action">
                    <navigator url="/pages/goods/goods-category/index" open-type="switchTab" hover-class="none"
                               class="cuIcon-moreandroid text-white"></navigator>
                </view>
            </view>
            <!-- 分类内容 -->
            <view v-if="TabCur != 0">
                <view v-if="secondCategoryData.length==0" class="text-sm text-gray text-center padding bg-white">暂无数据
                </view>
                <view v-else class="cu-list grid col-5 no-border">
                    <view class="cu-item commodity" v-for="(item, index) in secondCategoryData" :key="index"
                          v-if="index < 9">
                        <navigator
                                :url="'/pages/goods/goods-list/index?categorySecond=' + item.id + '&title=' + item.name"
                                hover-class="none">
                            <image class="img-category" :src="item.picUrl"></image>
                            <text class="text-black">{{ item.name }}</text>
                        </navigator>
                    </view>
                    <view class="cu-item more">
                        <navigator url="/pages/goods/goods-category/index" open-type="switchTab"
                                   @click="setGoodsCategoryParam()" hover-class="none"
                                   v-if="secondCategoryData.length>=9">
                            <view class="text-gray cuIcon-more"></view>
                            <text class="text-black">更多分类</text>
                        </navigator>
                    </view>
                </view>
                <view class="cu-card case">
                    <view class="cu-item">
                        <view class="image" style="height: 180rpx;">
                            <image class="img-category-banner" :src="firstCategoryData[TabCur].picUrl"
                                   @click="jumpPage(firstCategoryData[TabCur].page)"></image>
                        </view>
                    </view>
                </view>
                <view class="cu-bar justify-center bg-white margin-top-sm">
                    <view class="action border-title">
                        <text class="cuIcon-hot" :class="'text-'+theme.themeColor"></text>
                        更多热卖
                        <text :class="'bg-'+theme.themeColor" style="width:5rem"></text>
                    </view>
                </view>
                <goods-card :goodsList="goodsList2"></goods-card>
                <view :class="'cu-load bg-gray ' + (loadmore2?'loading':'over')"></view>
            </view>
            <!-- 首页内容 -->
            <!-- 新品首发 -->
            <view class="bg-white" v-if="TabCur == 0">
                <view class="wrapper bg-white margin-top-sm" v-if="goodsListNew.length > 0">
                    <view class="cu-bar bg-white">
                        <view class="text-df margin-left">
                            <text class="cuIcon-new text-bold" :class="'text-'+theme.themeColor"></text>
                            <text class="margin-left-xs">新品首发</text>
                        </view>
                        <navigator hover-class="none" url="/pages/goods/goods-list/index?type=1"
                                   class=" text-sm margin-right">更多
                            <text class="cuIcon-right"></text>
                        </navigator>
                    </view>
                    <view class="wrapper-list">
                        <scroll-view class="scroll-view_x" scroll-x style="width:auto;overflow:hidden;">
                            <block v-for="(item, index) in goodsListNew" :key="index">
                                <navigator hover-class="none" :url="'/pages/goods/goods-detail/index?id=' + item.id"
                                           class="item shadow-warp goods-card radius">
                                    <view class="img-box">
                                        <image
                                                :src="item.picUrls[0] ? item.picUrls[0] : '/static/public/img/no_pic.png'">
                                        </image>
                                    </view>
                                    <view class="text-cut goods-name margin-top-sm text-sm">{{ item.name }}</view>
                                    <view class="text-price text-red margin-left-sm margin-xs text-lg text-bold">
                                        {{ item.priceDown }}
                                    </view>
                                </navigator>
                            </block>
                        </scroll-view>
                    </view>
                </view>
                <view class="cu-list grid col-4 no-border ">
                    <view class="cu-item">
                        <navigator url="/pageA/groupon/groupon-list/index" hover-class="none">
                            <view class="lg text-blue cuIcon-group"></view>
                            <text class="text-black">拼团活动</text>
                        </navigator>
                    </view>
                    <view class="cu-item">
                        <navigator url="/pageA/bargain/bargain-list/index" hover-class="none">
                            <view class="lg text-orange cuIcon-cardboardforbid"></view>
                            <text class="text-black">砍价活动</text>
                        </navigator>
                    </view>
                    <view class="cu-item">
                        <navigator url="/pageA/seckill/seckill-list/index" hover-class="none">
                            <view class="lg text-green cuIcon-countdown"></view>
                            <text class="text-black">秒杀活动</text>
                        </navigator>
                    </view>
                    <view class="cu-item">
                        <navigator url="/extraJumpPages/coupon/coupon-list/index" hover-class="none">
                            <view class="lg text-orange cuIcon-ticket"></view>
                            <text class="text-black">领优惠券</text>
                        </navigator>
                    </view>
                </view>
                <view class="wrapper-list bg-white" v-if="shopInfoData.length > 0&&theme.showType!='2'">
                    <view class="cu-bar">
                        <view class="shop-selection text-df margin-left">
                            <text class="cuIcon-shop text-bold" :class="'text-'+theme.themeColor"></text>
                            <text class="margin-left-xs">店铺甄选</text>
                        </view>
                        <navigator hover-class="none" url="/extraJumpPages/shop/shop-list/index?type=2"
                                   class="shop-more text-sm margin-right">更多
                            <text class="cuIcon-right"></text>
                        </navigator>
                    </view>
                    <scroll-view class="scroll-view_x shop-detail" scroll-x>
                        <block v-for="(item, index) in shopInfoData" :key="index">
                            <navigator hover-class="none" :url="'/extraJumpPages/shop/shop-detail/index?id=' + item.id"
                                       class="item shadow-warp flex shop-box radius">
                                <view class="bg-mask flex shop-image radius">
                                    <image :src="item.imgUrl" class="radius"></image>
                                </view>
                                <view class="shop-information text-center">
                                    <view class="text-white enter-shop text-sm ">进店
                                        <text class="cuIcon-right"></text>
                                    </view>
                                    <view class="bg-white round enter-bg"></view>
                                </view>
                                <view class="overflow-2 text-white text-center text-xs shop-name">{{ item.name }}</view>
                            </navigator>
                        </block>
                    </scroll-view>
                </view>
            </view>
            <view class="wrapper margin-top-xs" v-if="goodsListHot.length > 0">
                <view class="cu-bar bg-image" :class="'bg-'+theme.themeColor">
                    <view class="hot-item text-df margin-left">
                        <text class="cuIcon-hot text-bold text-white"></text>
                        <text class="margin-left-xs">热销单品</text>
                    </view>
                    <navigator hover-class="none" url="/pages/goods/goods-list/index?type=2"
                               class="hot-more text-sm margin-right">更多
                        <text class="cuIcon-right"></text>
                    </navigator>
                </view>
            </view>
            <view class="wrapper-list hot-product" v-if="goodsListHot.length > 0">
                <scroll-view class="scroll-view_x" scroll-x style="width:auto;overflow:hidden;">
                    <block v-for="(item, index) in goodsListHot" :key="index">
                        <navigator hover-class="none" :url="'/pages/goods/goods-detail/index?id=' + item.id"
                                   class="item shadow-warp radius goods-card">
                            <view class="img-box">
                                <image :src="item.picUrls[0] ? item.picUrls[0] : '/static/public/img/no_pic.png'">
                                </image>
                            </view>
                            <view class="text-cut goods-name margin-top-sm text-sm">{{ item.name }}</view>
                            <view class="text-price text-red margin-left-sm margin-xs text-lg text-bold">
                                {{ item.priceDown }}
                            </view>
                        </navigator>
                    </block>
                </scroll-view>
            </view>
            <view class="cu-bar justify-center " style="min-height: 80upx;">
                <view class="action text-bold" :class="'text-'+theme.themeColor">
                    <text class="cuIcon-move"></text>
                    <text class="cuIcon-like"></text>
                    猜你喜欢
                    <text class="cuIcon-move"></text>
                </view>
            </view>
            <goods-row :goodsList="goodsList"></goods-row>
            <view :class="'cu-load bg-gray ' + (loadmore?'loading':'over')"></view>
        </view>

        <!-- 用户隐私政策授权弹框,小程序端首页不弹出 -->
        <!-- 采用uniapp自带的隐私协议才能上架成功，查看文件androidPrivacy.json -->
        <!-- #ifdef APP || MP -->
        <privacy-policy v-if="showPrivacyPolicy"></privacy-policy>
        <!-- #endif -->
    </view>
</template>

<script>
/**
 * Copyright (C) 2020-2022
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
import Vue from 'vue';
// #ifdef APP-PLUS || MP
import privacyPolicy from '@/components/privacy-policy/index';
// #endif
const app = getApp();
import api from '@/utils/api'
import homeSkeleton from "@/components/base-skeleton/page-skeleton/home-skeleton";
import goodsCard from "components/goods-card/index";
import goodsRow from "components/goods-row/index";
import shopInfo from "@/components/shop/shop-info/index";
import __config from "@/config/env";

import divSearch from "@/components/div-components/div-search/div-search.vue";
import divImage from "@/components/div-components/div-image/div-image.vue";
import divSwiper from "@/components/div-components/div-swiper/div-swiper.vue";
import divNavButton from "@/components/div-components/div-nav-button/div-nav-button.vue";
import divNotice from "@/components/div-components/div-notice/div-notice.vue";
import divTitleText from "@/components/div-components/div-title-text/div-title-text.vue";
import divGoods from "@/components/div-components/div-goods/div-goods.vue";
import divShop from "@/components/div-components/div-shop/div-shop.vue";
import divGoodsRow from "@/components/div-components/div-goods-row/div-goods-row.vue";
import divGoodsCategory from "@/components/div-components/div-goods-category/div-goods-category.vue";
import divCoupon from "@/components/div-components/div-coupon/div-coupon.vue";
import divBargain from "@/components/div-components/div-bargain/div-bargain.vue";
import divGroupon from "@/components/div-components/div-grouponinfo/div-grouponinfo.vue";
import divSeckill from "@/components/div-components/div-seckill/div-seckill.vue";
import divGoodsNew from "@/components/div-components/div-goods-new/div-goods-new.vue";
import divImageMulti from "@/components/div-components/div-image-multi/div-image-multi.vue";
import divBaseNavigator from "@/components/div-components/div-base/div-base-navigator";
import divImageHot from "@/components/div-components/div-image-hot/div-image-hot.vue";

import DivSearchCopy from "@/components/div-components/DivSearchCopy/DivSearchCopy.vue";
import DivGoodsCategoryCopy from "@/components/div-components/DivGoodsCategoryCopy/DivGoodsCategoryCopy.vue";

import divHealthService from '@/components/div-components/div-healthService/div-healthService.vue';
import divSciencePopularizationVideo
    from '@/components/div-components/div-sciencePopularizationVideo/div-sciencePopularizationVideo.vue';
import divHealthEducation from '@/components/div-components/div-healthEducation/div-healthEducation.vue';

const util = require("utils/util.js");
import JimUtil from '@/utils/jim-util' // IM工具库
import {go} from "@/utils/customUtil";
export default {
    components: {
        divHealthService,
        divSciencePopularizationVideo,
        divHealthEducation,
        DivSearchCopy,
        DivGoodsCategoryCopy,

        // #ifdef APP-PLUS || MP
        privacyPolicy,
        // #endif
        homeSkeleton,
        goodsCard,
        goodsRow,
        shopInfo,
        divSearch,
        divImage,
        divSwiper,
        divNavButton,
        divNotice,
        divTitleText,
        divGoods,
        divShop,
        divGoodsRow,
        divGoodsCategory,
        divCoupon,
        divBargain,
        divGroupon,
        divSeckill,
        divGoodsNew,
        divImageMulti,
        divBaseNavigator,
        divImageHot
    },
    data() {
        return {
            loading: true,
            showPrivacyPolicy: __config.showPrivacyPolicy,
            theme: app.globalData.theme, //全局颜色变量
            CustomBar: this.CustomBar,
            page: {
                searchCount: false,
                current: 1,
                size: 10
            },
            showDefaultAppPage: false,
            loadmore: true,
            pageDivData: {
                pageComponent: {
                    topNavComponent: {},
                    componentsList: []
                }
            }, //首页自定义配置组件的数据
            goodsList: [],
            goodsListNew: [],
            goodsListHot: [],
            scrollLeft: 0,
            cardCur: 0,
            shopInfoData: [],
            TabCur: 0,
            firstCategoryData: [{
                id: '0',
                name: '首页'
            }],
            secondCategoryData: [],
            page2: {
                searchCount: false,
                current: 1,
                size: 10
            },
            loadmore2: true,
            goodsList2: [],
            loadmore3: true, // 自定义页面的加载状态
        };
    },

    props: {},
    onLoad(options) {
        // 2监听联网状态
        let that = this
        uni.onNetworkStatusChange(function (res) {
            // console.log(res.isConnected);
            // console.log(res.networkType);
            if (res.isConnected) { //网络连接后自动刷新
                app.globalData.hasNetwork = true
                app.setGlobalStyle()
                that.refresh()
            } else {
                app.globalData.hasNetwork = false
            }
        });
        // 保存别人分享来的 userCode
        util.saveSharerUserCode(options);
        // 优先显示获取缓存本地租户的装修数据
        let pageDivData = uni.getStorageSync('home_div_page_' + app.globalData.tenantId);
        if (pageDivData) {
            this.showDefaultAppPage = false;
            this.pageDivData = JSON.parse(pageDivData)
        }
        app.initPage().then(res => {
            this.loadData()
        })
    },
    onShow() {
        //更新购物车tabar角标数量
        uni.setTabBarBadge({
            index: 3,
            text: app.globalData.shoppingCartCount + ''
        });
        // 更新消息消息未读数
        JimUtil.getJIMUnreadMsgCnt()
        this.$nextTick(function () {
            let pagesStr = uni.getStorageSync("jump_im_connect_page")
            if (pagesStr) {
                console.log('<<<----------JIM重连页面还原---------->>>')
                // 如果是IM重连的就跳转到im重连之前的页面
                let pages = JSON.parse(pagesStr)
                pages.map((url) => {
                    setTimeout(() => {
                        uni.navigateTo({
                            url: url,
                            fail: (res) => {
                                // 	"errMsg": "navigateTo:fail can not navigateTo a tabbar page"
                                console.log('失败了，改为switchTab：', res);
                                uni.switchTab({
                                    url: url,
                                    fail: (res) => {
                                        console.log('switchTab失败了：',
                                                res);
                                    }
                                })
                            }
                        })
                    }, 1000)
                })
                uni.removeStorageSync("jump_im_connect_page") //移除保存的im跳转之前的页面
            }
        })
    },

    onShareAppMessage: function () {
        let title = 'JooLun商城源码-小程序演示';
        let path = 'pages/home/<USER>'
        // let path = util.getCurPage(getCurrentPages());
        const userInfo = uni.getStorageSync('user_info')
        const userCode = userInfo ? '?sharer_user_code=' + userInfo.userCode : ''
        path = path + userCode;
        return {
            title: this.pageDivData.pageName || title,
            path: path,
            imageUrl: this.pageDivData.pageImg || '',
            desc: this.pageDivData.pageDesc || '',
            success: function (res) {
                if (res.errMsg == 'shareAppMessage:ok') {
                    console.log(res.errMsg);
                }
            },
            fail: function (res) { // 转发失败
            }
        };
    },

    onPullDownRefresh() {
        // 显示顶部刷新图标
        uni.showNavigationBarLoading();
        this.refresh(); // 隐藏导航栏加载框
        uni.hideNavigationBarLoading(); // 停止下拉动作
        uni.stopPullDownRefresh();
    },

    onReachBottom() {
        if (this.showDefaultAppPage) {
            if (this.TabCur === 0 && this.loadmore) {
                this.page.current = this.page.current + 1;
                this.goodsPage();
            }
            if (this.TabCur == !0 && this.loadmore2) {
                this.page2.current = this.page2.current + 1;
                this.goodsPageByCateGory();
            }
        }
    },

    methods: {
        // 科普视频点击详情
        scienceClickInfo(data) {
            go('/extraJumpPages/scienceVideoPage/scienceVideoList')
        },
        loadData() {
            if (!app.globalData.hasNetwork) {
                return
            }
            this.loadmore3 = true;
            // if(this.loading)
            api.pagedevise(1).then(res => {
                let pageDivData = res.data;
                if (pageDivData) {
                    // 初始化为空数据
                    this.loading = true
                    this.pageDivData = {
                        pageComponent: {
                            componentsList: [],
                            topNavComponent: {}
                        }
                    }
                    if (!pageDivData || !pageDivData.pageComponent || pageDivData.pageComponent.componentsList
                            .length == 0) {
                        // 如果没有设置自定义页面数据，那就显示默认原始页面
                        this.getDefaultPageData();
                        uni.removeStorage({
                            key: 'home_div_page_' + app.globalData.tenantId,
                        });
                        this.loading = false
                    } else {
                        this.showDefaultAppPage = false;
                        this.loadmore3 = false;
                        this.$nextTick(() => {
                            this.pageDivData = pageDivData;
                            Vue.prototype.$homeDivPageLoad = true
                            this.loading = false
                        })
                        // 缓存到本地，方便下次进入时获取加载
                        uni.setStorage({
                            key: 'home_div_page_' + app.globalData.tenantId,
                            data: JSON.stringify(pageDivData)
                        });
                    }
                } else {
                    // 如果没有设置自定义页面数据，那就显示默认原始页面
                    this.getDefaultPageData();
                    this.loading = false
                    uni.removeStorage({
                        key: 'home_div_page_' + app.globalData.tenantId,
                    });
                }
            }).catch(e => {
                // 如果没有设置自定义页面数据，那就显示默认原始页面
                let pageDivData = uni.getStorageSync('home_div_page_' + app.globalData.tenantId);
                if (!pageDivData) {
                    this.showDefaultAppPage = true
                    this.getDefaultPageData();
                    uni.removeStorage({
                        key: 'home_div_page_' + app.globalData.tenantId,
                    });
                }
                this.loadmore3 = false
                this.loading = false
            });

        },
        getDefaultPageData() {
            this.showDefaultAppPage = true;
            this.goodsCategoryPage({
                searchCount: false,
                current: 1,
                size: 100,
                ascs: 'sort',
                parentId: '0'
            });
            this.shopInfoPage();
            this.goodsNew();
            this.goodsHot();
            this.goodsPage();

        },
        tabSelect(e) {
            let index = e.currentTarget.dataset.index
            let TabCur = this.TabCur
            let firstCategory = this.firstCategoryData[index]
            this.TabCur = index;
            this.scrollLeft = (index - 1) * 60
            if (index != 0 && TabCur != index) {
                this.secondCategoryData = firstCategory.children || [];
                this.loadmore2 = true;
                this.goodsList2 = [];
                this.page2.current = 1;
                this.goodsPageByCateGory();
            }
        },
        setGoodsCategoryParam() {
            /* 把参数信息异步存储到缓存当中 */
            uni.setStorage({
                key: 'param-goods-category-index',
                data: this.TabCur - 1
            });
        },

        cardSwiper(e) {
            this.cardCur = e.detail.current
        },
        //商品分类
        goodsCategoryPage() {
            api.goodsCategoryTree().then(res => {
                this.firstCategoryData = [...this.firstCategoryData, ...res.data];
            });
        },
        //店铺列表
        shopInfoPage() {
            api.shopInfoPage({
                searchCount: false,
                current: 1,
                size: 5,
                descs: ''
            }).then(res => {
                this.shopInfoData = res.data.records;
            });
        },

        //新品首发
        goodsNew() {
            api.goodsPage({
                searchCount: false,
                current: 1,
                size: 5,
                descs: 'create_time'
            }).then(res => {
                this.goodsListNew = res.data.records;
            });
        },

        //热销单品
        goodsHot() {
            api.goodsPage({
                searchCount: false,
                current: 1,
                size: 5,
                descs: 'sale_num'
            }).then(res => {
                this.goodsListHot = res.data.records;
            });
        },

        goodsPage() {
            api.goodsPage(this.page).then(res => {
                let goodsList = res.data.records
                this.goodsList = [...this.goodsList, ...goodsList];
                if (goodsList.length < this.page.size) {
                    this.loadmore = false;
                }
            })
        },

        goodsPageByCateGory() {
            api.goodsPage(Object.assign(this.page2, {
                categoryFirst: this.firstCategoryData[this.TabCur].id
            })).then(res => {
                let goodsList2 = res.data.records
                this.goodsList2 = [...this.goodsList2, ...goodsList2];
                if (goodsList2.length < this.page2.size) {
                    this.loadmore2 = false;
                }
            });
        },
        refresh() {
            this.loadmore = true;
            this.page.current = 1;
            this.goodsList = [];
            this.goodsListNew = [];
            this.goodsListHot = [];
            this.loadData();
        },
        jumpPage(page) {
            if (page) {
                uni.navigateTo({
                    url: page
                });
            }
        },

    }
};
</script>
<style>
@import "./index.css";
</style>
