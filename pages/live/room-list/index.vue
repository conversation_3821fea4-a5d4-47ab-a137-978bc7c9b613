<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true">
			<block slot="backText">返回</block>
			<block slot="content">直播房间列表</block>
		</cu-custom>
		<view class="live-container flex">
			<view class="live-box radius" v-for="(item, index) in roomList" :key="index">
				<navigator class="case" :url="'plugin-private://wx2b03c6e691cd7370/pages/live-player-plugin?room_id=' + item.roomId">
					<view :style="'background-image:url(' + item.coverImg + ');'" class="cu-item live-image radius">
						<view class="radius text-white tittle-bg">
							<view class="flex align-center margin-left-sm">
								<view class="cu-avatar round sm margin-top-sm" :style="'background-image:url(' + item.feedsImg + ');'"></view>
								<view class="text-sm margin-left-xs overflow-1 margin-top-sm text-shadow">{{item.anchorName}}</view>
							</view>
							<text class="text-cut text-sm overflow-2 margin-top-xs margin-left-sm text-shadow">{{item.name}}</text>
							<view class="cu-tag bg-red" v-if="item.liveStatus == 101"><text class="cuIcon-playfill"></text>正在直播</view>
						</view>
					</view>
				</navigator>
			</view>
		</view>
		<view :class="'cu-load bg-gray ' + (roomList.length>0?'':'over')"></view>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2018-2019
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	const util = require("utils/util.js");
	const app = getApp();
	import api from 'utils/api'

	export default {
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				CustomBar: this.CustomBar,
				roomList: []
			};
		},

		components: {},
		props: {},

		onLoad(options) {
			// 保存别人分享来的 userCode
			util.saveSharerUserCode(options);
			app.initPage().then(res => {
				this.liveRoomInfoList();
			});
		},

		methods: {
			liveRoomInfoList() {
				api.liveRoomInfoList().then(res => {
					this.roomList = res.data;
				});
			}

		}
	};
</script>
<style>
	.live-container {
		justify-content: space-between;
		flex-wrap: wrap;
		box-sizing: content-box;
		padding: 20rpx;
	}

	.live-box {
		width: 349rpx;
		background-color: #fff;
		overflow: hidden;
		margin-bottom: 20rpx;
	}

	.live-image {
		width: 349rpx;
		height: 460rpx;
		background-size: 349rpx 460rpx;
		background-repeat: no-repeat;
	}
	
	.tittle-bg{
		background-image: -webkit-gradient(linear, 0 0, 0 bottom, from(rgba(0, 0, 0, 0.5)), to(rgba(51, 51, 51, 0)));
	}
	
	.text-shadow{
		text-shadow: 0px 0px 1px #444 !important;
	}
</style>
