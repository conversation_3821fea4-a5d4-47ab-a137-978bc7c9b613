<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true">
			<block slot="backText">返回</block>
			<block slot="content">登录</block>
		</cu-custom>
		<view class="nav text-center login padding-top" style="" :class="'bg-'+theme.backgroundColor">
			<view :class="'cu-item '+ (1==TabCur?'text-white cur':'')" @click="tabSelect(1)" class="quick-login">
				<text class="">快速登录</text>
			</view>
			<view :class="'cu-item '+ (0==TabCur?'text-white cur':'')" @click="tabSelect(0)">
				<text class="">账号登录</text>
			</view>
		</view>
		<userLogin v-if="TabCur===0" :reUrl="reUrl"></userLogin>
		<codeLogin v-if="TabCur===1" :reUrl="reUrl"></codeLogin>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2018-2019
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	const app = getApp();
	const util = require("utils/util.js");
	import api from 'utils/api';
	import userLogin from "./userlogin";
	import codeLogin from "./codelogin";
	import register from "./register";
	
	export default {
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				CustomBar: this.CustomBar,
				TabCur: 1,
				reUrl: ''
			};
		},
		components: {
			userLogin,
			codeLogin,
			register
		},
		props: {
			
		},
		onLoad(options) {
			// 保存别人分享来的 userCode
			util.saveSharerUserCode(options);
			let reUrl = options.reUrl
			this.reUrl = reUrl
		},

		onShow() {
			// #ifdef MP-WEIXIN
			app.doLogin()
			// #endif
		},

		methods: {
			tabSelect(index){
				this.TabCur = index
			}
		}
	};
</script>
<style>
	.quick-login{
		margin-right: 150rpx !important;
	}
	
	.login{
		margin-left: -20rpx;
	}
</style>
