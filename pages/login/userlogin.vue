<!--
  - Copyright (C) 2020-2022
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view>
		<form @submit="loginSub">
			<view class="cu-form-group margin-top">
				<view class="title">号码</view>
				<input placeholder="请输入手机号" name="phone" v-model="form.phone"></input>
			</view>
			<view class="cu-form-group">
				<view class="title">密码</view>
				<input placeholder="请输入密码" password name="password" v-model="form.password"></input>
			</view>
			<view class="text-right text-gray margin-right margin-top cuIcon-question" @click="toReset">
				忘记密码
			</view>
			<view class="padding flex flex-direction">
				<button class="cu-btn margin-tb-sm lg round" :class="'bg-'+theme.backgroundColor"
					form-type="submit">立即登录</button>
				<view class="margin-top flex justify-center text-sm align-center" v-show="showPrivacyPolicy">
					<!-- #ifdef APP-PLUS || MP -->
					<checkbox-group @change="checkedChange">
						<checkbox class="red round  margin-right-xs"
							:class="protocolCheck?theme.themeColor+' checked':''" value="true" :checked="protocolCheck">
						</checkbox>
					</checkbox-group>
					<!-- #endif -->
					<text>登录即代表您同意</text>
					<navigator class="text-blue text-sm"
						:url="'/pages/public/webview/webview?title=用户协议&url='+protocolUrl">{{' 用户协议 '}}</navigator>和
					<navigator class="text-blue text-sm  "
						:url="'/pages/public/webview/webview?title=隐私政策&url='+privacyPolicyUrl">{{' 隐私政策'}}</navigator>
				</view>
			</view>
			<view class="text-center">
				没有账号？<text class="text-red" @click="toRegister">立即注册</text>
			</view>
		</form>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2020-2022
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	const app = getApp();
	import api from 'utils/api'
	import validate from 'utils/validate'
	import __config from '@/config/env'; // 配置文件

	export default {
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				CustomBar: this.CustomBar,
				form: {},
				showPrivacyPolicy: __config.showPrivacyPolicy,
				privacyPolicyUrl: __config.privacyPolicyUrl,
				protocolUrl: __config.protocolUrl,
				protocolCheck: !__config.showPrivacyPolicy
			};
		},
		props: {
			reUrl: { //重定向页面
				type: String,
				default: '/pages/home/<USER>'
			}
		},
		methods: {
			checkedChange() {
				this.protocolCheck = !this.protocolCheck
			},
			toReset() {
				uni.redirectTo({
					url: '/pages/login/register?type=reset'
				});
			},
			toRegister() {
				uni.redirectTo({
					url: '/pages/login/register'
				});
			},
			loginSub(e) {
				// #ifdef APP-PLUS || MP 
				if (!this.protocolCheck) {
					uni.showToast({
						title: '请先同意用户协议和隐私政策',
						icon: 'none',
						duration: 3000
					});
					return;
				}
				// #endif
				if (!validate.validateMobile(this.form.phone)) {
					uni.showToast({
						title: '请输入正确的手机号码',
						icon: 'none',
						duration: 3000
					});
					return;
				}
				if (!this.form.password) {
					uni.showToast({
						title: '请输入密码',
						icon: 'none',
						duration: 3000
					});
					return;
				}
				api.login(this.form).then(res => {
					let userInfo = res.data;
					uni.setStorageSync('third_session', userInfo.thirdSession);
					uni.setStorageSync('user_info', userInfo);
					//登录完成跳到首页
					uni.reLaunch({
						url: this.reUrl ? decodeURIComponent(this.reUrl) : '/pages/home/<USER>'
					});
					//获取购物车数量
					app.shoppingCartCount()
				});
			}
		}
	};
</script>
<style>

</style>
