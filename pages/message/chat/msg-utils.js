/**
 * Copyright (C) 2024
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
/**
 * 创建IM消息统一管理
 * OpenIM 文档:https://open-im-docs.vercel.app/#/?id=%e6%88%91%e4%bb%ac%e7%9a%84%e4%bd%bf%e5%91%bd
 */
/**
 * 消息类型说明: /libs/public/open-im-sdk/types/index.d.ts
 * 文本 = 101,
 * 图片 = 102,
 * 视频 = 104,
 * 文件 = 105,
 * 自定义 = 110,
 */
import JimUtil from "@/utils/jim-util"; // IM工具库
import api from "@/utils/api";
// import type { VideoMsgParams } from "@/libs/public/open-im-sdk/types/index.d.ts";

/**
 * 将会话消息标记为已读
 * @param {会话Id} chatUserId
 */
function markMsgRead(JIM, chatUserId) {
  //设置消息已读，更新会话未读数数据 https://doc.rentsoft.cn/#/js_v2/sdk_integrate/message?id=markc2cmessageasread
  const options = {
    userID: chatUserId,
    msgIDList: [],
  };
  JIM.markC2CMessageAsRead(options)
    .then((r) => {})
    .catch((err) => {});
}
/**
 *  发送消息
 * 通过createImageMessage、createSoundMessage、createVideoMessage、createFileMessage方法创建的消息必须通过sendMessageNotOss进行发送。
 * @param {IM} JIM
 * @param {*} message
 * @param {*} file
 * @returns
 */
function sendMessage(JIM, message, file = false) {
  return new Promise((resolve, reject) => {
    if (file) {
      JIM.sendMessageNotOss(message)
        .then(({ data, errCode }) => {
          resolve(data);
        })
        .catch((err) => {
          if (err.errCode == 301) {
            uni.showToast({
              title: "发送消息失败，未找到客服",
              icon: "none",
            });
          } else {
            uni.showToast({
              title: "发送消息失败",
              icon: "none",
            });
          }
          reject();
        });
    } else {
      JIM.sendMessage(message)
        .then(({ data, errCode }) => {
          resolve(data);
        })
        .catch((err) => {
          if (err.errCode == 301) {
            uni.showToast({
              title: "发送消息失败，未找到客服",
              icon: "none",
            });
          } else {
            uni.showToast({
              title: "发送消息失败",
              icon: "none",
            });
          }
          reject();
        });
    }
  });
}
/**
 * 发送文本消息
 * @param {IM} JIM 对象,传入避免获取失败
 * @param {recvID} 接收人
 * @param {content} 消息内容
 */
function sendMsgText(JIM, recvID, content) {
  return new Promise((resolve, reject) => {
    JIM.createTextMessage(content)
      .then(({ data }) => {
        let message = {
          recvID: recvID,
          groupID: "",
          offlinePushInfo: "", //离线推送配置
          message: data,
        };
        sendMessage(JIM, message)
          .then((data) => {
            resolve(data);
          })
          .catch((err) => {
            reject();
          });
      })
      .catch((err) => {
        uni.showToast({
          title: "创建消息失败",
          icon: "none",
        });
        reject(err);
      });
  });
}
/**
 * 发送 图片 消息
 * @param {IM} JIM 对象
 * @param {recvID} 接收人
 * @param {file} 本地文件对象 { path:string, size:number}
 */
function sendMsgImage(JIM, recvID, file) {
  return new Promise((resolve, reject) => {
    // 如果是其他文件可以用 uni.getFileInfo getImageInfo
    // 上传文件接口
    api.uploadFile(file.path, "im/image", "image")
      .then((link) => {
        let baseInfo = {
          uuid: JimUtil.chatMsgUUID(),
          type: getFileType(link),
          width: 100,
          height: 100,
          size: file.size,
          url: link,
        };
        const options = {
          sourcePicture: baseInfo,
          bigPicture: baseInfo,
          snapshotPicture: baseInfo,
        };
        JIM.createImageMessage(options)
          .then(({ data }) => {
            let message = {
              recvID: recvID,
              groupID: "",
              message: data,
            };
            sendMessage(JIM, message, true)
              .then((data) => {
                resolve(data);
              })
              .catch((err) => {
                reject(err);
              });
          })
          .catch((err) => {
            uni.showToast({
              title: "创建消息失败",
              icon: "none",
            });
            reject(err);
          });
      })
      .catch((err) => {
        reject(err);
      });
  });
}
/**
 * 发送 视频 消息
 * @param {IM} JIM 对象
 * @param {recvID} 接收人
 * @param {file} 本地文件对象 { path:string, size:number}
 */
function sendMsgVideo(JIM, recvID, file) {
  return new Promise((resolve, reject) => {
    // 上传文件
    api.uploadFile(file.tempFilePath, "im/video", "video")
      .then((link) => {
        let option = {
          videoPath: link,
          videoType: getFileType(link),
          duration: file.duration,
          snapshotPath: link,
          videoUUID: JimUtil.chatMsgUUID(),
          videoUrl: link,
          videoSize: file.size || 0,
        };
        JIM.createVideoMessage(option).then(({ data }) => {
          let message = {
            recvID: recvID,
            groupID: "",
            message: data,
          };
          sendMessage(JIM, message, true)
            .then((data) => {
              resolve(data);
            })
            .catch((err) => {
              reject(err);
            });
        });
      })
      .catch((err) => {
        reject(err);
      });
  });
}
/**
 * 发送 自定义 消息
 * @param {IM} JIM 对象
 * @param {recvID} 接收人
 * @param {content} 自定义的消息内容对象
 */
function sendMsgCustom(JIM, recvID, content) {
  return new Promise((resolve, reject) => {
    //自定义消息：https://doc.rentsoft.cn/#/js_v2/sdk_integrate/message?id=createcustommessage
    const options = {
      data: JSON.stringify(content),
      extension: "",
      description: "",
    };
    JIM.createCustomMessage(options)
      .then(({ data }) => {
        let message = {
          recvID: recvID,
          groupID: "",
          message: data,
        };
        sendMessage(JIM, message)
          .then((data) => {
            resolve(data);
          })
          .catch((err) => {
            reject();
          });
      })
      .catch((err) => {
        uni.showToast({
          title: "创建消息失败",
          icon: "none",
        });
        reject(err);
      });
  });
}
/**
 * 根据名称结尾获取文件类型 如 aa.png 那么返回 png
 * @param {文件名称} name
 * @returns  文件类型
 */
const getFileType = (name) => {
  const idx = name.lastIndexOf(".");
  return name.slice(idx + 1);
};
export default { sendMsgText, sendMsgImage, sendMsgVideo, sendMsgCustom, markMsgRead };
