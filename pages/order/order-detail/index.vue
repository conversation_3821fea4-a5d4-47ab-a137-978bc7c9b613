<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true">
			<block slot="backText">返回</block>
			<block slot="content">订单详情</block>
		</cu-custom>
		<view class="margin-bottom-bar" v-if="orderInfo != null">
			<view class="bg-white padding">
				<view
					:class="'text-red text-bold text-xl cuIcon-' + (orderInfo.status == null && orderInfo.isPay == '0' ? 'pay' : orderInfo.status == '1' ? 'send' : orderInfo.status == '2' ? 'deliver' : orderInfo.status == '3' ? 'evaluate' : orderInfo.status == '4' ? 'upstage' : orderInfo.status == '5' ? 'roundclose' : '')">
					<text class="margin-left-xs">{{ orderInfo.deliveryWay == 3 && orderInfo.statusDesc == '待收货' ? '待上门' : orderInfo.statusDesc}}</text></view>
				<view class="text-gray text-sm margin-top-xs" v-if="orderInfo.isPay == '0' && !orderInfo.status">
					请在<count-down :outTime="1000 * orderInfo.outTime"
						@countDownDone="countDownDone"></count-down>内付款，超时订单将自动取消</view>
				<view class="text-gray text-sm margin-top-xs" v-if="orderInfo.status == '2' && orderInfo.deliveryWay != 3">还剩<count-down
						:outTime="1000 * orderInfo.outTime" @countDownDone="countDownDone"></count-down>自动确认</view>
			</view>
			<view class="cu-list cu-card no-card menu-avatar margin-top-xs">
				<navigator class="cu-item address-bg"
					:url="'/pages/order/order-logistics/index?id=' + orderInfo.orderLogistics.id"
					v-if="orderInfo.deliveryWay == '1' && orderInfo.deliveryType == '1' && (orderInfo.status == '2' || orderInfo.status == '3' || orderInfo.status == '4')">
					<view class="cu-avatar round cuIcon-deliver_fill bg-red align-center"></view>
					<view class="loc-content align-center">
						<view class="flex align-center">
							<view class="text-blue">{{orderInfo.orderLogistics.statusDesc}}</view>
							<view class="text-gray text-sm margin-left-sm">{{orderInfo.orderLogistics.logisticsDesc}}
							</view>
							<view class="cuIcon-right text-gray text-xs"></view>
						</view>
						<view class="text-gray text-sm overflow-2 loc-info padding-right-sm"
							v-if="orderInfo.orderLogistics.message">{{orderInfo.orderLogistics.message}}</view>
					</view>
				</navigator>
				<view class="cu-item address-bg" v-if="orderInfo.deliveryWay == '1' && orderInfo.orderLogistics">
					<view class="cu-avatar round cuIcon-locationfill bg-orange align-center"></view>
					<view class="loc-content align-center">
						<view class="flex align-center">
							<view class="text-black">{{orderInfo.orderLogistics.userName}}</view>
							<view class="text-gray text-sm margin-left-sm">{{orderInfo.orderLogistics.telNum}}</view>
						</view>
						<view class="text-gray text-sm overflow-2 loc-info">{{orderInfo.orderLogistics.address}}</view>
					</view>
				</view>
				<view class="bg-white" v-if="orderInfo.deliveryWay == '2'">
					<shop-store-info :storeId="orderInfo.storeId" />
				</view>
			</view>
			<view class="cu-card no-card article margin-top-xs">
				<view class="cu-item ">
					<navigator v-if="theme.showType!='2'" class="padding-lr padding-top padding-bottom-sm flex align-center"
						hover-class="none" :url="'/extraJumpPages/shop/shop-detail/index?id=' + orderInfo.shopInfo.id">
						<view class="cu-avatar sm radius margin-right-xs"
							:style="'background-image:url(' + orderInfo.shopInfo.imgUrl + ')'"></view>
						<view class="cu-tag bg-red light sm radius margin-right-xs" v-if="orderInfo.shopInfo.saleType == 2"> 自营 </view>
						<text class="text-df">{{orderInfo.shopInfo.name}}</text>
					</navigator>
					<view class="cu-list menu">
						<view v-for="(item, index) in orderInfo.listOrderItem" :key="index">
							<navigator hover-class="none" :url="'/pages/goods/goods-detail/index?id=' + item.spuId"
								class="cu-item">
								<view class="flex margin-top-sm">
									<view class="content response align-center">
										<image :src="item.picUrl ? item.picUrl : '/static/public/img/no_pic.png'"
											mode="aspectFill" class="row-img"></image>
										<view class="desc row-info block">
											<view class="text-black text-sm overflow-2">{{item.spuName}}</view>
											<view class="text-gray margin-top-xs text-sm overflow-2"
												v-if="item.specInfo">{{item.specInfo}}</view>
											<view class="flex justify-between align-center margin-bottom-xs">
												<view class="text-price text-gray text-sm ">{{item.salesPrice}}</view>
												<view class="text-gray text-sm ">x{{item.quantity}}</view>
											</view>
											<view v-if="item.paymentCouponPrice" class="text-red text-sm ">优惠金额 <text
													class="margin-left-xs text-price  text-red ">{{item.paymentCouponPrice}}</text>
											</view>
											<view v-if="item.paymentPointsPrice" class="text-red text-sm ">积分抵扣 <text
													class="margin-left-xs text-price  text-red ">{{item.paymentPointsPrice}}</text>
											</view>
											<view class="text-red text-sm ">支付金额 <text
													class="margin-left-xs text-price  text-red ">{{item.paymentPrice}}</text>
											</view>
										</view>
									</view>
								</view>
							</navigator>
							<view class="cu-item text-right padding-sm margin-right-xs">
								<navigator class="cu-btn line sm"
									:url="'/pages/order/order-refunds/submit/index?orderItemId=' + item.id"
									v-if="orderInfo.isPay == '1' && item.status == '0' && (orderInfo.statusDesc != '已完成' || wihiteUserIds.includes(orderInfo.userId))">申请售后<text class="cuIcon-right"></text></navigator>
								<navigator class="cu-btn line sm text-orange"
									:url="'/pages/order/order-refunds/form/index?orderItemId=' + item.id"
									v-if="orderInfo.isPay == '1' && item.status != '0'">{{item.statusDesc}}<text class="cuIcon-right"></text></navigator>
							</view>
						</view>
						<view class="cu-item">
							<view class="">
								<text class="text-gray text-sm">订单金额</text>
							</view>
							<view class="text-sm text-gray">
								<text class="text-price">{{orderInfo.salesPrice}}</text>
							</view>
						</view>
						<view class="cu-item margin-top-xs">
							<view class="text-sm text-gray">
								<text class="text-gray text-sm">{{ rederText }}</text>
							</view>
							<view class="text-sm text-gray">+<text class="text-price">{{ orderInfo.deliveryWay == 3 ? orderInfo.fulfillPrice : orderInfo.freightPrice}}</text>
							</view>
						</view>
						<view class="cu-item margin-top-xs" v-if="orderInfo.paymentCouponPrice">
							<view class="">
								<text class="text-gray text-sm">优惠券抵扣金额</text>
							</view>
							<view class="text-sm text-gray">-<text
									class="text-price">{{orderInfo.paymentCouponPrice}}</text>
							</view>
						</view>
						<view class="cu-item margin-top-xs" v-if="orderInfo.paymentPoints">
							<view class="">
								<text class="text-gray text-sm">积分抵扣金额</text>
							</view>
							<view class="text-sm text-gray">-<text
									class="text-price">{{orderInfo.paymentPointsPrice}}</text>
							</view>
						</view>
						<view class="cu-item margin-top-xs">
							<view class="">
								<text class="text-gray text-sm">支付金额</text>
							</view>
							<view class="margin-top-xs">
								<text class="text-gray text-sm"
									v-if="orderInfo.orderType != '0'">{{orderInfo.orderType == '1' ? '砍价后' : orderInfo.orderType == '2' ? '拼团价' : orderInfo.orderType == '3' ? '秒杀价' : ''}}</text>
								<text
									class="text-price text-red text-df text-bold margin-left-sm">{{orderInfo.paymentPrice}}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="cu-card no-card margin-top-xs">
				<view class="cu-item order-information">
					<view class="cu-bar bg-white">
						<view class="text-df margin-left-sm">
							<text class="cuIcon-titles" :class="'text-'+theme.themeColor"></text>订单信息
						</view>
					</view>
					<view class="margin-left-sm flex align-center">
						<text class="margin-left flex-sub text-sm text-gray">配送方式:</text>
						<view class="flex-twice text-sm text-gray">
							{{orderInfo.deliveryWay == '1' ? '普通快递' : orderInfo.deliveryWay == '2' ? '上门自提' : orderInfo.deliveryWay == '3' ? '同城配送' : ''}}
						</view>
					</view>
					<view class="margin-left-sm margin-top-xs flex align-center"
						v-if="orderInfo.deliveryWay == '1' && (orderInfo.status == '2' || orderInfo.status == '3' || orderInfo.status == '4')">
						<text class="margin-left flex-sub text-sm text-gray">发货类型:</text>
						<view class="flex-twice text-sm text-gray">
							{{orderInfo.deliveryType == '1' ? '发物流' : orderInfo.deliveryType == '2' ? '商家配送' : orderInfo.deliveryType == '3' ? '虚拟发货' : ''}}
						</view>
					</view>
					<view class="margin-left-sm margin-top-xs flex align-center">
						<text class="margin-left flex-sub text-sm text-gray">订单编号:</text>
						<view class="flex-twice text-sm text-gray">{{orderInfo.orderNo}}<button
								class="cu-btn sm margin-left-xl" @tap="copyData(orderInfo.orderNo)"
								:data-data="orderInfo.orderNo">复制</button>
						</view>
					</view>
					<view class="margin-left-sm margin-top-xs flex align-center">
						<text class="margin-left flex-sub text-sm text-gray">创建时间:</text>
						<view class="flex-twice text-sm text-gray">{{orderInfo.createTime}}</view>
					</view>
					<view class="margin-left-sm margin-top-xs flex align-center" v-if="orderInfo.paymentTime">
						<text class="margin-left flex-sub text-sm text-gray">付款时间:</text>
						<view class="flex-twice text-sm text-gray">{{orderInfo.paymentTime}}</view>
					</view>
					<view class="margin-left-sm margin-top-xs flex align-center">
						<text class="margin-left flex-sub text-sm text-gray">订单来源:</text>
						<view class="flex-twice text-sm text-gray">
							{{orderInfo.appType=='MA' ? '小程序' : orderInfo.appType=='H5-WX' ? '公众号H5' : orderInfo.appType=='H5' ? '普通H5' : orderInfo.appType=='APP' ? 'APP' : orderInfo.appType=='H5-PC' ? 'PC端H5' : ''}}
						</view>
					</view>
					<view class="margin-left-sm margin-top-xs flex align-center" v-if="orderInfo.paymentType">
						<text class="margin-left flex-sub text-sm text-gray">支付类型:</text>
						<view class="flex-twice text-sm text-gray">
							{{orderInfo.paymentType=='1' ? '微信' : orderInfo.paymentType=='2' ? '支付宝' : ''}}
						</view>
					</view>
				</view>
			</view>
			<view class="cu-card no-card margin-top-xs" v-if="orderInfo.userMessage">
				<view class="cu-item cu-form-group order-information align-start">
					<view class="cu-bar bg-white">
						<view class="text-df">给卖家留言:</view>
					</view>
					<textarea class="text-sm text-gray" readonly :value="orderInfo.userMessage"></textarea>
				</view>
			</view>
		</view>
		<!-- 用来保证底部高度 -->
		<view class="cu-tabbar-height"></view>
		<view class="cu-bar bg-white border foot flex justify-end">
			<order-operate :orderInfo="orderInfo" :callPay="callPay" :contact="true" @orderCancel="orderCancel"
				@orderReceive="orderCancel" @orderDel="orderDel"></order-operate>
			<button v-if="orderInfo && orderInfo.deliveryWay == '2' && orderInfo.status == '1'" @click="onQrcode"
				class="cu-btn round margin-right-sm" :class="'bg-'+theme.themeColor">
				<text class="cuIcon-qrcode margin-right-xs"></text>核销码
			</button>
		</view>
		<view :class="'cu-modal ' + showModal" @tap="onHideModal">
			<view class="cu-dialog bg-white radius-sm" @tap.stop>
				<view class="cu-bar bg-white justify-end">
					<view class="content">{{ '核销二维码' }}</view>
					<view class="action" @tap="onHideModal">
						<text class="cuIcon-close text-red"></text>
					</view>
				</view>
				<view class="padding-bottom padding-top-xs">
					<tki-qrcode ref="qrCodeRef" :size="500" :iconSize="30" @result="onShowModal"
						icon="/static/public/logo.png"></tki-qrcode>
				</view>
				<view>
					<view class="cu-bar bg-white solid-top show-btn">
						<view class="action margin-0 flex-sub" @tap="onHideModal">关闭</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2018-2019
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	const app = getApp();
	import api from 'utils/api'
	import util from 'utils/util'
	import orderOperate from "components/order-operate/index";
	import countDown from "components/count-down/index";
	import tkiQrcode from "@/components/tki-qrcode/tki-qrcode.vue"
	import shopStoreInfo from "@/components/shop/shop-store-info/index.vue"

	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				orderInfo: null,
				id: null,
				callPay: false, //是否直接调起支付
				showModal: "",
				wihiteUserIds: ["1891669560646533122"]//白名单
			};
		},

		components: {
			shopStoreInfo,
			tkiQrcode,
			orderOperate,
			countDown
		},

		onShow() {
			app.initPage().then(res => {
				this.orderGet(this.id);
			});
		},

		onLoad(options) {
			// 保存别人分享来的 userCode
			util.saveSharerUserCode(options);
			this.id = options.id;
			if (options.callPay) {
				this.callPay = true;
			}
		},

		computed: {
			rederText () {
				let text = '运费'
				if (this.orderInfo &&this.orderInfo.deliveryWay != 3) {
					return text
				}
				if (
					this.orderInfo
					&& this.orderInfo.listOrderItem
					&& this.orderInfo.listOrderItem[0].spuName
					&& this.orderInfo.listOrderItem[0].spuName.includes('上门')
				) {
					text = '交通费'
				} else {
					text = '配送费'
				}
				return text
			}
		},

		methods: {
			orderGet(id) {
				let that = this;
				api.orderGet(id).then(res => {
					let orderInfo = res.data;
					if (!orderInfo) {
						uni.redirectTo({
							url: '/pages/order/order-list/index'
						});
					}
					this.orderInfo = orderInfo;
					setTimeout(function() {
						that.callPay = false;
					}, 4000);
				});
			},

			//复制内容
			copyData(value) {
				uni.setClipboardData({
					data: value,
					success: function() {
						uni.showToast({
							title: '复制成功',
							icon: 'none',
						});
					}
				});
			},
			orderCancel() {
				let id = this.orderInfo.id;
				this.orderGet(id);
			},
			orderDel() {
				uni.navigateBack({
					delta: 1
				});
			},
			countDownDone() {
				this.orderGet(this.id);
			},
			onShowModal() {
				this.showModal = "show";
			},
			onHideModal() {
				this.showModal = "";
				this.orderGet(this.id);
			},
			onQrcode() {
				// 生成核销二维码，商户扫码后跳转到核销详情页面
				this.$refs.qrCodeRef._makeCode(this.id);
			},

		}
	};
</script>
<style>
	.address-bg {
		min-height: 180rpx;
	}

	.row-img {
		width: 200rpx !important;
		height: 200rpx !important;
		border-radius: 10rpx
	}

	.loc-content {
		width: 84% !important;
	}

	.loc-info {
		line-height: 1.4em
	}

	.cu-list.menu>.cu-item:after {
		border-bottom: unset !important;
	}

	.cu-list.menu>.cu-item {
		min-height: unset !important;
	}

	.order-information {
		padding-bottom: 100rpx;
	}
</style>