<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true">
			<block slot="backText">返回</block>
			<block slot="content">订单跟踪</block>
		</cu-custom>
		<view class="bg-white padding logistics" v-if="orderLogistics">
			<view class="text-black text-sm">物流单号：{{orderLogistics.logisticsNo}}<button class="cu-btn sm" @tap="copyData" :data-data="orderLogistics.logisticsNo" style="margin-left: 20rpx;">复制</button></view>
			<view class="text-black text-sm">物流公司：{{orderLogistics.logisticsDesc}}</view>
			<view class="text-black text-sm">当前状态：{{orderLogistics.statusDesc}}</view>
		</view>
		<view class="cu-card no-card margin-top-xs"  v-if="orderLogistics">
			<view class="cu-item padding-top padding-bottom">
				<view class="address text-sm">
					<view class="cuIcon-locationfill text-bold" :class="'text-'+theme.themeColor">[收货地址]</view>
					<view class="address-home margin-left">{{orderLogistics.address}}</view>
				</view>
				<view class="cu-timeline Logistics-information" v-if="!orderLogistics.listOrderLogisticsDetail || orderLogistics.listOrderLogisticsDetail.length <= 0">
					<view class="cu-item">
						<view class="content bg-white text-sm without">暂无物流信息</view>
					</view>
				</view>
				<view class="cu-timeline text-sm Logistics-information" v-for="(item, index) in orderLogistics.listOrderLogisticsDetail":key="index">
					<view :class="'cu-item ' + (index == 0 ? 'text-green cuIcon-roundcheckfill' : '')">
						<view :class="'content bg-white text-sm' + (index == 0 ? 'bg-red' : '')">
							<view class="logisticsTime">
								<view class="text-gray text-sm ">{{item.day}}</view>
								<view class="text-gray text-sm ">{{item.time}}</view>
							</view>
							<view class="logisticsAddress">{{item.logisticsInformation}}</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2018-2019
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	const app = getApp();
	import api from 'utils/api'
	import util from 'utils/util'

	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				orderLogistics: null,
				id: null
			};
		},

		components: {},
		props: {},

		onShow() {},

		onLoad(options) {
			// 保存别人分享来的 userCode
			util.saveSharerUserCode(options);
			this.id = options.id;
			app.initPage().then(res => {
				this.orderLogisticsGet(this.id);
			});
		},

		methods: {
			orderLogisticsGet(id) {
				let that = this;
				api.orderLogisticsGet(id).then(res => {
					let orderLogistics = res.data;
					orderLogistics.listOrderLogisticsDetail.forEach(function(item) {
						let logisticsTime = item.logisticsTime
						let timeArr = logisticsTime.split(' ')
						item.day = timeArr[0]
						item.time = timeArr[1]
					})
					this.orderLogistics = orderLogistics;
				});
			},
			//复制内容
			copyData(e) {
				uni.setClipboardData({
					data: e.currentTarget.dataset.data,
					success: function () {
						uni.showToast({
							title: '复制成功',
						});
					}
				});
			}

		}
	};
</script>

<style>
	.Logistics-information{
		margin-left: 160rpx;
	}

	.logisticsTime{
		margin-left: -280rpx;
		margin-top: -20rpx;
	}

	.logisticsAddress{
		margin-top: -74rpx;
		margin-left: -30rpx;
	}

	.without{
		margin-top: -20rpx;
		margin-left: -30rpx;
	}

	.address{
		width: 90%;
		padding-left: 210rpx;
		padding-bottom: 20rpx;
	}
</style>
