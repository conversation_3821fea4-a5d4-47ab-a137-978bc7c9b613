<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view>
		<cu-custom :bgColor="'bg-' + theme.backgroundColor" :isBack="true">
			<block slot="backText">返回</block>
			<block slot="content">订单退款</block>
		</cu-custom>
		<view class="cu-bar bg-white solid-bottom">
			<view class="action">
				<text class="cuIcon-titles" :class="'text-' + theme.themeColor"></text>
				退款信息
			</view>
		</view>
		<view class="cu-card no-card article margin-bottom-bar" style="padding-bottom: 80rpx;">
			<view class="cu-item">
				<view class="content response align-center margin-top-sm">
					<image :src="orderItem.picUrl ? orderItem.picUrl : '/static/public/img/no_pic.png'" mode="aspectFill" class="row-img margin-top-xs"></image>
					<view class="desc row-info block">
						<view class="text-black margin-top-xs overflow-2">{{ orderItem.spuName }}</view>
						<view class="text-gray  margin-top-xs overflow-2" v-if="orderItem.specInfo">{{ orderItem.specInfo }}</view>
						<view class="flex align-center">
							<view class="text-price text-scarlet text-bold text-xl margin-top-sm">{{ orderItem.salesPrice }}</view>
							<view class="text-df text-gray margin-top-sm padding-lr">x{{ orderItem.quantity }}</view>
						</view>
					</view>
				</view>
			</view>
			<view class="cu-item solid-top" style="padding-bottom: 100rpx;" v-for="(item, index) in orderRefundsList" :key="index">
				<view class="padding-lr padding-tb-xs margin-top-sm">发起时间：{{ item.createTime }}</view>
				<view class="padding-lr padding-tb-xs">
					退款状态：
					<text class="cu-tag radius line-red">{{ item.statusDesc }}</text>
				</view>
				<view class="padding-lr padding-tb-xs">
					退款金额：
					<text class="text-price text-bold text-scarlet">{{ item.refundAmount }}</text>
				</view>
				<view class="padding-lr padding-tb-xs" v-if="orderItem.paymentPoints">
					退款积分：
					<text class="text-bold text-red">{{ orderItem.paymentPoints }}</text>
				</view>
				<view class="padding-lr padding-tb-xs">退款数量：x{{ orderItem.quantity }}</view>
				<view class="padding-lr padding-tb-xs" v-if="index == 0">
					是否到账：
					<text :class="'cu-tag radius line-' + (orderItem.isRefund == '1' ? 'green' : 'red')">{{ orderItem.isRefund == '1' ? '是' : '否' }}</text>
				</view>
				<view class="padding-lr padding-tb-xs" v-if="item.refuseRefundReson">拒绝原因：{{ item.refuseRefundReson }}</view>
				<view class="padding-lr padding-tb-xs">
					<view>退款原因：</view>
					<view class="margin-top-sm bg-gray padding-sm radius-sm" style="opacity: 0.4;">{{ item.refundReson }}</view>
				</view>
				<view v-if="orderItem.status == '2' || orderItem.status == '4'" class="margin-top">
					<view class="cu-bar bg-white solid-top">
						<view class="action">
							<text class="cuIcon-titles" :class="'text-' + theme.themeColor"></text>
							退货信息
						</view>
					</view>
					<view>
						<view class="padding-lr padding-tb-xs">
							商家收件人:
							<text class="">{{ item.receiveInfo.nickName }}</text>
						</view>
						<view class="padding-lr padding-tb-xs">
							收件电话:
							<text class="">{{ item.receiveInfo.phone }}</text>
						</view>
						<view class="padding-lr padding-tb-xs">
							收件地址:
							<text class="">{{ item.receiveInfo.address }}</text>
						</view>
					</view>
					<view class="flex align-center padding-lr padding-tb  text-df">
						<view>退货快递单号:</view>
						<view class="flex-sub text-right">
							<text v-if="orderItem.status == '4' || item.status == '212'">{{ item.logisticsNo }}</text>
							<input v-else class="text-right text-df" placeholder="请输入退货快递单号" v-model:value="item.logisticsNo" />
						</view>
					</view>
					<view class="flex align-center padding-lr padding-tb-xs">
						<view>退货快递公司:</view>
						<view class="flex-sub text-right">
							<text v-if="orderItem.status == '4' || item.status == '212' || index != 0">{{ getLogisticsName(item.logisticsCode) }}</text>
							<picker v-else @change="pickerChange" :value="pickerIndex" :range="orderlogistics" :range-key="'name'">
								<view class="picker text-black">
									{{ pickerIndex > -1 && orderlogistics[pickerIndex] ? orderlogistics[pickerIndex].name : '请选择快递' }}
									<text class="cuIcon-right"></text>
								</view>
							</picker>
						</view>
					</view>
					<view v-if="orderItem.status == '2' && item.status != '212'" class="flex align-center justify-end margin-top-sm">
						<view class="text-gray text-sm flex align-center">提交后请等待店铺审核</view>
						<view @click="submitRefundInfo(item)" class="cu-btn bg-red margin-lr round">提交退货单号</view>
					</view>
				</view>
			</view>
		</view>
		<view class="cu-bar bg-white tabbar foot justify-end">
			<navigator class="cu-btn round margin-right shadow-blur" open-type="navigate" :url="'/pages/customer-service/customer-service-list/index?shopId=' + orderItem.shopId">
				<view class="cuIcon-servicefill">联系客服</view>
			</navigator>
		</view>
	</view>
</template>

<script>
/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
const app = getApp();
const util = require('utils/util.js');
import api from 'utils/api';
import { dicSelect } from './util.js';

export default {
	data() {
		return {
			CustomBar: this.CustomBar,
			theme: app.globalData.theme, //全局颜色变量
			orderItem: {
				quantity: 0
			},
			orderRefundsList: [],
			pickerIndex: -1,
			orderlogistics: dicSelect //快递公司
		};
	},

	onLoad(options) {
		// 保存别人分享来的 userCode
		util.saveSharerUserCode(options);
		this.orderItemId = options.orderItemId;
		app.initPage().then((res) => {
			this.orderItemGet(this.orderItemId);
		});
	},

	methods: {
		orderItemGet(id) {
			let that = this;
			api.orderItemGet(id).then((res) => {
				let orderItem = res.data;
				this.orderItem = orderItem;
				this.orderRefundsList = orderItem.listOrderRefunds;
				if (this.orderRefundsList && this.orderRefundsList.length > 0) {
					let code = this.orderRefundsList[0].logisticsCode;
					// 找到pickerIndex
					for (let i = 0; i < this.orderlogistics.length; i++) {
						if (this.orderlogistics[i].code == code) {
							this.pickerIndex = i;
							break;
						}
					}
				}
			});
		},
		submitRefundInfo(item) {
			//判断退货信息是否填写
			if (!item.logisticsNo) {
				uni.showToast({
					title: '请填写退货快递单号',
					icon: 'none'
				});
				return;
			}
			// 单号的最小长度6个字符，最大长度32个字符
			if (item.logisticsNo.length < 6 || item.logisticsNo.length > 32) {
				uni.showToast({
					title: '退货快递单号长度不正确',
					icon: 'none'
				});
				return;
			}

			if (this.pickerIndex > -1) {
				item.logisticsCode = this.orderlogistics[this.pickerIndex].code;
			}
			if (!item.logisticsCode) {
				uni.showToast({
					title: '请选择退货快递公司',
					icon: 'none'
				});
				return;
			}
			let that = this;
			uni.showModal({
				content: '确定要提交退货信息吗?',
				cancelText: '我再想想',
				confirmColor: '#ff0000',
				success(res) {
					let form = {
						id: item.id,
						logisticsNo: item.logisticsNo,
						logisticsCode: item.logisticsCode
					};
					if (res.confirm) {
						api.orderRefundsUpdate(form).then((res) => {
							//提交成功刷新页面信息
							that.orderItemGet(that.orderItemId);
							// 提示成功
							uni.showToast({
								title: '提交成功'
							});
							// 返回上一页
							uni.navigateBack({
								delta: 1
							});
						});
					}
				}
			});
		},
		pickerChange(value) {
			if (value) {
				this.pickerIndex = value.detail.value;
			}
		},
		getLogisticsName(value) {
			let name = '-';
			if (value) {
				for (let i = 0; i < this.orderlogistics.length; i++) {
					if (this.orderlogistics[i].code == value) {
						name = this.orderlogistics[i].name;
						break;
					}
				}
			}
			return name;
		}
	}
};
</script>
<style>
.row-img {
	width: 200rpx !important;
	height: 200rpx !important;
	border-radius: 10rpx;
}
</style>
