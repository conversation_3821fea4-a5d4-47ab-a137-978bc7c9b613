<template>
    <view>
        <!-- #ifndef MP -->
        <cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true">
            <block slot="content">{{ title }}</block>
        </cu-custom>
        <view class="margin-top-xl">
            <!-- #endif -->
            <web-view :src="url" :webview-styles="webviewStyles"></web-view>
            <!-- #ifndef MP -->
        </view>
        <!-- #endif -->
    </view>
</template>
<script>
// #ifndef MP
const app = getApp();
// #endif
export default {
    data() {
        return {
            // #ifndef MP
            theme: app.globalData.theme, //全局颜色变量
            // #endif
            url: "",
            title: '浏览',
            webviewStyles: {
                progress: {
                    color: '#FF3333'
                }
            }
        }
    },
    onLoad(option) {
        // url如果带参数: 小程序中必须使用encodeURIComponent先编码,然后在本页面使用 decodeURIComponent 解码
        this.url = decodeURIComponent(option.url);
        if (option.title) {
            this.title = option.title;
            uni.setNavigationBarTitle({
                title: option.title
            })
        }
        uni.setNavigationBarColor({
            frontColor: '#ffffff',
            backgroundColor: '#FF3333'
        });
    },
    methods: {}
}
</script>
<style>

</style>