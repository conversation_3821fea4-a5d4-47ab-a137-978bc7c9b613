<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<form @submit="userAddressSave">
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true">
			<block slot="backText">返回</block>
			<block slot="content">{{userAddress.id?'编辑':'新增'}}收货地址</block>
		</cu-custom>
		<view class="cu-form-group">
			<view class="title">姓名</view>
			<input class="text-right" placeholder="请输入姓名" maxlength="10" name="userName" :value="userAddress.userName"></input>
		</view>
		<view class="cu-form-group">
			<view class="title">联系电话</view>
			<input class="text-right" placeholder="请输入电话" maxlength="20" name="telNum" :value="userAddress.telNum"></input>
		</view>
		<view class="cu-form-group">
			<view class="title">地址选择</view>
			<!-- #ifndef H5 || APP-PLUS || MP-ALIPAY -->
			<!-- <picker mode="region" @change="regionChange" :value="region" >
				<view class="picker">{{region[0]}}，{{region[1]}}，{{region[2]}}</view>
			</picker> -->
			<view @tap="chooseLocation" class="picker">{{ region[0] && region[1] && region[2] ? region[0] + '，' + region[1] + '，' + region[2] : '点击选择地址' }}</view>
			<!-- #endif -->
			<!-- #ifdef H5 || APP-PLUS || MP-ALIPAY -->
			<region-picker @change="regionChange" :value="region" >
				<view class="picker">{{region[0]}}，{{region[1]}}，{{region[2]}}</view>
			</region-picker>
			<!-- #endif -->
		</view>
		<view class="cu-form-group">
			<view class="title">详细地址</view>
			<input class="text-right" placeholder="请输入详细地址" maxlength="50" name="detailInfo" :value="userAddress.detailInfo"></input>
		</view>
		<view class="cu-form-group">
			<view class="title">设为默认地址</view>
			<switch class="  " :class="userAddress.isDefault == '1'?theme.themeColor+' checked ':' '"
			:checked="userAddress.isDefault == '1'" @change="isDefaultChange"></switch>
		</view>
		<view class="compile">
			<button class="cu-btn shadow-blur block margin-sm round bottom-btn" :class="'bg-'+theme.themeColor" formType="submit">立即保存</button>
			<!-- #ifdef MP-WEIXIN -->
			<button class="cu-btn shadow-blur block bg-green margin-sm round bottom-btn" @tap="getWxAddress"><text class=" cuIcon-weixin">导入微信地址</text></button>
			<!-- #endif -->
			<button class="cu-btn shadow-blur block bg-red margin-sm round bottom-btn" @tap="userAddressDelete" v-if="userAddress.id">删除</button>
		</view>
	</form>
</template>

<script>
	/**
	 * Copyright (C) 2018-2019
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	const app = getApp();
	const util = require("utils/util.js");
	import api from 'utils/api'
	import validate from 'utils/validate'

	import regionPicker from "@/components/region-picker/region-picker.vue"

	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				// #ifdef H5 || APP-PLUS || MP-ALIPAY
				region: ['选择省', '选择市', '选择区'],
				// #endif
				// #ifndef H5 || APP-PLUS || MP-ALIPAY
				region: ['', '', ''],
				// #endif
				userAddress: {
					isDefault: '0',
					userAddress: '',
					telNum: '',
					detailInfo: '',
				},
			};
		},

		components: {
			regionPicker
		},
		props: {},

		onLoad(options) {
			// 保存别人分享来的 userCode
			util.saveSharerUserCode(options);
			// 本地获取参数信息
			let that = this;
			uni.getStorage({
				key: 'param-userAddressForm',
				success: function(res) {
					let userAddress = res.data;
					that.region[0] = userAddress.provinceName ? userAddress.provinceName : '选择省';
					that.region[1] = userAddress.cityName ? userAddress.cityName : '选择市';
					that.region[2] = userAddress.countyName ? userAddress.countyName : '选择区';
					that.userAddress.latValue = userAddress.latValue ? userAddress.latValue : '';
					that.userAddress.lngValue = userAddress.lngValue ? userAddress.lngValue : '';
					if( userAddress.userName){
						userAddress.isDefault = userAddress.isDefault?userAddress.isDefault:"0";
						that.userAddress = userAddress;
					}

				}
			});
		},

		methods: {
			chooseLocation() {
				console.log("选择地址");
				uni.chooseLocation({
					success: (res) => {
						console.log(res)
						// let reg = /.+?(省|市|自治区|自治州|县|区)/g
						let reg = /.+?(省|市|自治区|自治州|县|区|镇|乡|街道)/g;
						const area = res.address.match(reg)
						console.log(area);
						console.log(res.latitude, res.longitude, "经纬度");
						if(area.length > 2) {
							this.region[0] = area[0]
							this.region[1] = area[1]
							this.region[2] = area[2]
						} else {
							this.region[0] = area[0]
							this.region[1] = area[0]
							this.region[2] = area[1]
						}
						this.userAddress.detailInfo = res.name
						this.userAddress.latValue = res.latitude
						this.userAddress.lngValue= res.longitude
					}
        })
			},
			regionChange(e) {
				this.region = e.detail.value;
			},

			isDefaultChange(e) {
				if (e.detail.value) {
					this.userAddress.isDefault = '1';
				} else {
					this.userAddress.isDefault = '0';
				}
			},

			userAddressSave(e) {
				let value = e.detail.value;
				let region = this.region;

				if (!value.userName) {
					uni.showToast({
						title: '请填写收货人姓名',
						icon: 'none',
						duration: 3000
					});
					return;
				}

				if (!value.telNum) {
					uni.showToast({
						title: '请填写联系电话',
						icon: 'none',
						duration: 3000
					});
					return;
				}

				if (!validate.validateMobile(value.telNum)) {
					uni.showToast({
						title: '请输入正确的手机号码',
						icon: 'none',
						duration: 3000
					});
					return;
				}

				if (region[0] == '选择省') {
					uni.showToast({
						title: '请选择所在地区',
						icon: 'none',
						duration: 3000
					});
					return;
				}

				if (!value.detailInfo) {
					uni.showToast({
						title: '请填写详细地址',
						icon: 'none',
						duration: 3000
					});
					return;
				}

				api.userAddressSave({
					id: this.userAddress.id,
					userName: value.userName,
					telNum: value.telNum,
					provinceName: region[0],
					cityName: region[1],
					countyName: region[2],
					detailInfo: value.detailInfo,
					isDefault: this.userAddress.isDefault,
					latValue: String(this.userAddress.latValue),
					lngValue: String(this.userAddress.lngValue)
				}).then(res => {
					uni.navigateBack({
						delta: 1
					});
				});
			},

			userAddressDelete() {
				let that = this;
				uni.showModal({
					content: '确认将这个地址删除吗？',
					cancelText: '我再想想',
					confirmColor: '#ff0000',
					success(res) {
						if (res.confirm) {
							api.userAddressDel(that.userAddress.id).then(res => {
								uni.navigateBack({
									delta: 1
								});
							});
						}
					}

				});
			},

			/*
			 * 导入微信地址
			 */
			getWxAddress: function() {
				let that = this;
				uni.authorize({
					scope: 'scope.address',
					success: function(res) {
						uni.chooseAddress({
							success: function(res) {
								that.region[0] = res.provinceName;
								that.region[1] = res.cityName;
								that.region[2] = res.countyName;
								that.userAddress.userName = res.userName;
								that.userAddress.telNum = res.telNumber;
								that.userAddress.detailInfo = res.detailInfo;
							},
							fail: function(res) {
								if (res.errMsg == 'chooseAddress:cancel') {
									uni.showToast({
										title: '取消选择',
										icon: 'none',
										duration: 3000
									});
								}
							}
						});
					},
					fail: function(res) {
						console.log('-----fail')
						console.log(res)
					}
				});
			}
		}
	};
</script>

<style>
 .bottom-btn{
	margin: auto;
	width: 96%;
	height: 88rpx;
	margin-bottom: 20rpx;
 }

 .compile{
	position: fixed;
	width: 100%;
	position: absolute;
	bottom: 0;
 }
</style>
