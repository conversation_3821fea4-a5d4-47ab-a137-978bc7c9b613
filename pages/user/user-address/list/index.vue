<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true">
			<block slot="backText">返回</block>
			<block slot="content">收货地址</block>
		</cu-custom>
		<view class="cu-list menu-avatar">
			<view class="cu-item address-item" v-for="(item, index) in userAddress" :key="index">
				<view class="cu-avatar round bg-orange">
					<text class="avatar-text">{{item.userName}}</text>
				</view>
				<view class="content loc-content" @tap="selectUserAddress(item)" :data-index="index">
					<view class="flex">
						<view class="text-black">{{item.userName}}</view>
						<view class="text-gray text-sm margin-left-sm">{{item.telNum}}</view>
					</view>
					<view class="overflow-2 margin-top-xs">
						<view class="cu-tag sm radius bg-green margin-right-xs" v-if="item.isDefault == '1'">
							<text class="text-xs">默认</text>
						</view>
						<text class="text-gray text-sm">{{item.provinceName}}{{item.cityName}}{{item.countyName}}{{item.detailInfo}}</text>
					</view>
				</view>
				<view class="text-gray text-sm text-center margin-right-sm" @tap="toEdit" :data-index="index">
					<text class="cuIcon-edit"></text>
					<text class="flex margin-top-xs">编辑</text>
				</view>
			</view>
		</view>
		<view class="add-address" v-if="userAddress.length < 10">
			<button class="cu-btn block shadow-blur round add-btn" :class="'bg-'+theme.themeColor" @tap="toAdd">
				<text class="cuIcon-add"></text>添加新地址
			</button>
		</view>
		<view :class="'cu-load' + (loadmore?'loading':'')"></view>
		<view class="cu-load margin-top-xl" v-if="userAddress.length <= 0 && !loadmore">无收货地址，请添加</view>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2018-2019
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	const app = getApp();
	const util = require("utils/util.js");
	import api from 'utils/api'

	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				page: {
					searchCount: false,
					current: 1,
					size: 10,
					ascs: '',
					//升序字段
					descs: ''
				},
				parameter: {},
				loadmore: true,
				userAddress: [],
				select: false
			};
		},

		components: {},
		props: {},

		onLoad(options) {
			// 保存别人分享来的 userCode
			util.saveSharerUserCode(options);
			if (options.select) {
				this.select = true;
			}
		},

		onShow() {
			app.initPage().then(res => {
				this.userAddressPage();
			});
		},

		methods: {
			userAddressPage() {
				api.userAddressPage(this.page).then(res => {
					this.userAddress = res.data.records;
					this.loadmore = false;
				});
			},

			toAdd() {
				uni.setStorage({
					key: 'param-userAddressForm',
					data: []
				});
				uni.navigateTo({
					url: '/pages/user/user-address/form/index'
				});
			},

			toEdit(e) {
				let index = e.currentTarget.dataset.index;
				let userAddressForm = this.userAddress[index];
				/* 把参数信息异步存储到缓存当中 */

				uni.setStorage({
					key: 'param-userAddressForm',
					data: userAddressForm
				});
				uni.navigateTo({
					url: '/pages/user/user-address/form/index'
				});
			},

			selectUserAddress(userAddressForm) {
				if (this.select) {
					var pages = getCurrentPages(); // 获取页面栈
					var currPage = pages[pages.length - 1]; // 当前页面
					var prevPage = pages[pages.length - 2]; // 上一个页面
					prevPage.$vm.setUserAddress(userAddressForm); //调用上一个页面的 setUserAddress 方法，并传入 userAddressForm
					uni.navigateBack({
						delta: 1
					});
				}
			}

		}
	};
</script>
<style>
	page{
		background-color: #fff;
	}
	
	.loc-content {
		width: calc(100% - 96rpx - 60rpx - 80rpx) !important;
		left: 126rpx !important;
		line-height: 34rpx !important;
	}
	
	.address-item{
		height: 140rpx !important;
	}

	.add-btn {
		margin:20rpx auto;
		width: 96%;
		height: 80rpx;
	}

	.add-address {
		width: 100%;
		height: 120rpx;
		margin-top: 30rpx;
	}
</style>
