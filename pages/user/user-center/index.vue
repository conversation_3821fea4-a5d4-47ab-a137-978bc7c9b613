<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="false">
			<block slot="backText">返回</block>
			<block slot="content">个人中心</block>
		</cu-custom>
		<view class="cu-list menu-avatar" :class="'bg-'+theme.backgroundColor">
			<view class="padding-top-xs">
				<view class="flex padding-lr-lg">
					<view class="flex-treble flex" @click.stop="goPage('/pages/user/user-info/index')">
						<view class="cu-avatar round xl  flex solids"
							:style="userInfo.headimgUrl?'background-image:url(' + userInfo.headimgUrl + ')':''">
							{{ !userInfo.headimgUrl ? '头' : '' }}
						</view>
						<view class="content margin-left margin-top-sm">
							<view class="flex align-center">
								<view class="text-white text-bold text-xl" v-if="userInfo.nickName">
									{{ userInfo.nickName }}
								</view>
								<view class="margin-left-xl">
									<!--  #ifndef H5 -->
									<view>
										<image class="update-image"
											src="../../../static/public/img/user-center/update-image.png"></image>
									</view>
									<!--  #endif -->
									<!--  #ifdef  H5 -->
									<view @click.stop="updateUserInfo">
										<image class="update-image"
											src="../../../static/public/img/user-center/update-image.png"></image>
									</view>
									<!--  #endif -->
								</view>
							</view>
							<view class="text-white margin-top-xs" v-if="userInfo.phone">{{ userInfo.phone }}</view>
						</view>
					</view>
					<view class="flex-sub flex justify-end align-center">
						<navigator url="/extraJumpPages/signrecord/signrecord-info/index" hover-class="none">
							<image class="signrecord" src="../../../static/public/img/user-center/signrecord.png">
							</image>
						</navigator>
					</view>
				</view>
				<view class="account cu-list menu card-menu margin-top-sm margin-bottom-xl">
					<view class="flex text-center text-white align-center margin-bottom" v-if="userInfo">
						<view class="flex flex-sub flex-direction">
							<view class="text-xl text-bold">{{ userInfo.userCode }}</view>
							<view class="margin-top-xs flex align-center justify-center">
								<text class="cuIcon-vip"></text>
								<text
									class="margin-left-xs text-sm">{{ userInfo.userGrade == '0' ? '用户' : '会员' }}编号</text>
							</view>
						</view>
						<navigator class="flex flex-sub flex-direction" url="/pages/user/user-points-record/index"
							hover-class="none">
							<view class="text-xl text-bold">{{ userInfo.pointsCurrent }}</view>
							<view class="margin-top-xs flex align-center justify-center">
								<text class="cuIcon-medal"></text>
								<text class="margin-left-xs text-sm">当前积分</text>
							</view>
						</navigator>
						<navigator class="flex flex-sub flex-direction" url="/extraJumpPages/coupon/coupon-user-list/index"
							hover-class="none">
							<view class="text-xl text-bold">{{ userInfo.couponNum ? userInfo.couponNum : 0 }}</view>
							<view class="margin-top-xs flex align-center justify-center">
								<text class="cuIcon-ticket"></text>
								<text class="margin-left-xs text-sm">优惠券</text>
							</view>
						</navigator>
					</view>
				</view>
				<!-- <image class="user-bg" src="http://minio.joolun.com/joolun/1/material/c73c49ba-9e7b-47de-bf95-c430fb6e1198.png"></image> -->
			</view>
		</view>
		<view class="cu-list padding-lr-sm" style="margin-top: -30px;">
			<view v-if="distributionConfig != null && distributionConfig.enable=='1'">
				<view
					class="card-menu menu padding-lr-sm padding-top-sm padding-bottom-lg flex justify-between align-center distribution-bg">
					<view class="flex align-center">
						<image class="distribution-image"
							src="https://minio.joolun.com/joolun/1/material/db98e7da-9cd6-433e-a75b-74b4847db7a5.png">
						</image>
						<view class="text-yellow" style="color: #faead0;">微分销招募令,期待你的加入！</view>
					</view>
					<navigator class="text-sm distribution-center" url="/pageA/distribution/distribution-center/index"
						hover-class="none">分销中心</navigator>
				</view>
			</view>
			<!--<view class="cu-bar bg-white solid-bottom" style="border-radius: 10rpx 10rpx 0rpx 0rpx; margin-top: -30rpx;">
				<view class="action">我的订单</view>
				<navigator class="action text-df" url="/pages/order/order-list/index" hover-class="none">全部订单
					<text class="cuIcon-right"></text>
				</navigator>
			</view>-->
			<view class="cu-list grid col-5 no-border" style="margin-top: -30rpx; border-radius: 10rpx;">
				<view class="cu-item">
					<navigator class="flex flex-sub flex-direction align-center"
						url="/pages/order/order-list/index?status=0" hover-class="none">
						<view class="cuIcon-pay" :class="'text-'+theme.themeColor">
							<view v-if="orderCountAll[0]>0" class="cu-tag badge">{{ orderCountAll[0] }}</view>
						</view>
						<text class="text-df margin-top-sm">待付款</text>
					</navigator>
				</view>
				<view class="cu-item">
					<navigator class="flex flex-sub flex-direction align-center"
						url="/pages/order/order-list/index?status=1" hover-class="none">
						<view class="cuIcon-deliver" :class="'text-'+theme.themeColor">
							<view v-if="orderCountAll[1]>0" class="cu-tag badge">{{ orderCountAll[1] }}</view>
						</view>
						<text class="text-df margin-top-sm">待发货</text>
					</navigator>
				</view>
				<view class="cu-item">
					<navigator class="flex flex-sub flex-direction align-center"
						url="/pages/order/order-list/index?status=2" hover-class="none">
						<view class="cuIcon-send" :class="'text-'+theme.themeColor">
							<view v-if="orderCountAll[2]>0" class="cu-tag badge">{{ orderCountAll[2] }}</view>
						</view>
						<text class="text-df margin-top-sm">待收货</text>
					</navigator>
				</view>
				<view class="cu-item">
					<navigator class="flex flex-sub flex-direction align-center"
						url="/pages/order/order-list/index?status=4" hover-class="none">
						<view class="cuIcon-evaluate" :class="'text-'+theme.themeColor">
							<view v-if="orderCountAll[3]>0" class="cu-tag badge">{{ orderCountAll[3] }}</view>
						</view>
						<text class="text-df margin-top-sm">已完成</text>
					</navigator>
				</view>
				<view class="cu-item">
					<navigator class="flex flex-sub flex-direction align-center solid-left"
						url="/pages/order/order-list/index" hover-class="none">
						<view class="cuIcon-goods" :class="'text-'+theme.themeColor">
						</view>
						<text class="text-df margin-top-sm">全部订单</text>
					</navigator>
				</view>
			</view>
		</view>
		<view class="padding-lr-sm margin-top-xs">
			<navigator url="/extraJumpPages/coupon/coupon-list/index" hover-class="none">
				<image class="activity-image"
					src="https://minio.joolun.com/joolun/1/material/2786ba37-4d66-40b7-9b2a-062ebd2f7517.png">
				</image>
			</navigator>
		</view>
		<view class="cu-list padding-lr-sm margin-top-xs">
			<view class="cu-bar bg-white" style="border-radius: 10rpx 10rpx 0rpx 0rpx;">
				<view class="action">我的工具</view>
			</view>
			<view class="cu-list grid col-4 no-border" style="border-radius: 0rpx 0rpx 10rpx 10rpx;">
				<view class="cu-item">
					<navigator url="/pages/user/user-footprint/index" hover-class="none">
						<image src="../../../static/public/img/user-center/user-footprint.png" class="item-img"></image>
						<text class="text-grey text-df">我的足迹</text>
					</navigator>
				</view>
				<view class="cu-item">
					<navigator url="/extraJumpPages/coupon/coupon-user-list/index" hover-class="none">
						<image src="../../../static/public/img/user-center/user-coupon.png" class="item-img"></image>
						<text class="text-grey text-df">我的卡券</text>
					</navigator>
				</view>
				<view class="cu-item">
					<navigator url="/pageA/groupon/groupon-user-list/index" hover-class="none">
						<image src="../../../static/public/img/user-center/user-groupon.png" class="item-img"></image>
						<text class="text-grey text-df">我的团购</text>
					</navigator>
				</view>
				<view class="cu-item">
					<navigator url="/pageA/bargain/bargain-user-list/index" hover-class="none">
						<image src="../../../static/public/img/user-center/user-bargain.png" class="item-img"></image>
						<text class="text-grey text-df">我的砍价</text>
					</navigator>
				</view>
				<view class="cu-item">
					<navigator url="/pageA/seckill/seckill-user-list/index" hover-class="none">
						<image src="../../../static/public/img/user-center/user-seckill.png" class="item-img"></image>
						<text class="text-grey text-df">我的秒杀</text>
					</navigator>
				</view>
				<view class="cu-item" v-if="distributionConfig != null && distributionConfig.enable=='1'">
					<navigator url="/pageA/distribution/distribution-center/index" hover-class="none">
						<image src="../../../static/public/img/user-center/user-distribution.png" class="item-img">
						</image>
						<text class="text-grey text-df">分销中心</text>
					</navigator>
				</view>
				<view class="cu-item">
					<navigator url="/pages/user/user-collect/index" hover-class="none">
						<image src="../../../static/public/img/user-center/user-collect.png" class="item-img"></image>
						<text class="text-grey text-df">我的收藏</text>
					</navigator>
				</view>
				<view class="cu-item">
					<navigator url="/pages/article/article-list/index" hover-class="none">
						<image src="../../../static/public/img/user-center/user-article.png" class="item-img"></image>
						<text class="text-grey text-df">新闻资讯</text>
					</navigator>
				</view>
				<view class="cu-item">
					<navigator url="/pages/user/user-appraises/index" hover-class="none">
						<image src="../../../static/public/img/user-center/daipinglun.png" class="item-img"></image>
						<text class="text-grey text-df">我的评价</text>
					</navigator>
				</view>
				<view class="cu-item">
					<navigator url="/pages/user/user-address/list/index" hover-class="none">
						<image src="../../../static/public/img/user-center/user-address.png" class="item-img"></image>
						<text class="text-grey text-df">收货地址</text>
					</navigator>
				</view>
				<view class="cu-item">
					<navigator url="/extraJumpPages/signrecord/signrecord-info/index" hover-class="none">
						<image src="../../../static/public/img/user-center/user-signrecord.png" class="item-img">
						</image>
						<text class="text-grey text-df">我的签到</text>
					</navigator>
				</view>
				<view v-if="theme.showType!='2'" class="cu-item">
					<navigator url="/extraJumpPages/shop/shop-apply/index">
						<image src="../../../static/public/img/user-center/user-shop-apply.png" class="item-img">
						</image>
						<text class="text-grey text-df">店铺入驻</text>
					</navigator>
				</view>
			</view>
		</view>
		<!-- 提示：实际功能为退出当前账号，因为苹果上架App Store必须要删除账号功能，所以将 退出登录 文字改为 删除账号  -->
		<view class="text-red bg-white  text-center margin-top margin-sm padding-sm radius" @click="logout">
			删除账号
		</view>
		<!-- #ifdef APP-PLUS -->
		<view class="margin-top flex justify-center text-sm" v-show="showPrivacyPolicy">
			<navigator class="text-blue text-sm" :url="'/pages/public/webview/webview?title=用户协议&url='+protocolUrl">
				{{ ' 用户协议 ' }}
			</navigator>
			和
			<navigator class="text-blue text-sm  "
				:url="'/pages/public/webview/webview?title=隐私政策&url='+privacyPolicyUrl">{{ ' 隐私政策' }}
			</navigator>
		</view>
		<!-- #endif -->
		<view class="text-gray text-sm text-center margin-sm">www.joolun.com提供技术支持（{{ version }}）</view>

	</view>
</template>

<script>
	/**
	 * Copyright (C) 2018-2019
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	const app = getApp();
	import api from 'utils/api'
	import util from 'utils/util'
	import JimUtil from '@/utils/jim-util' // IM工具库
	import __config from '@/config/env'; // 配置文件Ω
	import packagejson from '@/package.json'

	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				userInfo: {},
				distributionConfig: null,
				orderCountAll: [],
				showPrivacyPolicy: __config.showPrivacyPolicy,
				privacyPolicyUrl: __config.privacyPolicyUrl,
				protocolUrl: __config.protocolUrl,
				canIUseGetUserProfile: false,
				version: packagejson.version,
				isWeiXinBrowser: util.isWeiXinBrowser()
			};
		},

		components: {},
		props: {},

		onShow() {
			app.initPage().then(res => {
				this.userInfoGet();
				this.orderCountAllFun();
			});
			// 更新消息消息未读数
			JimUtil.getJIMUnreadMsgCnt()
			//更新购物车tabar角标数量
			uni.setTabBarBadge({
				index: 3,
				text: app.globalData.shoppingCartCount + ''
			});
			// #ifdef MP-WEIXIN
			// app.doLogin()
			// #endif
		},

		onLoad(option) {
			// #ifdef MP-WEIXIN
			if (uni.getUserProfile) {
				this.canIUseGetUserProfile = true
			}
			// #endif
			// #ifdef H5
			let code = option.code;
			let state = option.state;
			//授权code获取用户信息
			if (code && state == 'snsapi_userinfo_update') { //有code
				this.userInfoUpdateByMp({
					jsCode: code,
					scope: state
				});
			}
			// #endif
		},

		methods: {
			goPage(path) {
				if (path) {
					uni.navigateTo({
						url: path
					})
				}
			},
			// #ifdef MP-WEIXIN
			agreeGetUser(e) {
				if (e.detail.errMsg == 'getUserInfo:ok') {
					api.userInfoUpdateByMa(e.detail).then(res => {
						this.userInfo = res.data;
						uni.setStorageSync('user_info', this.userInfo);
						this.userInfoGet();
					});
				}
			},

			getUserProfile(e) {
				// 该接口2022年10月25日后收回 https://developers.weixin.qq.com/community/develop/doc/00022c683e8a80b29bed2142b56c01
				// 推荐使用wx.getUserProfile获取用户信息，开发者每次通过该接口获取用户个人信息均需用户确认
				// 开发者妥善保管用户快速填写的头像昵称，避免重复弹窗
				wx.getUserProfile({
					desc: '用于完善会员资料', // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
					success: (detail) => {
						api.userInfoUpdateByMa(detail).then(res => {
							this.userInfo = res.data;
							uni.setStorageSync('user_info', this.userInfo);
							this.userInfoGet();
						});
					}
				})
			},
			// #endif
			// #ifdef H5
			updateUserInfo() {
				if (util.isWeiXinBrowser()) {
					//微信公众号H5，页面授权获取用户详情信息
					let appId = app.globalData.appId;
					let pages = getCurrentPages();
					let currentPage = pages[pages.length - 1];
					let route = currentPage.route;
					let redirectUri = location.href;
					let componentAppId_str = app.globalData.componentAppId ? '&component_appid=' + app.globalData
						.componentAppId : '';
					redirectUri = encodeURIComponent(redirectUri);
					let wx_url = 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' + appId +
						'&redirect_uri=' + redirectUri + componentAppId_str +
						'&response_type=code&scope=snsapi_userinfo&state=snsapi_userinfo_update#wechat_redirect';
					location.href = wx_url;
				}
			},
			//通过微信公众号网页授权更新用户信息
			userInfoUpdateByMp(parm) {
				let that = this;
				api.userInfoUpdateByMp(parm).then(res => {
					//公众号h5网页授权时url会产生code、state参数，防止code、state被复制，需自动剔除
					let query = that.$Route.query;
					delete query.code;
					delete query.state;
					util.resetPageUrl(query);
					this.userInfo = res.data;
					uni.setStorageSync('user_info', this.userInfo);
					this.userInfoGet();
				}).catch(res => {

				});
			},
			// #endif
			//获取商城用户信息
			userInfoGet() {
				api.userInfoGet().then(res => {
					this.userInfo = res.data;
				});
				//分销设置
				api.distributionConfig().then(res => {
					this.distributionConfig = res.data
				});
			},

			orderCountAllFun() {
				api.orderCountAll().then(res => {
					this.orderCountAll = res.data;
				});
			},

			logout() {
				uni.showModal({
					content: '确定删除账号吗？',
					cancelText: '我再想想',
					confirmColor: '#ff0000',
					success(res) {
						if (res.confirm) {
							api.logout().then(res => {
								let userInfo = res.data;
								uni.setStorageSync('user_info', userInfo);
								if (userInfo) {
									uni.setStorageSync('third_session', userInfo.thirdSession);
								}
								//登出IM
								JimUtil.IMloginOut()
								//退出登录完成跳到首页
								uni.reLaunch({
									url: '/pages/home/<USER>'
								});
								//清空购物车数量
								uni.setTabBarBadge({
									index: 3,
									text: '0'
								});
							});
						}
					}
				});
			}

		}
	};
</script>
<style>
	.user-bg {
		position: absolute;
		top: 180rpx;
		right: 30rpx;
		width: 248rpx;
		height: 248rpx;
		opacity: 0.2;
	}

	.item-img {
		width: 60rpx;
		height: 60rpx;
	}

	.update-image {
		width: 30rpx;
		height: 30rpx;
	}

	.signrecord {
		width: 140rpx;
		height: 52rpx;
	}

	.distribution-bg {
		border-radius: 20rpx 20rpx 0 0;
		background-color: #3b363a;
	}

	.distribution-image {
		width: 80rpx;
		height: 80rpx;
	}

	.distribution-center {
		border-radius: 20px;
		padding: 6rpx 16rpx;
		background-image: linear-gradient(to right, #faead0, #e9b59a);
		color: #42494d;
	}

	.activity-image {
		width: 100%;
		height: 130rpx;
		background-size: 100% 130rpx;
	}
</style>