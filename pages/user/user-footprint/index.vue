<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true">
			<block slot="backText">返回</block>
			<block slot="content">我的足迹</block>
		</cu-custom>
		<view class="cu-list menu-avatar">
			<navigator class="goods-item cu-item" hover-class="none" 
				:url="'/pages/goods/goods-detail/index?id=' + item.goodsSpu.id"
				v-for="(item, index) in userFootprint" :key="index">
				<view class="cu-avatar image-box" :style="'background-image:url(' + item.goodsSpu.picUrls[0] + ');'"></view>
				<view class="padding-right-sm goods-detail">
					<view class="text-df overflow-2">{{item.goodsSpu.name}}</view>
					<view class="text-gray text-sm overflow-1">{{item.goodsSpu.sellPoint}}</view>
					<view class="text-sm">已售{{item.goodsSpu.saleNum}}</view>
					<view class="flex justify-between align-center">
						<view class="text-price text-xl text-bold text-red">{{item.goodsSpu.priceDown}}</view>
						<view class="margin-right-sm text-gray">{{item.createTime}}</view>
					</view>
				</view>
			</navigator>
		</view>
		<view :class="'cu-load bg-gray ' + (loadmore?'loading':'over')"></view>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2018-2019
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	const util = require("utils/util.js");
	const app = getApp();
	import api from 'utils/api'

	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				page: {
					searchCount: false,
					current: 1,
					size: 10,
					ascs: '',
					//升序字段
					descs: 'create_time'
				},
				parameter: {},
				loadmore: true,
				userFootprint: []
			};
		},

		components: {},
		props: {},

		onLoad(options) {
			// 保存别人分享来的 userCode
			util.saveSharerUserCode(options);
			app.initPage().then(res => {
				this.userFootprintPage();
			});
		},

		onShow(options) {},

		onReachBottom() {
			if (this.loadmore) {
				this.page.current = this.page.current + 1;
				this.userFootprintPage();
			}
		},

		methods: {
			
			userFootprintPage() {
				api.userFootprintPage(Object.assign({}, this.page, util.filterForm(this.parameter))).then(res => {
					let userFootprint = res.data.records;
					this.userFootprint = [...this.userFootprint, ...userFootprint];
					if (userFootprint.length < this.page.size) {
						this.loadmore = false;
					}
				});
			},
		}
	};
</script>
<style>
	.collection-types {
		top: unset !important;
	}
	
	.goods-item{
		height: 260rpx !important;
	}
	
	.store-item{
		height: 260rpx !important;
	}
	
	.image-box{
		width: 200rpx;
		height: 200rpx;
	}
	
	.goods-detail{
		width: 480rpx;
	}
	
	.store-detail{
		width: 480rpx;
	}
</style>
