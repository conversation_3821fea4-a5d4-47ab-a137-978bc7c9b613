<template>
	<view>
		<view class="cu-custom" :style="[{height:CustomBar + 'px', backgroundColor: bgColor}]">
			<view class="cu-bar fixed" :style="style" :class="[bgImage!=''?'none-bg text-white bg-img':'',bgColor]">
				<slot name="left"></slot>
				<view class="action" @tap="BackPage" v-if="CanBack && isBack">
					<text class="cuIcon-back"></text>
					<slot name="backText"></slot>
				</view>
				<view class="action" @tap="GoHome" v-else-if="!CanBack && isBack">
					<text class="cuIcon-home">首页</text>
				</view>
				<view class="content" :style="[{top:StatusBar + 'px'}]">
					<slot name="content"></slot>
				</view>
				<slot name="right"></slot>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				StatusBar: this.StatusBar,
				CustomBar: this.CustomBar,
				CanBack: true
			};
		},
		name: 'cu-custom',
		computed: {
			style() {
				var StatusBar = this.StatusBar;
				var CustomBar = this.CustomBar;
				var bgImage = this.bgImage;
				var textColor = this.textColor;
				var bgColor = this.bgColor;
				var style =
					`height:${CustomBar}px;padding-top:${StatusBar}px;color: ${textColor};background-color: ${bgColor};`;
				if (this.bgImage) {
					style = `${style}background-image:url(${bgImage});`;
				}
				return style
			}
		},
		props: {
			textColor: {
				type: String,
				default: '#FFFFFF'
			},
			bgColor: {
				type: String,
				default: ''
			},
			isBack: {
				type: [Boolean, String],
				default: false
			},
			bgImage: {
				type: String,
				default: ''
			},
		},
		// 组件所在页面的生命周期函数
		mounted: function() {
			let pages = getCurrentPages()
			if (pages.length <= 1 || pages[pages.length - 1].route == 'pages/login/index') { //判断是否能返回
				this.CanBack = false
			} else {
				this.CanBack = true
			}
		},
		methods: {
			BackPage() {
				uni.navigateBack({
					delta: 1
				});
			},
			GoHome() {
				uni.switchTab({
					url: '/pages/home/<USER>'
				});
			}
		}
	}
</script>

<style>

</style>
