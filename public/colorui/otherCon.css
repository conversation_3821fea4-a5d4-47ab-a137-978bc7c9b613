.df {
	display: flex;
}

.dfc {
	display: flex;
	flex-flow: row;
	justify-content: center;
	align-items: center;
}

.f1 {
	flex: 1;
	overflow: hidden;
}

.fs0 {
	flex-shrink: 0;
}

.flr {
	flex-flow: row;
}

.flrw {
	flex-flow: row wrap;
}

.flc {
	flex-flow: column;
}

.flcw {
	flex-flow: column wrap;
}

.jcc {
	justify-content: center;
}

.jc-fs {
	justify-content: flex-start;
}

.jc-fe {
	justify-content: flex-end;
}

.jc-sa {
	justify-content: space-around;
}

.jc-sb {
	justify-content: space-between;
}

.alc {
	align-items: center;
}

.als {
	align-items: flex-start;
}

.ale {
	align-items: flex-end;
}

.w100 {
	width: 100%;
}

.h100 {
	height: 100%;
}

.wv100 {
	width: 100vw;
}

.hv100 {
	height: 100vh;
}

.wh100 {
	width: 100%;
	height: 100%;
}

.whv100 {
	width: 100vw;
	height: 100vh;
}

.bc1 {
	background-color: #FFFFFF;
}

.bc2 {
	background-color: #FF6203;
}

.bc3 {
	background-color: #F2F2F2;
}

.bc4 {
	background-color: #F8F8F8;
}

.bc5 {
	background-color: #F6F6F6;
}

.bc6 {
	background-color: #FF4200;
}

.bc7 {
	background-color: #FFE4D9;
}

.fc1 {
	color: #FFFFFF;
}

.fc2 {
	color: #2A2A2A;
}

.fc3 {
	color: #4A4A4A;
}

.fc4 {
	color: #878787;
}

.fc5 {
	color: #242424;
}

.fc6 {
	color: #6F6F6F;
}

.fc7 {
	color: #FF6203;
}

.fc8 {
	color: #515151;
}

.fc9 {
	color: #000000;
}

.fc10 {
	color: #787878;
}

.fc11 {
	color: #B34901;
}

.fc12 {
	color: #777777;
}

.fc13 {
	color: #D5620F;
}

.fc14 {
	color: #A5A5A5;
}

.fc15 {
	color: #7C7C7C;
}

.fc16 {
	color: #FA2C19;
}

.fwb {
	font-weight: bold;
}

.single-line-hide {
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 1; /* 设置行数 */
	overflow: hidden;
}
