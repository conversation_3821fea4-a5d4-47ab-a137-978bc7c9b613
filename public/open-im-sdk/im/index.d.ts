import Emitter from "../event";
import { InitConfig, WsResponse, AtMsgParams, ImageMsgParams, SoundMsgParams, VideoMsgParams, FileMsgParams, MergerMsgParams, LocationMsgParams, CustomMsgParams, QuoteMsgParams, SendMsgParams, GetHistoryMsgParams, InsertSingleMsgParams, TypingUpdateParams, MarkC2CParams, GetOneCveParams, SetDraftParams, PinCveParams, AddFriendParams, InviteGroupParams, GetGroupMemberParams, CreateGroupParams, JoinGroupParams, TransferGroupParams, AccessGroupParams, SplitParams, AccessFriendParams, GroupInfoParams, RemarkFriendParams, PartialUserItem, isRecvParams, SearchLocalParams, InsertGroupMsgParams, FaceMessageParams, RtcInvite, RtcActionParams, GroupMsgReadParams, ChangeGroupMuteParams, ChangeGroupMemberMuteParams, setPrvParams, MarkNotiParams, FileMsgFullParams, SouondMsgFullParams, VideoMsgFullParams, MemberNameParams, AdvancedMsgParams, GetSubDepParams, SearchInOrzParams, SetGroupRoleParams, SetGroupVerificationParams, SearchFriendParams, OptType, SearchGroupParams, GetGroupMemberByTimeParams, SearchGroupMemberParams, AdvancedQuoteMsgParams, SetMemberAuthParams, GetAdvancedHistoryMsgParams, FindMessageParams } from "../types";
export default class OpenIMSDK extends Emitter {
    private ws;
    private uid;
    private token;
    private platform;
    private wsUrl;
    private lock;
    private logoutFlag;
    private ws2promise;
    private onceFlag;
    private timer;
    private lastTime;
    private heartbeatCount;
    private heartbeatStartTime;
    private platformID;
    private isBatch;
    private worker;
    constructor();
    /**
     *
     * @description init and login OpenIMSDK
     * @param uid userID
     * @param token token
     * @param url service url
     * @param platformID platformID
     * @param operationID? unique operation ID
     * @returns
     */
    login(config: InitConfig): Promise<WsResponse>;
    private iLogin;
    logout(operationID?: string): Promise<WsResponse>;
    getLoginStatus: (operationID?: string | undefined) => Promise<WsResponse>;
    getLoginUser: (operationID?: string | undefined) => Promise<WsResponse>;
    getSelfUserInfo: (operationID?: string | undefined) => Promise<WsResponse>;
    getUsersInfo: (data: string[], operationID?: string | undefined) => Promise<WsResponse>;
    setSelfInfo: (data: PartialUserItem, operationID?: string | undefined) => Promise<WsResponse>;
    createTextMessage: (data: string, operationID?: string | undefined) => Promise<WsResponse>;
    createTextAtMessage: (data: AtMsgParams, operationID?: string | undefined) => Promise<WsResponse>;
    createAdvancedTextMessage: (data: AdvancedMsgParams, operationID?: string | undefined) => Promise<WsResponse>;
    createImageMessage: (data: ImageMsgParams, operationID?: string | undefined) => Promise<WsResponse>;
    createSoundMessage: (data: SoundMsgParams, operationID?: string | undefined) => Promise<WsResponse>;
    createVideoMessage: (data: VideoMsgParams, operationID?: string | undefined) => Promise<WsResponse>;
    createFileMessage: (data: FileMsgParams, operationID?: string | undefined) => Promise<WsResponse>;
    createFileMessageFromFullPath: (data: FileMsgFullParams, operationID?: string | undefined) => Promise<WsResponse>;
    createImageMessageFromFullPath: (data: string, operationID?: string | undefined) => Promise<WsResponse>;
    createSoundMessageFromFullPath: (data: SouondMsgFullParams, operationID?: string | undefined) => Promise<WsResponse>;
    createVideoMessageFromFullPath: (data: VideoMsgFullParams, operationID?: string | undefined) => Promise<WsResponse>;
    createMergerMessage: (data: MergerMsgParams, operationID?: string | undefined) => Promise<WsResponse>;
    createForwardMessage: (data: string, operationID?: string | undefined) => Promise<WsResponse>;
    createFaceMessage: (data: FaceMessageParams, operationID?: string | undefined) => Promise<WsResponse>;
    createLocationMessage: (data: LocationMsgParams, operationID?: string | undefined) => Promise<WsResponse>;
    createCustomMessage: (data: CustomMsgParams, operationID?: string | undefined) => Promise<WsResponse>;
    createQuoteMessage: (data: QuoteMsgParams, operationID?: string | undefined) => Promise<WsResponse>;
    createAdvancedQuoteMessage: (data: AdvancedQuoteMsgParams, operationID?: string | undefined) => Promise<WsResponse>;
    createCardMessage: (data: string, operationID?: string | undefined) => Promise<WsResponse>;
    sendMessage: (data: SendMsgParams, operationID?: string | undefined) => Promise<WsResponse>;
    sendMessageNotOss: (data: SendMsgParams, operationID?: string | undefined) => Promise<WsResponse>;
    getHistoryMessageList: (data: GetHistoryMsgParams, operationID?: string | undefined) => Promise<WsResponse>;
    getAdvancedHistoryMessageList: (data: GetAdvancedHistoryMsgParams, operationID?: string | undefined) => Promise<WsResponse>;
    getHistoryMessageListReverse: (data: GetHistoryMsgParams, operationID?: string | undefined) => Promise<WsResponse>;
    revokeMessage: (data: string, operationID?: string | undefined) => Promise<WsResponse>;
    setOneConversationPrivateChat: (data: setPrvParams, operationID?: string | undefined) => Promise<WsResponse>;
    deleteMessageFromLocalStorage: (data: string, operationID?: string | undefined) => Promise<WsResponse>;
    deleteMessageFromLocalAndSvr: (data: string, operationID?: string | undefined) => Promise<WsResponse>;
    deleteConversationFromLocalAndSvr: (data: string, operationID?: string | undefined) => Promise<WsResponse>;
    deleteAllConversationFromLocal: (operationID?: string | undefined) => Promise<WsResponse>;
    deleteAllMsgFromLocal: (operationID?: string | undefined) => Promise<WsResponse>;
    deleteAllMsgFromLocalAndSvr: (operationID?: string | undefined) => Promise<WsResponse>;
    markGroupMessageHasRead: (data: string, operationID?: string | undefined) => Promise<WsResponse>;
    markGroupMessageAsRead: (data: GroupMsgReadParams, operationID?: string | undefined) => Promise<WsResponse>;
    insertSingleMessageToLocalStorage: (data: InsertSingleMsgParams, operationID?: string | undefined) => Promise<WsResponse>;
    insertGroupMessageToLocalStorage: (data: InsertGroupMsgParams, operationID?: string | undefined) => Promise<WsResponse>;
    typingStatusUpdate: (data: TypingUpdateParams, operationID?: string | undefined) => Promise<WsResponse>;
    markC2CMessageAsRead: (data: MarkC2CParams, operationID?: string | undefined) => Promise<WsResponse>;
    markNotifyMessageHasRead: (conversationID: string, operationID?: string | undefined) => void;
    markMessageAsReadByConID: (data: MarkNotiParams, operationID?: string | undefined) => Promise<WsResponse>;
    clearC2CHistoryMessage: (data: string, operationID?: string | undefined) => Promise<WsResponse>;
    clearC2CHistoryMessageFromLocalAndSvr: (data: string, operationID?: string | undefined) => Promise<WsResponse>;
    clearGroupHistoryMessage: (data: string, operationID?: string | undefined) => Promise<WsResponse>;
    clearGroupHistoryMessageFromLocalAndSvr: (data: string, operationID?: string | undefined) => Promise<WsResponse>;
    getAllConversationList: (operationID?: string | undefined) => Promise<WsResponse>;
    getConversationListSplit: (data: SplitParams, operationID?: string | undefined) => Promise<WsResponse>;
    getOneConversation: (data: GetOneCveParams, operationID?: string | undefined) => Promise<WsResponse>;
    getConversationIDBySessionType: (data: GetOneCveParams, operationID?: string | undefined) => Promise<WsResponse>;
    getMultipleConversation: (data: string[], operationID?: string | undefined) => Promise<WsResponse>;
    deleteConversation: (data: string, operationID?: string | undefined) => Promise<WsResponse>;
    setConversationDraft: (data: SetDraftParams, operationID?: string | undefined) => Promise<WsResponse>;
    pinConversation: (data: PinCveParams, operationID?: string | undefined) => Promise<WsResponse>;
    getTotalUnreadMsgCount: (operationID?: string | undefined) => Promise<WsResponse>;
    getConversationRecvMessageOpt: (data: string[], operationID?: string | undefined) => Promise<WsResponse>;
    setConversationRecvMessageOpt: (data: isRecvParams, operationID?: string | undefined) => Promise<WsResponse>;
    searchLocalMessages: (data: SearchLocalParams, operationID?: string | undefined) => Promise<WsResponse>;
    addFriend: (data: AddFriendParams, operationID?: string | undefined) => Promise<WsResponse>;
    searchFriends: (data: SearchFriendParams, operationID?: string | undefined) => Promise<WsResponse>;
    getDesignatedFriendsInfo: (data: string[], operationID?: string | undefined) => Promise<WsResponse>;
    getRecvFriendApplicationList: (operationID?: string | undefined) => Promise<WsResponse>;
    getSendFriendApplicationList: (operationID?: string | undefined) => Promise<WsResponse>;
    getFriendList: (operationID?: string | undefined) => Promise<WsResponse>;
    setFriendRemark: (data: RemarkFriendParams, operationID?: string | undefined) => Promise<WsResponse>;
    checkFriend: (data: string[], operationID?: string | undefined) => Promise<WsResponse>;
    acceptFriendApplication: (data: AccessFriendParams, operationID?: string | undefined) => Promise<WsResponse>;
    refuseFriendApplication: (data: AccessFriendParams, operationID?: string | undefined) => Promise<WsResponse>;
    deleteFriend: (data: string, operationID?: string | undefined) => Promise<WsResponse>;
    addBlack: (data: string, operationID?: string | undefined) => Promise<WsResponse>;
    removeBlack: (data: string, operationID?: string | undefined) => Promise<WsResponse>;
    getBlackList: (operationID?: string | undefined) => Promise<WsResponse>;
    inviteUserToGroup: (data: InviteGroupParams, operationID?: string | undefined) => Promise<WsResponse>;
    kickGroupMember: (data: InviteGroupParams, operationID?: string | undefined) => Promise<WsResponse>;
    getGroupMembersInfo: (data: Omit<InviteGroupParams, "reason">, operationID?: string | undefined) => Promise<WsResponse>;
    getGroupMemberList: (data: GetGroupMemberParams, operationID?: string | undefined) => Promise<WsResponse>;
    getGroupMemberListByJoinTimeFilter: (data: GetGroupMemberByTimeParams, operationID?: string | undefined) => Promise<WsResponse>;
    searchGroupMembers: (data: SearchGroupMemberParams, operationID?: string | undefined) => Promise<WsResponse>;
    setGroupApplyMemberFriend: (data: SetMemberAuthParams, operationID?: string | undefined) => Promise<WsResponse>;
    setGroupLookMemberInfo: (data: SetMemberAuthParams, operationID?: string | undefined) => Promise<WsResponse>;
    getJoinedGroupList: (operationID?: string | undefined) => Promise<WsResponse>;
    createGroup: (data: CreateGroupParams, operationID?: string | undefined) => Promise<WsResponse>;
    setGroupInfo: (data: GroupInfoParams, operationID?: string | undefined) => Promise<WsResponse>;
    setGroupMemberNickname: (data: MemberNameParams, operationID?: string | undefined) => Promise<WsResponse>;
    getGroupsInfo: (data: string[], operationID?: string | undefined) => Promise<WsResponse>;
    joinGroup: (data: JoinGroupParams, operationID?: string | undefined) => Promise<WsResponse>;
    searchGroups: (data: SearchGroupParams, operationID?: string | undefined) => Promise<WsResponse>;
    quitGroup: (data: string, operationID?: string | undefined) => Promise<WsResponse>;
    dismissGroup: (data: string, operationID?: string | undefined) => Promise<WsResponse>;
    changeGroupMute: (data: ChangeGroupMuteParams, operationID?: string | undefined) => Promise<WsResponse>;
    changeGroupMemberMute: (data: ChangeGroupMemberMuteParams, operationID?: string | undefined) => Promise<WsResponse>;
    transferGroupOwner: (data: TransferGroupParams, operationID?: string | undefined) => Promise<WsResponse>;
    getSendGroupApplicationList: (operationID?: string | undefined) => Promise<WsResponse>;
    getRecvGroupApplicationList: (operationID?: string | undefined) => Promise<WsResponse>;
    acceptGroupApplication: (data: AccessGroupParams, operationID?: string | undefined) => Promise<WsResponse>;
    refuseGroupApplication: (data: AccessGroupParams, operationID?: string | undefined) => Promise<WsResponse>;
    signalingInvite: (data: RtcInvite, operationID?: string | undefined) => Promise<WsResponse>;
    signalingInviteInGroup: (data: RtcInvite, operationID?: string | undefined) => Promise<WsResponse>;
    signalingAccept: (data: RtcActionParams, operationID?: string | undefined) => Promise<WsResponse>;
    signalingReject: (data: RtcActionParams, operationID?: string | undefined) => Promise<WsResponse>;
    signalingCancel: (data: RtcActionParams, operationID?: string | undefined) => Promise<WsResponse>;
    signalingHungUp: (data: RtcActionParams, operationID?: string | undefined) => Promise<WsResponse>;
    getSubDepartment: (data: GetSubDepParams, operationID?: string | undefined) => Promise<WsResponse>;
    getDepartmentMember: (data: GetSubDepParams, operationID?: string | undefined) => Promise<WsResponse>;
    getUserInDepartment: (userID: string, operationID?: string | undefined) => Promise<WsResponse>;
    getDepartmentMemberAndSubDepartment: (departmentID: string, operationID?: string | undefined) => Promise<WsResponse>;
    getDepartmentInfo: (departmentID: string, operationID?: string | undefined) => Promise<WsResponse>;
    searchOrganization: (data: SearchInOrzParams, operationID?: string | undefined) => Promise<WsResponse>;
    resetConversationGroupAtType: (data: string, operationID?: string | undefined) => Promise<WsResponse>;
    setGroupMemberRoleLevel: (data: SetGroupRoleParams, operationID?: string | undefined) => Promise<WsResponse>;
    setGroupVerification: (data: SetGroupVerificationParams, operationID?: string | undefined) => Promise<WsResponse>;
    setGlobalRecvMessageOpt: (data: {
        opt: OptType;
    }, operationID?: string | undefined) => Promise<WsResponse>;
    newRevokeMessage: (data: string, operationID?: string | undefined) => Promise<WsResponse>;
    findMessageList: (data: FindMessageParams, operationID?: string | undefined) => Promise<WsResponse>;
    private wsSend;
    private getPlatform;
    private createWs;
    private reconnect;
    private clearTimer;
    private heartbeat;
}
