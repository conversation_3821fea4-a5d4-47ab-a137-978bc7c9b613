var e,n,r=function(){function e(){this.events=void 0,this.events={}}var n=e.prototype;return n.emit=function(e,n){return this.events[e]&&this.events[e].forEach(function(e){return e(n)}),this},n.on=function(e,n){return this.events[e]?this.events[e].push(n):this.events[e]=[n],this},n.off=function(e,n){if(e&&"function"==typeof n){var r=this.events[e],t=r.findIndex(function(e){return e===n});r.splice(t,1)}else this.events[e]=[];return this},e}();function t(){return t=Object.assign||function(e){for(var n=1;n<arguments.length;n++){var r=arguments[n];for(var t in r)Object.prototype.hasOwnProperty.call(r,t)&&(e[t]=r[t])}return e},t.apply(this,arguments)}function i(e,n){return i=Object.setPrototypeOf||function(e,n){return e.__proto__=n,e},i(e,n)}!function(e){e.INITSDK="InitSDK",e.LOGIN="Login",e.LOGOUT="Logout",e.GETLOGINSTATUS="GetLoginStatus",e.GETLOGINUSER="GetLoginUser",e.GETSELFUSERINFO="GetSelfUserInfo",e.CREATETEXTMESSAGE="CreateTextMessage",e.CREATETEXTATMESSAGE="CreateTextAtMessage",e.CREATEADVANCEDTEXTMESSAGE="CreateAdvancedTextMessage",e.CREATEIMAGEMESSAGEFROMBYURL="CreateImageMessageByURL",e.CREATESOUNDMESSAGEBYURL="CreateSoundMessageByURL",e.CREATEVIDEOMESSAGEBYURL="CreateVideoMessageByURL",e.CREATEFILEMESSAGEBYURL="CreateFileMessageByURL",e.CREATEIMAGEMESSAGEFROMFULLPATH="CreateImageMessageFromFullPath",e.CREATESOUNDMESSAGEFROMFULLPATH="CreateSoundMessageFromFullPath",e.CREATEVIDEOMESSAGEFROMFULLPATH="CreateVideoMessageFromFullPath",e.CREATEFILEMESSAGEFROMFULLPATH="CreateFileMessageFromFullPath",e.CREATELOCATIONMESSAGE="CreateLocationMessage",e.CREATECUSTOMMESSAGE="CreateCustomMessage",e.CREATEMERGERMESSAGE="CreateMergerMessage",e.CREATEFORWARDMESSAGE="CreateForwardMessage",e.CREATEQUOTEMESSAGE="CreateQuoteMessage",e.CREATEADVANCEDQUOTEMESSAGE="CreateAdvancedQuoteMessage",e.CREATECARDMESSAGE="CreateCardMessage",e.CREATEFACEMESSAGE="CreateFaceMessage",e.SENDMESSAGE="SendMessage",e.SENDMESSAGENOTOSS="SendMessageNotOss",e.GETHISTORYMESSAGELIST="GetHistoryMessageList",e.GETADVANCEDHISTORYMESSAGELIST="GetAdvancedHistoryMessageList",e.GETHISTORYMESSAGELISTREVERSE="GetHistoryMessageListReverse",e.REVOKEMESSAGE="RevokeMessage",e.SETONECONVERSATIONPRIVATECHAT="SetOneConversationPrivateChat",e.DELETEMESSAGEFROMLOCALSTORAGE="DeleteMessageFromLocalStorage",e.DELETEMESSAGEFROMLOCALANDSVR="DeleteMessageFromLocalAndSvr",e.DELETECONVERSATIONFROMLOCALANDSVR="DeleteConversationFromLocalAndSvr",e.DELETEALLCONVERSATIONFROMLOCAL="DeleteAllConversationFromLocal",e.DELETEALLMSGFROMLOCALANDSVR="DeleteAllMsgFromLocalAndSvr",e.DELETEALLMSGFROMLOCAL="DeleteAllMsgFromLocal",e.MARKSINGLEMESSAGEHASREAD="MarkSingleMessageHasRead",e.INSERTSINGLEMESSAGETOLOCALSTORAGE="InsertSingleMessageToLocalStorage",e.INSERTGROUPMESSAGETOLOCALSTORAGE="InsertGroupMessageToLocalStorage",e.TYPINGSTATUSUPDATE="TypingStatusUpdate",e.MARKC2CMESSAGEASREAD="MarkC2CMessageAsRead",e.MARKMESSAGEASREADBYCONID="MarkMessageAsReadByConID",e.CLEARC2CHISTORYMESSAGE="ClearC2CHistoryMessage",e.CLEARC2CHISTORYMESSAGEFROMLOCALANDSVR="ClearC2CHistoryMessageFromLocalAndSvr",e.CLEARGROUPHISTORYMESSAGE="ClearGroupHistoryMessage",e.CLEARGROUPHISTORYMESSAGEFROMLOCALANDSVR="ClearGroupHistoryMessageFromLocalAndSvr",e.ADDFRIEND="AddFriend",e.SEARCHFRIENDS="SearchFriends",e.GETDESIGNATEDFRIENDSINFO="GetDesignatedFriendsInfo",e.GETRECVFRIENDAPPLICATIONLIST="GetRecvFriendApplicationList",e.GETSENDFRIENDAPPLICATIONLIST="GetSendFriendApplicationList",e.GETFRIENDLIST="GetFriendList",e.SETFRIENDREMARK="SetFriendRemark",e.ADDBLACK="AddBlack",e.GETBLACKLIST="GetBlackList",e.REMOVEBLACK="RemoveBlack",e.CHECKFRIEND="CheckFriend",e.ACCEPTFRIENDAPPLICATION="AcceptFriendApplication",e.REFUSEFRIENDAPPLICATION="RefuseFriendApplication",e.DELETEFRIEND="DeleteFriend",e.GETUSERSINFO="GetUsersInfo",e.SETSELFINFO="SetSelfInfo",e.GETALLCONVERSATIONLIST="GetAllConversationList",e.GETCONVERSATIONLISTSPLIT="GetConversationListSplit",e.GETONECONVERSATION="GetOneConversation",e.GETCONVERSATIONIDBYSESSIONTYPE="GetConversationIDBySessionType",e.GETMULTIPLECONVERSATION="GetMultipleConversation",e.DELETECONVERSATION="DeleteConversation",e.SETCONVERSATIONDRAFT="SetConversationDraft",e.PINCONVERSATION="PinConversation",e.GETTOTALUNREADMSGCOUNT="GetTotalUnreadMsgCount",e.GETCONVERSATIONRECVMESSAGEOPT="GetConversationRecvMessageOpt",e.SETCONVERSATIONRECVMESSAGEOPT="SetConversationRecvMessageOpt",e.SEARCHLOCALMESSAGES="SearchLocalMessages",e.MARKGROUPMESSAGEHASREAD="MarkGroupMessageHasRead",e.MARKGROUPMESSAGEASREAD="MarkGroupMessageAsRead",e.INVITEUSERTOGROUP="InviteUserToGroup",e.KICKGROUPMEMBER="KickGroupMember",e.GETGROUPMEMBERSINFO="GetGroupMembersInfo",e.GETGROUPMEMBERLIST="GetGroupMemberList",e.GETGROUPMEMBERLISTBYJOINTIMEFILTER="GetGroupMemberListByJoinTimeFilter",e.SEARCHGROUPMEMBERS="SearchGroupMembers",e.SETGROUPAPPLYMEMBERFRIEND="SetGroupApplyMemberFriend",e.SETGROUPLOOKMEMBERINFO="SetGroupLookMemberInfo",e.GETJOINEDGROUPLIST="GetJoinedGroupList",e.CREATEGROUP="CreateGroup",e.SETGROUPINFO="SetGroupInfo",e.SETGROUPMEMBERNICKNAME="SetGroupMemberNickname",e.GETGROUPSINFO="GetGroupsInfo",e.JOINGROUP="JoinGroup",e.SEARCHGROUPS="SearchGroups",e.QUITGROUP="QuitGroup",e.DISMISSGROUP="DismissGroup",e.CHANGEGROUPMUTE="ChangeGroupMute",e.CHANGEGROUPMEMBERMUTE="ChangeGroupMemberMute",e.TRANSFERGROUPOWNER="TransferGroupOwner",e.GETSENDGROUPAPPLICATIONLIST="GetSendGroupApplicationList",e.GETRECVGROUPAPPLICATIONLIST="GetRecvGroupApplicationList",e.ACCEPTGROUPAPPLICATION="AcceptGroupApplication",e.REFUSEGROUPAPPLICATION="RefuseGroupApplication",e.SIGNAL_INGINVITE="SignalingInvite",e.SIGNALINGINVITEINGROUP="SignalingInviteInGroup",e.SIGNALINGACCEPT="SignalingAccept",e.SIGNALINGREJECT="SignalingReject",e.SIGNALINGCANCEL="SignalingCancel",e.SIGNALINGHUNGUP="SignalingHungUp",e.GETSUBDEPARTMENT="GetSubDepartment",e.GETDEPARTMENTMEMBER="GetDepartmentMember",e.GETUSERINDEPARTMENT="GetUserInDepartment",e.GETDEPARTMENTMEMBERANDSUBDEPARTMENT="GetDepartmentMemberAndSubDepartment",e.GETDEPARTMENTINFO="GetDepartmentInfo",e.SEARCHORGANIZATION="SearchOrganization",e.RESETCONVERSATIONGROUPATTYPE="ResetConversationGroupAtType",e.SETGROUPMEMBERROLELEVEL="SetGroupMemberRoleLevel",e.SETGROUPVERIFICATION="SetGroupVerification",e.SETGLOBALRECVMESSAGEOPT="SetGlobalRecvMessageOpt",e.NEWREVOKEMESSAGE="NewRevokeMessage",e.FINDMESSAGELIST="FindMessageList"}(e||(e={})),function(e){e.ONCONNECTFAILED="OnConnectFailed",e.ONCONNECTSUCCESS="OnConnectSuccess",e.ONCONNECTING="OnConnecting",e.ONKICKEDOFFLINE="OnKickedOffline",e.ONSELFINFOUPDATED="OnSelfInfoUpdated",e.ONUSERTOKENEXPIRED="OnUserTokenExpired",e.ONPROGRESS="OnProgress",e.ONRECVNEWMESSAGE="OnRecvNewMessage",e.ONRECVNEWMESSAGES="OnRecvNewMessages",e.ONRECVMESSAGEREVOKED="OnRecvMessageRevoked",e.ONRECVC2CREADRECEIPT="OnRecvC2CReadReceipt",e.ONRECVGROUPREADRECEIPT="OnRecvGroupReadReceipt",e.ONCONVERSATIONCHANGED="OnConversationChanged",e.ONNEWCONVERSATION="OnNewConversation",e.ONSYNCSERVERFAILED="OnSyncServerFailed",e.ONSYNCSERVERFINISH="OnSyncServerFinish",e.ONSYNCSERVERSTART="OnSyncServerStart",e.ONTOTALUNREADMESSAGECOUNTCHANGED="OnTotalUnreadMessageCountChanged",e.ONBLACKADDED="OnBlackAdded",e.ONBLACKDELETED="OnBlackDeleted",e.ONFRIENDAPPLICATIONACCEPTED="OnFriendApplicationAccepted",e.ONFRIENDAPPLICATIONADDED="OnFriendApplicationAdded",e.ONFRIENDAPPLICATIONDELETED="OnFriendApplicationDeleted",e.ONFRIENDAPPLICATIONREJECTED="OnFriendApplicationRejected",e.ONFRIENDINFOCHANGED="OnFriendInfoChanged",e.ONFRIENDADDED="OnFriendAdded",e.ONFRIENDDELETED="OnFriendDeleted",e.ONJOINEDGROUPADDED="OnJoinedGroupAdded",e.ONJOINEDGROUPDELETED="OnJoinedGroupDeleted",e.ONGROUPMEMBERADDED="OnGroupMemberAdded",e.ONGROUPMEMBERDELETED="OnGroupMemberDeleted",e.ONGROUPAPPLICATIONADDED="OnGroupApplicationAdded",e.ONGROUPAPPLICATIONDELETED="OnGroupApplicationDeleted",e.ONGROUPINFOCHANGED="OnGroupInfoChanged",e.ONGROUPMEMBERINFOCHANGED="OnGroupMemberInfoChanged",e.ONGROUPAPPLICATIONACCEPTED="OnGroupApplicationAccepted",e.ONGROUPAPPLICATIONREJECTED="OnGroupApplicationRejected",e.ONRECEIVENEWINVITATION="OnReceiveNewInvitation",e.ONINVITEEACCEPTED="OnInviteeAccepted",e.ONINVITEEREJECTED="OnInviteeRejected",e.ONINVITATIONCANCELLED="OnInvitationCancelled",e.ONHANGUP="OnHangUp",e.ONINVITATIONTIMEOUT="OnInvitationTimeout",e.ONINVITEEACCEPTEDBYOTHERDEVICE="OnInviteeAcceptedByOtherDevice",e.ONINVITEEREJECTEDBYOTHERDEVICE="OnInviteeRejectedByOtherDevice",e.ONORGANIZATIONUPDATED="OnOrganizationUpdated",e.ONRECVNEWMESSAGEFROMOTHERWEB="OnRecvNewMessageFromOtherWeb",e.ONNEWRECVMESSAGEREVOKED="OnNewRecvMessageRevoked"}(n||(n={}));var o,a,u,E,s,S,I,d,c,A,N,O,D=function(e){try{e&&e.terminate()}catch(e){console.log(e)}},R=function(e){return(36*Math.random()).toString(36).slice(2)+(new Date).getTime().toString()+e},T=function(r){var o,a;function u(){var i;return(i=r.call(this)||this).ws=void 0,i.uid=void 0,i.token=void 0,i.platform="web",i.wsUrl="",i.lock=!1,i.logoutFlag=!1,i.ws2promise={},i.onceFlag=!0,i.timer=void 0,i.lastTime=0,i.heartbeatCount=0,i.heartbeatStartTime=0,i.platformID=0,i.isBatch=!1,i.worker=null,i.getLoginStatus=function(n){return new Promise(function(r,t){var o=n||R(i.uid);i.wsSend({reqFuncName:e.GETLOGINSTATUS,operationID:o,userID:i.uid,data:""},r,t)})},i.getLoginUser=function(n){return new Promise(function(r,t){var o=n||R(i.uid);i.wsSend({reqFuncName:e.GETLOGINUSER,operationID:o,userID:i.uid,data:""},r,t)})},i.getSelfUserInfo=function(n){return new Promise(function(r,t){var o=n||R(i.uid);i.wsSend({reqFuncName:e.GETSELFUSERINFO,operationID:o,userID:i.uid,data:""},r,t)})},i.getUsersInfo=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.GETUSERSINFO,operationID:a,userID:i.uid,data:n},t,o)})},i.setSelfInfo=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.SETSELFINFO,operationID:a,userID:i.uid,data:n},t,o)})},i.createTextMessage=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.CREATETEXTMESSAGE,operationID:a,userID:i.uid,data:n},t,o)})},i.createTextAtMessage=function(n,r){return new Promise(function(o,a){var u=t({},n);u.atUserIDList=JSON.stringify(u.atUserIDList),u.atUsersInfo=JSON.stringify(u.atUsersInfo);var E=r||R(i.uid);i.wsSend({reqFuncName:e.CREATETEXTATMESSAGE,operationID:E,userID:i.uid,data:u},o,a)})},i.createAdvancedTextMessage=function(n,r){return new Promise(function(o,a){var u=t({},n);u.messageEntityList=JSON.stringify(u.messageEntityList);var E=r||R(i.uid);i.wsSend({reqFuncName:e.CREATEADVANCEDTEXTMESSAGE,operationID:E,userID:i.uid,data:u},o,a)})},i.createImageMessage=function(n,r){return new Promise(function(o,a){var u=r||R(i.uid),E=t({},n);E.bigPicture=JSON.stringify(E.bigPicture),E.snapshotPicture=JSON.stringify(E.snapshotPicture),E.sourcePicture=JSON.stringify(E.sourcePicture);var s={reqFuncName:e.CREATEIMAGEMESSAGEFROMBYURL,operationID:u,userID:i.uid,data:JSON.stringify(E)};i.wsSend(s,o,a)})},i.createSoundMessage=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid),u={soundBaseInfo:JSON.stringify(n)},E={reqFuncName:e.CREATESOUNDMESSAGEBYURL,operationID:a,userID:i.uid,data:JSON.stringify(u)};i.wsSend(E,t,o)})},i.createVideoMessage=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid),u={videoBaseInfo:JSON.stringify(n)},E={reqFuncName:e.CREATEVIDEOMESSAGEBYURL,operationID:a,userID:i.uid,data:JSON.stringify(u)};i.wsSend(E,t,o)})},i.createFileMessage=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid),u={fileBaseInfo:JSON.stringify(n)},E={reqFuncName:e.CREATEFILEMESSAGEBYURL,operationID:a,userID:i.uid,data:JSON.stringify(u)};i.wsSend(E,t,o)})},i.createFileMessageFromFullPath=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.CREATEFILEMESSAGEFROMFULLPATH,operationID:a,userID:i.uid,data:n},t,o)})},i.createImageMessageFromFullPath=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.CREATEIMAGEMESSAGEFROMFULLPATH,operationID:a,userID:i.uid,data:n},t,o)})},i.createSoundMessageFromFullPath=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.CREATESOUNDMESSAGEFROMFULLPATH,operationID:a,userID:i.uid,data:n},t,o)})},i.createVideoMessageFromFullPath=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.CREATEVIDEOMESSAGEFROMFULLPATH,operationID:a,userID:i.uid,data:n},t,o)})},i.createMergerMessage=function(n,r){return new Promise(function(o,a){var u=r||R(i.uid),E=t({},n);E.messageList=JSON.stringify(n.messageList),E.summaryList=JSON.stringify(n.summaryList),i.wsSend({reqFuncName:e.CREATEMERGERMESSAGE,operationID:u,userID:i.uid,data:E},o,a)})},i.createForwardMessage=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.CREATEFORWARDMESSAGE,operationID:a,userID:i.uid,data:n},t,o)})},i.createFaceMessage=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.CREATEFACEMESSAGE,operationID:a,userID:i.uid,data:n},t,o)})},i.createLocationMessage=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.CREATELOCATIONMESSAGE,operationID:a,userID:i.uid,data:n},t,o)})},i.createCustomMessage=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.CREATECUSTOMMESSAGE,operationID:a,userID:i.uid,data:n},t,o)})},i.createQuoteMessage=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.CREATEQUOTEMESSAGE,operationID:a,userID:i.uid,data:n},t,o)})},i.createAdvancedQuoteMessage=function(n,r){return new Promise(function(o,a){var u=r||R(i.uid),E=t({},n);E.messageEntityList=JSON.stringify(E.messageEntityList),i.wsSend({reqFuncName:e.CREATEADVANCEDQUOTEMESSAGE,operationID:u,userID:i.uid,data:E},o,a)})},i.createCardMessage=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.CREATECARDMESSAGE,operationID:a,userID:i.uid,data:n},t,o)})},i.sendMessage=function(n,r){return new Promise(function(o,a){var u=r||R(i.uid),E=t({},n);E.offlinePushInfo=E.offlinePushInfo?JSON.stringify(n.offlinePushInfo):"",i.wsSend({reqFuncName:e.SENDMESSAGE,operationID:u,userID:i.uid,data:E},o,a)})},i.sendMessageNotOss=function(n,r){return new Promise(function(o,a){var u=r||R(i.uid),E=t({},n);E.offlinePushInfo=E.offlinePushInfo?JSON.stringify(n.offlinePushInfo):"",i.wsSend({reqFuncName:e.SENDMESSAGENOTOSS,operationID:u,userID:i.uid,data:E},o,a)})},i.getHistoryMessageList=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.GETHISTORYMESSAGELIST,operationID:a,userID:i.uid,data:n},t,o)})},i.getAdvancedHistoryMessageList=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.GETADVANCEDHISTORYMESSAGELIST,operationID:a,userID:i.uid,data:n},t,o)})},i.getHistoryMessageListReverse=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.GETHISTORYMESSAGELISTREVERSE,operationID:a,userID:i.uid,data:n},t,o)})},i.revokeMessage=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.REVOKEMESSAGE,operationID:a,userID:i.uid,data:n},t,o)})},i.setOneConversationPrivateChat=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.SETONECONVERSATIONPRIVATECHAT,operationID:a,userID:i.uid,data:n},t,o)})},i.deleteMessageFromLocalStorage=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.DELETEMESSAGEFROMLOCALSTORAGE,operationID:a,userID:i.uid,data:n},t,o)})},i.deleteMessageFromLocalAndSvr=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.DELETEMESSAGEFROMLOCALANDSVR,operationID:a,userID:i.uid,data:n},t,o)})},i.deleteConversationFromLocalAndSvr=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.DELETECONVERSATIONFROMLOCALANDSVR,operationID:a,userID:i.uid,data:n},t,o)})},i.deleteAllConversationFromLocal=function(n){return new Promise(function(r,t){var o=n||R(i.uid);i.wsSend({reqFuncName:e.DELETEALLCONVERSATIONFROMLOCAL,operationID:o,userID:i.uid,data:""},r,t)})},i.deleteAllMsgFromLocal=function(n){return new Promise(function(r,t){var o=n||R(i.uid);i.wsSend({reqFuncName:e.DELETEALLMSGFROMLOCAL,operationID:o,userID:i.uid,data:""},r,t)})},i.deleteAllMsgFromLocalAndSvr=function(n){return new Promise(function(r,t){var o=n||R(i.uid);i.wsSend({reqFuncName:e.DELETEALLMSGFROMLOCALANDSVR,operationID:o,userID:i.uid,data:""},r,t)})},i.markGroupMessageHasRead=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.MARKGROUPMESSAGEHASREAD,operationID:a,userID:i.uid,data:n},t,o)})},i.markGroupMessageAsRead=function(n,r){return new Promise(function(o,a){var u=r||R(i.uid),E=t({},n);E.msgIDList=JSON.stringify(E.msgIDList),i.wsSend({reqFuncName:e.MARKGROUPMESSAGEASREAD,operationID:u,userID:i.uid,data:E},o,a)})},i.insertSingleMessageToLocalStorage=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.INSERTSINGLEMESSAGETOLOCALSTORAGE,operationID:a,userID:i.uid,data:n},t,o)})},i.insertGroupMessageToLocalStorage=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.INSERTGROUPMESSAGETOLOCALSTORAGE,operationID:a,userID:i.uid,data:n},t,o)})},i.typingStatusUpdate=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.TYPINGSTATUSUPDATE,operationID:a,userID:i.uid,data:n},t,o)})},i.markC2CMessageAsRead=function(n,r){return new Promise(function(o,a){var u=t({},n);u.msgIDList=JSON.stringify(u.msgIDList);var E=r||R(i.uid);i.wsSend({reqFuncName:e.MARKC2CMESSAGEASREAD,operationID:E,userID:i.uid,data:u},o,a)})},i.markNotifyMessageHasRead=function(e,n){i.markMessageAsReadByConID({conversationID:e,msgIDList:[]})},i.markMessageAsReadByConID=function(n,r){return new Promise(function(o,a){var u=t({},n);u.msgIDList=JSON.stringify(u.msgIDList);var E=r||R(i.uid);i.wsSend({reqFuncName:e.MARKMESSAGEASREADBYCONID,operationID:E,userID:i.uid,data:u},o,a)})},i.clearC2CHistoryMessage=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.CLEARC2CHISTORYMESSAGE,operationID:a,userID:i.uid,data:n},t,o)})},i.clearC2CHistoryMessageFromLocalAndSvr=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.CLEARC2CHISTORYMESSAGEFROMLOCALANDSVR,operationID:a,userID:i.uid,data:n},t,o)})},i.clearGroupHistoryMessage=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.CLEARGROUPHISTORYMESSAGE,operationID:a,userID:i.uid,data:n},t,o)})},i.clearGroupHistoryMessageFromLocalAndSvr=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.CLEARGROUPHISTORYMESSAGEFROMLOCALANDSVR,operationID:a,userID:i.uid,data:n},t,o)})},i.getAllConversationList=function(n){return new Promise(function(r,t){var o=n||R(i.uid);i.wsSend({reqFuncName:e.GETALLCONVERSATIONLIST,operationID:o,userID:i.uid,data:""},r,t)})},i.getConversationListSplit=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.GETCONVERSATIONLISTSPLIT,operationID:a,userID:i.uid,data:n},t,o)})},i.getOneConversation=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.GETONECONVERSATION,operationID:a,userID:i.uid,data:n},t,o)})},i.getConversationIDBySessionType=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.GETCONVERSATIONIDBYSESSIONTYPE,operationID:a,userID:i.uid,data:n},t,o)})},i.getMultipleConversation=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.GETMULTIPLECONVERSATION,operationID:a,userID:i.uid,data:n},t,o)})},i.deleteConversation=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.DELETECONVERSATION,operationID:a,userID:i.uid,data:n},t,o)})},i.setConversationDraft=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.SETCONVERSATIONDRAFT,operationID:a,userID:i.uid,data:n},t,o)})},i.pinConversation=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.PINCONVERSATION,operationID:a,userID:i.uid,data:n},t,o)})},i.getTotalUnreadMsgCount=function(n){return new Promise(function(r,t){var o=n||R(i.uid);i.wsSend({reqFuncName:e.GETTOTALUNREADMSGCOUNT,operationID:o,userID:i.uid,data:""},r,t)})},i.getConversationRecvMessageOpt=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.GETCONVERSATIONRECVMESSAGEOPT,operationID:a,userID:i.uid,data:n},t,o)})},i.setConversationRecvMessageOpt=function(n,r){return new Promise(function(o,a){var u=r||R(i.uid),E=t({},n);E.conversationIDList=JSON.stringify(n.conversationIDList),i.wsSend({reqFuncName:e.SETCONVERSATIONRECVMESSAGEOPT,operationID:u,userID:i.uid,data:E},o,a)})},i.searchLocalMessages=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.SEARCHLOCALMESSAGES,operationID:a,userID:i.uid,data:n},t,o)})},i.addFriend=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.ADDFRIEND,operationID:a,userID:i.uid,data:n},t,o)})},i.searchFriends=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.SEARCHFRIENDS,operationID:a,userID:i.uid,data:n},t,o)})},i.getDesignatedFriendsInfo=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.GETDESIGNATEDFRIENDSINFO,operationID:a,userID:i.uid,data:n},t,o)})},i.getRecvFriendApplicationList=function(n){return new Promise(function(r,t){var o=n||R(i.uid);i.wsSend({reqFuncName:e.GETRECVFRIENDAPPLICATIONLIST,operationID:o,userID:i.uid,data:""},r,t)})},i.getSendFriendApplicationList=function(n){return new Promise(function(r,t){var o=n||R(i.uid);i.wsSend({reqFuncName:e.GETSENDFRIENDAPPLICATIONLIST,operationID:o,userID:i.uid,data:""},r,t)})},i.getFriendList=function(n){return new Promise(function(r,t){var o=n||R(i.uid);i.wsSend({reqFuncName:e.GETFRIENDLIST,operationID:o,userID:i.uid,data:""},r,t)})},i.setFriendRemark=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.SETFRIENDREMARK,operationID:a,userID:i.uid,data:n},t,o)})},i.checkFriend=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.CHECKFRIEND,operationID:a,userID:i.uid,data:n},t,o)})},i.acceptFriendApplication=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.ACCEPTFRIENDAPPLICATION,operationID:a,userID:i.uid,data:n},t,o)})},i.refuseFriendApplication=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.REFUSEFRIENDAPPLICATION,operationID:a,userID:i.uid,data:n},t,o)})},i.deleteFriend=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.DELETEFRIEND,operationID:a,userID:i.uid,data:n},t,o)})},i.addBlack=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.ADDBLACK,operationID:a,userID:i.uid,data:n},t,o)})},i.removeBlack=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.REMOVEBLACK,operationID:a,userID:i.uid,data:n},t,o)})},i.getBlackList=function(n){return new Promise(function(r,t){var o=n||R(i.uid);i.wsSend({reqFuncName:e.GETBLACKLIST,operationID:o,userID:i.uid,data:""},r,t)})},i.inviteUserToGroup=function(n,r){return new Promise(function(o,a){var u=r||R(i.uid),E=t({},n);E.userIDList=JSON.stringify(E.userIDList),i.wsSend({reqFuncName:e.INVITEUSERTOGROUP,operationID:u,userID:i.uid,data:E},o,a)})},i.kickGroupMember=function(n,r){return new Promise(function(o,a){var u=r||R(i.uid),E=t({},n);E.userIDList=JSON.stringify(E.userIDList),i.wsSend({reqFuncName:e.KICKGROUPMEMBER,operationID:u,userID:i.uid,data:E},o,a)})},i.getGroupMembersInfo=function(n,r){return new Promise(function(o,a){var u=r||R(i.uid),E=t({},n);E.userIDList=JSON.stringify(E.userIDList),i.wsSend({reqFuncName:e.GETGROUPMEMBERSINFO,operationID:u,userID:i.uid,data:E},o,a)})},i.getGroupMemberList=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.GETGROUPMEMBERLIST,operationID:a,userID:i.uid,data:n},t,o)})},i.getGroupMemberListByJoinTimeFilter=function(n,r){return new Promise(function(o,a){var u=r||R(i.uid),E=t({},n);E.filterUserIDList=JSON.stringify(E.filterUserIDList),i.wsSend({reqFuncName:e.GETGROUPMEMBERLISTBYJOINTIMEFILTER,operationID:u,userID:i.uid,data:E},o,a)})},i.searchGroupMembers=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid),u={reqFuncName:e.SEARCHGROUPMEMBERS,operationID:a,userID:i.uid,data:{searchParam:JSON.stringify(n)}};i.wsSend(u,t,o)})},i.setGroupApplyMemberFriend=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.SETGROUPAPPLYMEMBERFRIEND,operationID:a,userID:i.uid,data:n},t,o)})},i.setGroupLookMemberInfo=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.SETGROUPLOOKMEMBERINFO,operationID:a,userID:i.uid,data:n},t,o)})},i.getJoinedGroupList=function(n){return new Promise(function(r,t){var o=n||R(i.uid);i.wsSend({reqFuncName:e.GETJOINEDGROUPLIST,operationID:o,userID:i.uid,data:""},r,t)})},i.createGroup=function(n,r){return new Promise(function(o,a){var u=r||R(i.uid),E=t({},n);E.groupBaseInfo=JSON.stringify(E.groupBaseInfo),E.memberList=JSON.stringify(E.memberList),i.wsSend({reqFuncName:e.CREATEGROUP,operationID:u,userID:i.uid,data:E},o,a)})},i.setGroupInfo=function(n,r){return new Promise(function(o,a){var u=r||R(i.uid),E=t({},n);E.groupInfo=JSON.stringify(E.groupInfo),i.wsSend({reqFuncName:e.SETGROUPINFO,operationID:u,userID:i.uid,data:E},o,a)})},i.setGroupMemberNickname=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.SETGROUPMEMBERNICKNAME,operationID:a,userID:i.uid,data:n},t,o)})},i.getGroupsInfo=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.GETGROUPSINFO,operationID:a,userID:i.uid,data:n},t,o)})},i.joinGroup=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.JOINGROUP,operationID:a,userID:i.uid,data:n},t,o)})},i.searchGroups=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.SEARCHGROUPS,operationID:a,userID:i.uid,data:n},t,o)})},i.quitGroup=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.QUITGROUP,operationID:a,userID:i.uid,data:n},t,o)})},i.dismissGroup=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.DISMISSGROUP,operationID:a,userID:i.uid,data:n},t,o)})},i.changeGroupMute=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.CHANGEGROUPMUTE,operationID:a,userID:i.uid,data:n},t,o)})},i.changeGroupMemberMute=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.CHANGEGROUPMEMBERMUTE,operationID:a,userID:i.uid,data:n},t,o)})},i.transferGroupOwner=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.TRANSFERGROUPOWNER,operationID:a,userID:i.uid,data:n},t,o)})},i.getSendGroupApplicationList=function(n){return new Promise(function(r,t){var o=n||R(i.uid);i.wsSend({reqFuncName:e.GETSENDGROUPAPPLICATIONLIST,operationID:o,userID:i.uid,data:""},r,t)})},i.getRecvGroupApplicationList=function(n){return new Promise(function(r,t){var o=n||R(i.uid);i.wsSend({reqFuncName:e.GETRECVGROUPAPPLICATIONLIST,operationID:o,userID:i.uid,data:""},r,t)})},i.acceptGroupApplication=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.ACCEPTGROUPAPPLICATION,operationID:a,userID:i.uid,data:n},t,o)})},i.refuseGroupApplication=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.REFUSEGROUPAPPLICATION,operationID:a,userID:i.uid,data:n},t,o)})},i.signalingInvite=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid),u={};u.invitation=n,i.wsSend({reqFuncName:e.SIGNAL_INGINVITE,operationID:a,userID:i.uid,data:u},t,o)})},i.signalingInviteInGroup=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid),u={};u.invitation=n,i.wsSend({reqFuncName:e.SIGNALINGINVITEINGROUP,operationID:a,userID:i.uid,data:u},t,o)})},i.signalingAccept=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.SIGNALINGACCEPT,operationID:a,userID:i.uid,data:n},t,o)})},i.signalingReject=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.SIGNALINGREJECT,operationID:a,userID:i.uid,data:n},t,o)})},i.signalingCancel=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.SIGNALINGCANCEL,operationID:a,userID:i.uid,data:n},t,o)})},i.signalingHungUp=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.SIGNALINGHUNGUP,operationID:a,userID:i.uid,data:n},t,o)})},i.getSubDepartment=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.GETSUBDEPARTMENT,operationID:a,userID:i.uid,data:n},t,o)})},i.getDepartmentMember=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.GETDEPARTMENTMEMBER,operationID:a,userID:i.uid,data:n},t,o)})},i.getUserInDepartment=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.GETUSERINDEPARTMENT,operationID:a,userID:i.uid,data:n},t,o)})},i.getDepartmentMemberAndSubDepartment=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.GETDEPARTMENTMEMBERANDSUBDEPARTMENT,operationID:a,userID:i.uid,data:n},t,o)})},i.getDepartmentInfo=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.GETDEPARTMENTINFO,operationID:a,userID:i.uid,data:n},t,o)})},i.searchOrganization=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid),u=n;u.input=JSON.stringify(u.input),i.wsSend({reqFuncName:e.SEARCHORGANIZATION,operationID:a,userID:i.uid,data:u},t,o)})},i.resetConversationGroupAtType=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.RESETCONVERSATIONGROUPATTYPE,operationID:a,userID:i.uid,data:n},t,o)})},i.setGroupMemberRoleLevel=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.SETGROUPMEMBERROLELEVEL,operationID:a,userID:i.uid,data:n},t,o)})},i.setGroupVerification=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.SETGROUPVERIFICATION,operationID:a,userID:i.uid,data:n},t,o)})},i.setGlobalRecvMessageOpt=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.SETGLOBALRECVMESSAGEOPT,operationID:a,userID:i.uid,data:n},t,o)})},i.newRevokeMessage=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.NEWREVOKEMESSAGE,operationID:a,userID:i.uid,data:n},t,o)})},i.findMessageList=function(n,r){return new Promise(function(t,o){var a=r||R(i.uid);i.wsSend({reqFuncName:e.FINDMESSAGELIST,operationID:a,userID:i.uid,data:n},t,o)})},i.wsSend=function(r,t,o){var a,u,E;if(null==(a=window)||!a.navigator||window.navigator.onLine)if((null==(u=i.ws)?void 0:u.readyState)===(null==(E=i.ws)?void 0:E.OPEN)){"object"==typeof r.data&&(r.data=JSON.stringify(r.data));var s={oid:r.operationID||R(i.uid),mname:r.reqFuncName,mrsve:t,mrjet:o,flag:!1};i.ws2promise[s.oid]=s;var S=function(r){i.lastTime=(new Date).getTime();var t=JSON.parse(r.data);if(n[t.event.toUpperCase()])i.emit(t.event,t);else{t.event===e.LOGOUT&&i.ws2promise[t.operationID]&&(i.logoutFlag=!0,i.ws.close(),i.ws=void 0);var o=i.ws2promise[t.operationID];o?(0===t.errCode?o.mrsve(t):o.mrjet(t),delete i.ws2promise[t.operationID]):t.event!==e.SENDMESSAGE&&t.event!==e.SENDMESSAGENOTOSS||i.emit(n.ONRECVNEWMESSAGEFROMOTHERWEB,t)}};try{"web"==i.platform?(i.ws.send(JSON.stringify(r)),i.ws.onmessage=S):(i.ws.send({data:JSON.stringify(r),success:function(e){"uni"===i.platform&&void 0!==i.ws._callbacks&&void 0!==i.ws._callbacks.message&&(i.ws._callbacks.message=[])}}),i.onceFlag&&(i.ws.onMessage(S),i.onceFlag=!1))}catch(e){return void o({event:r.reqFuncName,errCode:112,errMsg:"no ws conect...",data:"",operationID:r.operationID||""})}r.reqFuncName===e.LOGOUT&&(i.onceFlag=!0)}else o({event:r.reqFuncName,errCode:112,errMsg:"ws conecting...",data:"",operationID:r.operationID||""});else o({event:r.reqFuncName,errCode:113,errMsg:"net work error",data:"",operationID:r.operationID||""})},i.getPlatform(),i}a=r,(o=u).prototype=Object.create(a.prototype),o.prototype.constructor=o,i(o,a);var E=u.prototype;return E.login=function(n){var r=this;return new Promise(function(t,i){var o=n.userID,a=n.token,u=n.platformID,E=n.isBatch,s=void 0!==E&&E,S=n.operationID;r.wsUrl=n.url+"?sendID="+o+"&token="+a+"&platformID="+u,r.platformID=u;var I={userID:o,token:a},d={event:e.LOGIN,errCode:0,errMsg:"",data:"",operationID:S||""};r.createWs(function(){r.uid=o,r.token=a,r.isBatch=s,r.iLogin(I,S).then(function(e){r.logoutFlag=!1,r.heartbeat(),t(e)}).catch(function(e){d.errCode=e.errCode,d.errMsg=e.errMsg,i(d)})},function(){d.errCode=111,d.errMsg="ws connect close...",r.logoutFlag||Object.values(r.ws2promise).forEach(function(e){return e.mrjet({event:e.mname,errCode:111,errMsg:"ws connect close...",data:"",operationID:e.oid})}),i(d)},function(e){console.log(e),d.errCode=112,d.errMsg="ws connect error...",i(d)}),r.ws||(d.errCode=112,d.errMsg="The current platform is not supported...",i(d))})},E.iLogin=function(n,r){var t=this;return new Promise(function(i,o){var a=r||R(t.uid);t.wsSend({reqFuncName:e.LOGIN,operationID:a,userID:t.uid,data:n,batchMsg:t.isBatch?1:0},i,o)})},E.logout=function(n){var r=this;return new Promise(function(t,i){var o=n||R(r.uid);r.wsSend({reqFuncName:e.LOGOUT,operationID:o,userID:r.uid,data:""},t,i)})},E.getPlatform=function(){var e=typeof WebSocket,n=typeof uni,r=typeof wx;"undefined"===e?("object"===r&&(this.platform="wx"),"object"===n&&(this.platform="uni"),this.platform="unknow"):this.platform="web"},E.createWs=function(e,n,r){var t=this;return console.log("start createWs..."),new Promise(function(i,o){var a;null==(a=t.ws)||a.close(),t.ws=void 0;var u=function(){t.iLogin({userID:t.uid,token:t.token}).then(function(e){t.logoutFlag=!1,console.log("iLogin suc..."),t.heartbeat(),i()})};e&&(u=e);var E=function(){console.log("ws close agin:::"),t.logoutFlag||Object.values(t.ws2promise).forEach(function(e){return e.mrjet({event:e.mname,errCode:111,errMsg:"ws connect close...",data:"",operationID:e.oid})})};n&&(E=n);var s=function(){};if(r&&(s=r),"web"===t.platform)return t.ws=new WebSocket(t.wsUrl),t.ws.onclose=E,t.ws.onopen=u,void(t.ws.onerror=s);var S="uni"===t.platform?uni:wx;t.ws=S.connectSocket({url:t.wsUrl,complete:function(){}}),t.ws.onClose(E),t.ws.onOpen(u),t.ws.onError(s)})},E.reconnect=function(){var e=this;this.onceFlag||(this.onceFlag=!0),this.lock||(this.lock=!0,this.clearTimer(),this.timer=setTimeout(function(){e.createWs(),e.lock=!1},500))},E.clearTimer=function(){this.timer&&clearTimeout(this.timer)},E.heartbeat=function(){var e,n,r,t,i=this;console.log("start heartbeat..."),this.clearTimer(),this.worker&&D(this.worker);try{this.worker=(e=function(){var e,n,r,t;i.logoutFlag?i.worker&&D(i.worker):(null==(e=i.ws)?void 0:e.readyState)===(null==(n=i.ws)?void 0:n.CONNECTING)||(null==(r=i.ws)?void 0:r.readyState)===(null==(t=i.ws)?void 0:t.OPEN)?(new Date).getTime()-i.lastTime<9e3||i.getLoginStatus().catch(function(e){return i.reconnect()}):i.reconnect()},n=new Blob(["(function (e) {\n      setInterval(function () {\n        this.postMessage(null)\n      }, 10000)\n    })()"]),r=window.URL.createObjectURL(n),(t=new Worker(r)).onmessage=e,t)}catch(e){}},u}(r);!function(e){e[e.Nomal=0]="Nomal",e[e.Mute=1]="Mute",e[e.WithoutNotify=2]="WithoutNotify"}(o||(o={})),function(e){e[e.Allowed=0]="Allowed",e[e.NotAllowed=1]="NotAllowed"}(a||(a={})),function(e){e[e.NomalGroup=0]="NomalGroup",e[e.SuperGroup=1]="SuperGroup",e[e.WorkingGroup=2]="WorkingGroup"}(u||(u={})),function(e){e[e.ApplyNeedInviteNot=0]="ApplyNeedInviteNot",e[e.AllNeed=1]="AllNeed",e[e.AllNot=2]="AllNot"}(E||(E={})),function(e){e[e.Nomal=0]="Nomal",e[e.Baned=1]="Baned",e[e.Dismissed=2]="Dismissed",e[e.Muted=3]="Muted"}(s||(s={})),function(e){e[e.Invitation=2]="Invitation",e[e.Search=3]="Search",e[e.QrCode=4]="QrCode"}(S||(S={})),function(e){e[e.Nomal=1]="Nomal",e[e.Owner=2]="Owner",e[e.Admin=3]="Admin"}(I||(I={})),function(e){e[e.AtNormal=0]="AtNormal",e[e.AtMe=1]="AtMe",e[e.AtAll=2]="AtAll",e[e.AtAllAtMe=3]="AtAllAtMe",e[e.AtGroupNotice=4]="AtGroupNotice"}(d||(d={})),function(e){e[e.Sending=1]="Sending",e[e.Succeed=2]="Succeed",e[e.Failed=3]="Failed"}(c||(c={})),function(e){e[e.iOS=1]="iOS",e[e.Android=2]="Android",e[e.Windows=3]="Windows",e[e.MacOSX=4]="MacOSX",e[e.Web=5]="Web",e[e.Linux=7]="Linux",e[e.Admin=8]="Admin"}(A||(A={})),function(e){e[e.TEXTMESSAGE=101]="TEXTMESSAGE",e[e.PICTUREMESSAGE=102]="PICTUREMESSAGE",e[e.VOICEMESSAGE=103]="VOICEMESSAGE",e[e.VIDEOMESSAGE=104]="VIDEOMESSAGE",e[e.FILEMESSAGE=105]="FILEMESSAGE",e[e.ATTEXTMESSAGE=106]="ATTEXTMESSAGE",e[e.MERGERMESSAGE=107]="MERGERMESSAGE",e[e.CARDMESSAGE=108]="CARDMESSAGE",e[e.LOCATIONMESSAGE=109]="LOCATIONMESSAGE",e[e.CUSTOMMESSAGE=110]="CUSTOMMESSAGE",e[e.REVOKEMESSAGE=111]="REVOKEMESSAGE",e[e.HASREADRECEIPTMESSAGE=112]="HASREADRECEIPTMESSAGE",e[e.TYPINGMESSAGE=113]="TYPINGMESSAGE",e[e.QUOTEMESSAGE=114]="QUOTEMESSAGE",e[e.FACEMESSAGE=115]="FACEMESSAGE",e[e.ADVANCETEXTMESSAGE=117]="ADVANCETEXTMESSAGE",e[e.ADVANCEREVOKEMESSAGE=118]="ADVANCEREVOKEMESSAGE",e[e.CUSTOMMSGNOTTRIGGERCONVERSATION=119]="CUSTOMMSGNOTTRIGGERCONVERSATION",e[e.CUSTOMMSGONLINEONLY=120]="CUSTOMMSGONLINEONLY",e[e.FRIENDAPPLICATIONAPPROVED=1201]="FRIENDAPPLICATIONAPPROVED",e[e.FRIENDAPPLICATIONREJECTED=1202]="FRIENDAPPLICATIONREJECTED",e[e.FRIENDAPPLICATIONADDED=1203]="FRIENDAPPLICATIONADDED",e[e.FRIENDADDED=1204]="FRIENDADDED",e[e.FRIENDDELETED=1205]="FRIENDDELETED",e[e.FRIENDREMARKSET=1206]="FRIENDREMARKSET",e[e.BLACKADDED=1207]="BLACKADDED",e[e.BLACKDELETED=1208]="BLACKDELETED",e[e.SELFINFOUPDATED=1303]="SELFINFOUPDATED",e[e.NOTIFICATION=1400]="NOTIFICATION",e[e.GROUPCREATED=1501]="GROUPCREATED",e[e.GROUPINFOUPDATED=1502]="GROUPINFOUPDATED",e[e.JOINGROUPAPPLICATIONADDED=1503]="JOINGROUPAPPLICATIONADDED",e[e.MEMBERQUIT=1504]="MEMBERQUIT",e[e.GROUPAPPLICATIONACCEPTED=1505]="GROUPAPPLICATIONACCEPTED",e[e.GROUPAPPLICATIONREJECTED=1506]="GROUPAPPLICATIONREJECTED",e[e.GROUPOWNERTRANSFERRED=1507]="GROUPOWNERTRANSFERRED",e[e.MEMBERKICKED=1508]="MEMBERKICKED",e[e.MEMBERINVITED=1509]="MEMBERINVITED",e[e.MEMBERENTER=1510]="MEMBERENTER",e[e.GROUPDISMISSED=1511]="GROUPDISMISSED",e[e.GROUPMEMBERMUTED=1512]="GROUPMEMBERMUTED",e[e.GROUPMEMBERCANCELMUTED=1513]="GROUPMEMBERCANCELMUTED",e[e.GROUPMUTED=1514]="GROUPMUTED",e[e.GROUPCANCELMUTED=1515]="GROUPCANCELMUTED",e[e.GROUPMEMBERINFOUPDATED=1516]="GROUPMEMBERINFOUPDATED",e[e.BURNMESSAGECHANGE=1701]="BURNMESSAGECHANGE"}(N||(N={})),function(e){e[e.Single=1]="Single",e[e.Group=2]="Group",e[e.SuperGroup=3]="SuperGroup",e[e.Notification=4]="Notification"}(O||(O={}));export{a as AllowType,n as CbEvents,d as GroupAtType,S as GroupJoinSource,I as GroupRole,s as GroupStatus,u as GroupType,E as GroupVerificationType,c as MessageStatus,N as MessageType,T as OpenIMSDK,o as OptType,A as Platform,e as RequestFunc,O as SessionType,r as emitter,R as uuid};
//# sourceMappingURL=index.esm.js.map
