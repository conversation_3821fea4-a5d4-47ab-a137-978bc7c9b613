var e,n,t=function(){function e(){this.events=void 0,this.events={}}var n=e.prototype;return n.emit=function(e,n){return this.events[e]&&this.events[e].forEach(function(e){return e(n)}),this},n.on=function(e,n){return this.events[e]?this.events[e].push(n):this.events[e]=[n],this},n.off=function(e,n){if(e&&"function"==typeof n){var t=this.events[e],r=t.findIndex(function(e){return e===n});t.splice(r,1)}else this.events[e]=[];return this},e}();function r(){return r=Object.assign||function(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e},r.apply(this,arguments)}function o(e,n){return o=Object.setPrototypeOf||function(e,n){return e.__proto__=n,e},o(e,n)}exports.RequestFunc=void 0,(e=exports.RequestFunc||(exports.RequestFunc={})).INITSDK="InitSDK",e.LOGIN="Login",e.LOGOUT="Logout",e.GETLOGINSTATUS="GetLoginStatus",e.GETLOGINUSER="GetLoginUser",e.GETSELFUSERINFO="GetSelfUserInfo",e.CREATETEXTMESSAGE="CreateTextMessage",e.CREATETEXTATMESSAGE="CreateTextAtMessage",e.CREATEADVANCEDTEXTMESSAGE="CreateAdvancedTextMessage",e.CREATEIMAGEMESSAGEFROMBYURL="CreateImageMessageByURL",e.CREATESOUNDMESSAGEBYURL="CreateSoundMessageByURL",e.CREATEVIDEOMESSAGEBYURL="CreateVideoMessageByURL",e.CREATEFILEMESSAGEBYURL="CreateFileMessageByURL",e.CREATEIMAGEMESSAGEFROMFULLPATH="CreateImageMessageFromFullPath",e.CREATESOUNDMESSAGEFROMFULLPATH="CreateSoundMessageFromFullPath",e.CREATEVIDEOMESSAGEFROMFULLPATH="CreateVideoMessageFromFullPath",e.CREATEFILEMESSAGEFROMFULLPATH="CreateFileMessageFromFullPath",e.CREATELOCATIONMESSAGE="CreateLocationMessage",e.CREATECUSTOMMESSAGE="CreateCustomMessage",e.CREATEMERGERMESSAGE="CreateMergerMessage",e.CREATEFORWARDMESSAGE="CreateForwardMessage",e.CREATEQUOTEMESSAGE="CreateQuoteMessage",e.CREATEADVANCEDQUOTEMESSAGE="CreateAdvancedQuoteMessage",e.CREATECARDMESSAGE="CreateCardMessage",e.CREATEFACEMESSAGE="CreateFaceMessage",e.SENDMESSAGE="SendMessage",e.SENDMESSAGENOTOSS="SendMessageNotOss",e.GETHISTORYMESSAGELIST="GetHistoryMessageList",e.GETADVANCEDHISTORYMESSAGELIST="GetAdvancedHistoryMessageList",e.GETHISTORYMESSAGELISTREVERSE="GetHistoryMessageListReverse",e.REVOKEMESSAGE="RevokeMessage",e.SETONECONVERSATIONPRIVATECHAT="SetOneConversationPrivateChat",e.DELETEMESSAGEFROMLOCALSTORAGE="DeleteMessageFromLocalStorage",e.DELETEMESSAGEFROMLOCALANDSVR="DeleteMessageFromLocalAndSvr",e.DELETECONVERSATIONFROMLOCALANDSVR="DeleteConversationFromLocalAndSvr",e.DELETEALLCONVERSATIONFROMLOCAL="DeleteAllConversationFromLocal",e.DELETEALLMSGFROMLOCALANDSVR="DeleteAllMsgFromLocalAndSvr",e.DELETEALLMSGFROMLOCAL="DeleteAllMsgFromLocal",e.MARKSINGLEMESSAGEHASREAD="MarkSingleMessageHasRead",e.INSERTSINGLEMESSAGETOLOCALSTORAGE="InsertSingleMessageToLocalStorage",e.INSERTGROUPMESSAGETOLOCALSTORAGE="InsertGroupMessageToLocalStorage",e.TYPINGSTATUSUPDATE="TypingStatusUpdate",e.MARKC2CMESSAGEASREAD="MarkC2CMessageAsRead",e.MARKMESSAGEASREADBYCONID="MarkMessageAsReadByConID",e.CLEARC2CHISTORYMESSAGE="ClearC2CHistoryMessage",e.CLEARC2CHISTORYMESSAGEFROMLOCALANDSVR="ClearC2CHistoryMessageFromLocalAndSvr",e.CLEARGROUPHISTORYMESSAGE="ClearGroupHistoryMessage",e.CLEARGROUPHISTORYMESSAGEFROMLOCALANDSVR="ClearGroupHistoryMessageFromLocalAndSvr",e.ADDFRIEND="AddFriend",e.SEARCHFRIENDS="SearchFriends",e.GETDESIGNATEDFRIENDSINFO="GetDesignatedFriendsInfo",e.GETRECVFRIENDAPPLICATIONLIST="GetRecvFriendApplicationList",e.GETSENDFRIENDAPPLICATIONLIST="GetSendFriendApplicationList",e.GETFRIENDLIST="GetFriendList",e.SETFRIENDREMARK="SetFriendRemark",e.ADDBLACK="AddBlack",e.GETBLACKLIST="GetBlackList",e.REMOVEBLACK="RemoveBlack",e.CHECKFRIEND="CheckFriend",e.ACCEPTFRIENDAPPLICATION="AcceptFriendApplication",e.REFUSEFRIENDAPPLICATION="RefuseFriendApplication",e.DELETEFRIEND="DeleteFriend",e.GETUSERSINFO="GetUsersInfo",e.SETSELFINFO="SetSelfInfo",e.GETALLCONVERSATIONLIST="GetAllConversationList",e.GETCONVERSATIONLISTSPLIT="GetConversationListSplit",e.GETONECONVERSATION="GetOneConversation",e.GETCONVERSATIONIDBYSESSIONTYPE="GetConversationIDBySessionType",e.GETMULTIPLECONVERSATION="GetMultipleConversation",e.DELETECONVERSATION="DeleteConversation",e.SETCONVERSATIONDRAFT="SetConversationDraft",e.PINCONVERSATION="PinConversation",e.GETTOTALUNREADMSGCOUNT="GetTotalUnreadMsgCount",e.GETCONVERSATIONRECVMESSAGEOPT="GetConversationRecvMessageOpt",e.SETCONVERSATIONRECVMESSAGEOPT="SetConversationRecvMessageOpt",e.SEARCHLOCALMESSAGES="SearchLocalMessages",e.MARKGROUPMESSAGEHASREAD="MarkGroupMessageHasRead",e.MARKGROUPMESSAGEASREAD="MarkGroupMessageAsRead",e.INVITEUSERTOGROUP="InviteUserToGroup",e.KICKGROUPMEMBER="KickGroupMember",e.GETGROUPMEMBERSINFO="GetGroupMembersInfo",e.GETGROUPMEMBERLIST="GetGroupMemberList",e.GETGROUPMEMBERLISTBYJOINTIMEFILTER="GetGroupMemberListByJoinTimeFilter",e.SEARCHGROUPMEMBERS="SearchGroupMembers",e.SETGROUPAPPLYMEMBERFRIEND="SetGroupApplyMemberFriend",e.SETGROUPLOOKMEMBERINFO="SetGroupLookMemberInfo",e.GETJOINEDGROUPLIST="GetJoinedGroupList",e.CREATEGROUP="CreateGroup",e.SETGROUPINFO="SetGroupInfo",e.SETGROUPMEMBERNICKNAME="SetGroupMemberNickname",e.GETGROUPSINFO="GetGroupsInfo",e.JOINGROUP="JoinGroup",e.SEARCHGROUPS="SearchGroups",e.QUITGROUP="QuitGroup",e.DISMISSGROUP="DismissGroup",e.CHANGEGROUPMUTE="ChangeGroupMute",e.CHANGEGROUPMEMBERMUTE="ChangeGroupMemberMute",e.TRANSFERGROUPOWNER="TransferGroupOwner",e.GETSENDGROUPAPPLICATIONLIST="GetSendGroupApplicationList",e.GETRECVGROUPAPPLICATIONLIST="GetRecvGroupApplicationList",e.ACCEPTGROUPAPPLICATION="AcceptGroupApplication",e.REFUSEGROUPAPPLICATION="RefuseGroupApplication",e.SIGNAL_INGINVITE="SignalingInvite",e.SIGNALINGINVITEINGROUP="SignalingInviteInGroup",e.SIGNALINGACCEPT="SignalingAccept",e.SIGNALINGREJECT="SignalingReject",e.SIGNALINGCANCEL="SignalingCancel",e.SIGNALINGHUNGUP="SignalingHungUp",e.GETSUBDEPARTMENT="GetSubDepartment",e.GETDEPARTMENTMEMBER="GetDepartmentMember",e.GETUSERINDEPARTMENT="GetUserInDepartment",e.GETDEPARTMENTMEMBERANDSUBDEPARTMENT="GetDepartmentMemberAndSubDepartment",e.GETDEPARTMENTINFO="GetDepartmentInfo",e.SEARCHORGANIZATION="SearchOrganization",e.RESETCONVERSATIONGROUPATTYPE="ResetConversationGroupAtType",e.SETGROUPMEMBERROLELEVEL="SetGroupMemberRoleLevel",e.SETGROUPVERIFICATION="SetGroupVerification",e.SETGLOBALRECVMESSAGEOPT="SetGlobalRecvMessageOpt",e.NEWREVOKEMESSAGE="NewRevokeMessage",e.FINDMESSAGELIST="FindMessageList",exports.CbEvents=void 0,(n=exports.CbEvents||(exports.CbEvents={})).ONCONNECTFAILED="OnConnectFailed",n.ONCONNECTSUCCESS="OnConnectSuccess",n.ONCONNECTING="OnConnecting",n.ONKICKEDOFFLINE="OnKickedOffline",n.ONSELFINFOUPDATED="OnSelfInfoUpdated",n.ONUSERTOKENEXPIRED="OnUserTokenExpired",n.ONPROGRESS="OnProgress",n.ONRECVNEWMESSAGE="OnRecvNewMessage",n.ONRECVNEWMESSAGES="OnRecvNewMessages",n.ONRECVMESSAGEREVOKED="OnRecvMessageRevoked",n.ONRECVC2CREADRECEIPT="OnRecvC2CReadReceipt",n.ONRECVGROUPREADRECEIPT="OnRecvGroupReadReceipt",n.ONCONVERSATIONCHANGED="OnConversationChanged",n.ONNEWCONVERSATION="OnNewConversation",n.ONSYNCSERVERFAILED="OnSyncServerFailed",n.ONSYNCSERVERFINISH="OnSyncServerFinish",n.ONSYNCSERVERSTART="OnSyncServerStart",n.ONTOTALUNREADMESSAGECOUNTCHANGED="OnTotalUnreadMessageCountChanged",n.ONBLACKADDED="OnBlackAdded",n.ONBLACKDELETED="OnBlackDeleted",n.ONFRIENDAPPLICATIONACCEPTED="OnFriendApplicationAccepted",n.ONFRIENDAPPLICATIONADDED="OnFriendApplicationAdded",n.ONFRIENDAPPLICATIONDELETED="OnFriendApplicationDeleted",n.ONFRIENDAPPLICATIONREJECTED="OnFriendApplicationRejected",n.ONFRIENDINFOCHANGED="OnFriendInfoChanged",n.ONFRIENDADDED="OnFriendAdded",n.ONFRIENDDELETED="OnFriendDeleted",n.ONJOINEDGROUPADDED="OnJoinedGroupAdded",n.ONJOINEDGROUPDELETED="OnJoinedGroupDeleted",n.ONGROUPMEMBERADDED="OnGroupMemberAdded",n.ONGROUPMEMBERDELETED="OnGroupMemberDeleted",n.ONGROUPAPPLICATIONADDED="OnGroupApplicationAdded",n.ONGROUPAPPLICATIONDELETED="OnGroupApplicationDeleted",n.ONGROUPINFOCHANGED="OnGroupInfoChanged",n.ONGROUPMEMBERINFOCHANGED="OnGroupMemberInfoChanged",n.ONGROUPAPPLICATIONACCEPTED="OnGroupApplicationAccepted",n.ONGROUPAPPLICATIONREJECTED="OnGroupApplicationRejected",n.ONRECEIVENEWINVITATION="OnReceiveNewInvitation",n.ONINVITEEACCEPTED="OnInviteeAccepted",n.ONINVITEEREJECTED="OnInviteeRejected",n.ONINVITATIONCANCELLED="OnInvitationCancelled",n.ONHANGUP="OnHangUp",n.ONINVITATIONTIMEOUT="OnInvitationTimeout",n.ONINVITEEACCEPTEDBYOTHERDEVICE="OnInviteeAcceptedByOtherDevice",n.ONINVITEEREJECTEDBYOTHERDEVICE="OnInviteeRejectedByOtherDevice",n.ONORGANIZATIONUPDATED="OnOrganizationUpdated",n.ONRECVNEWMESSAGEFROMOTHERWEB="OnRecvNewMessageFromOtherWeb",n.ONNEWRECVMESSAGEREVOKED="OnNewRecvMessageRevoked";var u,i,s,a,E,c,S,I,d,R,A,N,O=function(e){try{e&&e.terminate()}catch(e){console.log(e)}},D=function(e){return(36*Math.random()).toString(36).slice(2)+(new Date).getTime().toString()+e},p=function(e){var n,t;function u(){var n;return(n=e.call(this)||this).ws=void 0,n.uid=void 0,n.token=void 0,n.platform="web",n.wsUrl="",n.lock=!1,n.logoutFlag=!1,n.ws2promise={},n.onceFlag=!0,n.timer=void 0,n.lastTime=0,n.heartbeatCount=0,n.heartbeatStartTime=0,n.platformID=0,n.isBatch=!1,n.worker=null,n.getLoginStatus=function(e){return new Promise(function(t,r){var o=e||D(n.uid),u={reqFuncName:exports.RequestFunc.GETLOGINSTATUS,operationID:o,userID:n.uid,data:""};n.wsSend(u,t,r)})},n.getLoginUser=function(e){return new Promise(function(t,r){var o=e||D(n.uid),u={reqFuncName:exports.RequestFunc.GETLOGINUSER,operationID:o,userID:n.uid,data:""};n.wsSend(u,t,r)})},n.getSelfUserInfo=function(e){return new Promise(function(t,r){var o=e||D(n.uid),u={reqFuncName:exports.RequestFunc.GETSELFUSERINFO,operationID:o,userID:n.uid,data:""};n.wsSend(u,t,r)})},n.getUsersInfo=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.GETUSERSINFO,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.setSelfInfo=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.SETSELFINFO,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.createTextMessage=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.CREATETEXTMESSAGE,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.createTextAtMessage=function(e,t){return new Promise(function(o,u){var i=r({},e);i.atUserIDList=JSON.stringify(i.atUserIDList),i.atUsersInfo=JSON.stringify(i.atUsersInfo);var s=t||D(n.uid),a={reqFuncName:exports.RequestFunc.CREATETEXTATMESSAGE,operationID:s,userID:n.uid,data:i};n.wsSend(a,o,u)})},n.createAdvancedTextMessage=function(e,t){return new Promise(function(o,u){var i=r({},e);i.messageEntityList=JSON.stringify(i.messageEntityList);var s=t||D(n.uid),a={reqFuncName:exports.RequestFunc.CREATEADVANCEDTEXTMESSAGE,operationID:s,userID:n.uid,data:i};n.wsSend(a,o,u)})},n.createImageMessage=function(e,t){return new Promise(function(o,u){var i=t||D(n.uid),s=r({},e);s.bigPicture=JSON.stringify(s.bigPicture),s.snapshotPicture=JSON.stringify(s.snapshotPicture),s.sourcePicture=JSON.stringify(s.sourcePicture);var a={reqFuncName:exports.RequestFunc.CREATEIMAGEMESSAGEFROMBYURL,operationID:i,userID:n.uid,data:JSON.stringify(s)};n.wsSend(a,o,u)})},n.createSoundMessage=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={soundBaseInfo:JSON.stringify(e)},s={reqFuncName:exports.RequestFunc.CREATESOUNDMESSAGEBYURL,operationID:u,userID:n.uid,data:JSON.stringify(i)};n.wsSend(s,r,o)})},n.createVideoMessage=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={videoBaseInfo:JSON.stringify(e)},s={reqFuncName:exports.RequestFunc.CREATEVIDEOMESSAGEBYURL,operationID:u,userID:n.uid,data:JSON.stringify(i)};n.wsSend(s,r,o)})},n.createFileMessage=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={fileBaseInfo:JSON.stringify(e)},s={reqFuncName:exports.RequestFunc.CREATEFILEMESSAGEBYURL,operationID:u,userID:n.uid,data:JSON.stringify(i)};n.wsSend(s,r,o)})},n.createFileMessageFromFullPath=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.CREATEFILEMESSAGEFROMFULLPATH,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.createImageMessageFromFullPath=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.CREATEIMAGEMESSAGEFROMFULLPATH,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.createSoundMessageFromFullPath=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.CREATESOUNDMESSAGEFROMFULLPATH,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.createVideoMessageFromFullPath=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.CREATEVIDEOMESSAGEFROMFULLPATH,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.createMergerMessage=function(e,t){return new Promise(function(o,u){var i=t||D(n.uid),s=r({},e);s.messageList=JSON.stringify(e.messageList),s.summaryList=JSON.stringify(e.summaryList);var a={reqFuncName:exports.RequestFunc.CREATEMERGERMESSAGE,operationID:i,userID:n.uid,data:s};n.wsSend(a,o,u)})},n.createForwardMessage=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.CREATEFORWARDMESSAGE,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.createFaceMessage=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.CREATEFACEMESSAGE,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.createLocationMessage=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.CREATELOCATIONMESSAGE,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.createCustomMessage=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.CREATECUSTOMMESSAGE,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.createQuoteMessage=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.CREATEQUOTEMESSAGE,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.createAdvancedQuoteMessage=function(e,t){return new Promise(function(o,u){var i=t||D(n.uid),s=r({},e);s.messageEntityList=JSON.stringify(s.messageEntityList);var a={reqFuncName:exports.RequestFunc.CREATEADVANCEDQUOTEMESSAGE,operationID:i,userID:n.uid,data:s};n.wsSend(a,o,u)})},n.createCardMessage=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.CREATECARDMESSAGE,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.sendMessage=function(e,t){return new Promise(function(o,u){var i=t||D(n.uid),s=r({},e);s.offlinePushInfo=s.offlinePushInfo?JSON.stringify(e.offlinePushInfo):"";var a={reqFuncName:exports.RequestFunc.SENDMESSAGE,operationID:i,userID:n.uid,data:s};n.wsSend(a,o,u)})},n.sendMessageNotOss=function(e,t){return new Promise(function(o,u){var i=t||D(n.uid),s=r({},e);s.offlinePushInfo=s.offlinePushInfo?JSON.stringify(e.offlinePushInfo):"";var a={reqFuncName:exports.RequestFunc.SENDMESSAGENOTOSS,operationID:i,userID:n.uid,data:s};n.wsSend(a,o,u)})},n.getHistoryMessageList=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.GETHISTORYMESSAGELIST,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.getAdvancedHistoryMessageList=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.GETADVANCEDHISTORYMESSAGELIST,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.getHistoryMessageListReverse=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.GETHISTORYMESSAGELISTREVERSE,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.revokeMessage=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.REVOKEMESSAGE,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.setOneConversationPrivateChat=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.SETONECONVERSATIONPRIVATECHAT,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.deleteMessageFromLocalStorage=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.DELETEMESSAGEFROMLOCALSTORAGE,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.deleteMessageFromLocalAndSvr=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.DELETEMESSAGEFROMLOCALANDSVR,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.deleteConversationFromLocalAndSvr=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.DELETECONVERSATIONFROMLOCALANDSVR,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.deleteAllConversationFromLocal=function(e){return new Promise(function(t,r){var o=e||D(n.uid),u={reqFuncName:exports.RequestFunc.DELETEALLCONVERSATIONFROMLOCAL,operationID:o,userID:n.uid,data:""};n.wsSend(u,t,r)})},n.deleteAllMsgFromLocal=function(e){return new Promise(function(t,r){var o=e||D(n.uid),u={reqFuncName:exports.RequestFunc.DELETEALLMSGFROMLOCAL,operationID:o,userID:n.uid,data:""};n.wsSend(u,t,r)})},n.deleteAllMsgFromLocalAndSvr=function(e){return new Promise(function(t,r){var o=e||D(n.uid),u={reqFuncName:exports.RequestFunc.DELETEALLMSGFROMLOCALANDSVR,operationID:o,userID:n.uid,data:""};n.wsSend(u,t,r)})},n.markGroupMessageHasRead=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.MARKGROUPMESSAGEHASREAD,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.markGroupMessageAsRead=function(e,t){return new Promise(function(o,u){var i=t||D(n.uid),s=r({},e);s.msgIDList=JSON.stringify(s.msgIDList);var a={reqFuncName:exports.RequestFunc.MARKGROUPMESSAGEASREAD,operationID:i,userID:n.uid,data:s};n.wsSend(a,o,u)})},n.insertSingleMessageToLocalStorage=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.INSERTSINGLEMESSAGETOLOCALSTORAGE,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.insertGroupMessageToLocalStorage=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.INSERTGROUPMESSAGETOLOCALSTORAGE,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.typingStatusUpdate=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.TYPINGSTATUSUPDATE,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.markC2CMessageAsRead=function(e,t){return new Promise(function(o,u){var i=r({},e);i.msgIDList=JSON.stringify(i.msgIDList);var s=t||D(n.uid),a={reqFuncName:exports.RequestFunc.MARKC2CMESSAGEASREAD,operationID:s,userID:n.uid,data:i};n.wsSend(a,o,u)})},n.markNotifyMessageHasRead=function(e,t){n.markMessageAsReadByConID({conversationID:e,msgIDList:[]})},n.markMessageAsReadByConID=function(e,t){return new Promise(function(o,u){var i=r({},e);i.msgIDList=JSON.stringify(i.msgIDList);var s=t||D(n.uid),a={reqFuncName:exports.RequestFunc.MARKMESSAGEASREADBYCONID,operationID:s,userID:n.uid,data:i};n.wsSend(a,o,u)})},n.clearC2CHistoryMessage=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.CLEARC2CHISTORYMESSAGE,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.clearC2CHistoryMessageFromLocalAndSvr=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.CLEARC2CHISTORYMESSAGEFROMLOCALANDSVR,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.clearGroupHistoryMessage=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.CLEARGROUPHISTORYMESSAGE,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.clearGroupHistoryMessageFromLocalAndSvr=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.CLEARGROUPHISTORYMESSAGEFROMLOCALANDSVR,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.getAllConversationList=function(e){return new Promise(function(t,r){var o=e||D(n.uid),u={reqFuncName:exports.RequestFunc.GETALLCONVERSATIONLIST,operationID:o,userID:n.uid,data:""};n.wsSend(u,t,r)})},n.getConversationListSplit=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.GETCONVERSATIONLISTSPLIT,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.getOneConversation=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.GETONECONVERSATION,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.getConversationIDBySessionType=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.GETCONVERSATIONIDBYSESSIONTYPE,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.getMultipleConversation=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.GETMULTIPLECONVERSATION,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.deleteConversation=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.DELETECONVERSATION,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.setConversationDraft=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.SETCONVERSATIONDRAFT,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.pinConversation=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.PINCONVERSATION,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.getTotalUnreadMsgCount=function(e){return new Promise(function(t,r){var o=e||D(n.uid),u={reqFuncName:exports.RequestFunc.GETTOTALUNREADMSGCOUNT,operationID:o,userID:n.uid,data:""};n.wsSend(u,t,r)})},n.getConversationRecvMessageOpt=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.GETCONVERSATIONRECVMESSAGEOPT,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.setConversationRecvMessageOpt=function(e,t){return new Promise(function(o,u){var i=t||D(n.uid),s=r({},e);s.conversationIDList=JSON.stringify(e.conversationIDList);var a={reqFuncName:exports.RequestFunc.SETCONVERSATIONRECVMESSAGEOPT,operationID:i,userID:n.uid,data:s};n.wsSend(a,o,u)})},n.searchLocalMessages=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.SEARCHLOCALMESSAGES,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.addFriend=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.ADDFRIEND,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.searchFriends=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.SEARCHFRIENDS,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.getDesignatedFriendsInfo=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.GETDESIGNATEDFRIENDSINFO,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.getRecvFriendApplicationList=function(e){return new Promise(function(t,r){var o=e||D(n.uid),u={reqFuncName:exports.RequestFunc.GETRECVFRIENDAPPLICATIONLIST,operationID:o,userID:n.uid,data:""};n.wsSend(u,t,r)})},n.getSendFriendApplicationList=function(e){return new Promise(function(t,r){var o=e||D(n.uid),u={reqFuncName:exports.RequestFunc.GETSENDFRIENDAPPLICATIONLIST,operationID:o,userID:n.uid,data:""};n.wsSend(u,t,r)})},n.getFriendList=function(e){return new Promise(function(t,r){var o=e||D(n.uid),u={reqFuncName:exports.RequestFunc.GETFRIENDLIST,operationID:o,userID:n.uid,data:""};n.wsSend(u,t,r)})},n.setFriendRemark=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.SETFRIENDREMARK,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.checkFriend=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.CHECKFRIEND,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.acceptFriendApplication=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.ACCEPTFRIENDAPPLICATION,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.refuseFriendApplication=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.REFUSEFRIENDAPPLICATION,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.deleteFriend=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.DELETEFRIEND,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.addBlack=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.ADDBLACK,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.removeBlack=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.REMOVEBLACK,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.getBlackList=function(e){return new Promise(function(t,r){var o=e||D(n.uid),u={reqFuncName:exports.RequestFunc.GETBLACKLIST,operationID:o,userID:n.uid,data:""};n.wsSend(u,t,r)})},n.inviteUserToGroup=function(e,t){return new Promise(function(o,u){var i=t||D(n.uid),s=r({},e);s.userIDList=JSON.stringify(s.userIDList);var a={reqFuncName:exports.RequestFunc.INVITEUSERTOGROUP,operationID:i,userID:n.uid,data:s};n.wsSend(a,o,u)})},n.kickGroupMember=function(e,t){return new Promise(function(o,u){var i=t||D(n.uid),s=r({},e);s.userIDList=JSON.stringify(s.userIDList);var a={reqFuncName:exports.RequestFunc.KICKGROUPMEMBER,operationID:i,userID:n.uid,data:s};n.wsSend(a,o,u)})},n.getGroupMembersInfo=function(e,t){return new Promise(function(o,u){var i=t||D(n.uid),s=r({},e);s.userIDList=JSON.stringify(s.userIDList);var a={reqFuncName:exports.RequestFunc.GETGROUPMEMBERSINFO,operationID:i,userID:n.uid,data:s};n.wsSend(a,o,u)})},n.getGroupMemberList=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.GETGROUPMEMBERLIST,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.getGroupMemberListByJoinTimeFilter=function(e,t){return new Promise(function(o,u){var i=t||D(n.uid),s=r({},e);s.filterUserIDList=JSON.stringify(s.filterUserIDList);var a={reqFuncName:exports.RequestFunc.GETGROUPMEMBERLISTBYJOINTIMEFILTER,operationID:i,userID:n.uid,data:s};n.wsSend(a,o,u)})},n.searchGroupMembers=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.SEARCHGROUPMEMBERS,operationID:u,userID:n.uid,data:{searchParam:JSON.stringify(e)}};n.wsSend(i,r,o)})},n.setGroupApplyMemberFriend=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.SETGROUPAPPLYMEMBERFRIEND,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.setGroupLookMemberInfo=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.SETGROUPLOOKMEMBERINFO,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.getJoinedGroupList=function(e){return new Promise(function(t,r){var o=e||D(n.uid),u={reqFuncName:exports.RequestFunc.GETJOINEDGROUPLIST,operationID:o,userID:n.uid,data:""};n.wsSend(u,t,r)})},n.createGroup=function(e,t){return new Promise(function(o,u){var i=t||D(n.uid),s=r({},e);s.groupBaseInfo=JSON.stringify(s.groupBaseInfo),s.memberList=JSON.stringify(s.memberList);var a={reqFuncName:exports.RequestFunc.CREATEGROUP,operationID:i,userID:n.uid,data:s};n.wsSend(a,o,u)})},n.setGroupInfo=function(e,t){return new Promise(function(o,u){var i=t||D(n.uid),s=r({},e);s.groupInfo=JSON.stringify(s.groupInfo);var a={reqFuncName:exports.RequestFunc.SETGROUPINFO,operationID:i,userID:n.uid,data:s};n.wsSend(a,o,u)})},n.setGroupMemberNickname=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.SETGROUPMEMBERNICKNAME,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.getGroupsInfo=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.GETGROUPSINFO,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.joinGroup=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.JOINGROUP,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.searchGroups=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.SEARCHGROUPS,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.quitGroup=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.QUITGROUP,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.dismissGroup=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.DISMISSGROUP,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.changeGroupMute=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.CHANGEGROUPMUTE,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.changeGroupMemberMute=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.CHANGEGROUPMEMBERMUTE,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.transferGroupOwner=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.TRANSFERGROUPOWNER,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.getSendGroupApplicationList=function(e){return new Promise(function(t,r){var o=e||D(n.uid),u={reqFuncName:exports.RequestFunc.GETSENDGROUPAPPLICATIONLIST,operationID:o,userID:n.uid,data:""};n.wsSend(u,t,r)})},n.getRecvGroupApplicationList=function(e){return new Promise(function(t,r){var o=e||D(n.uid),u={reqFuncName:exports.RequestFunc.GETRECVGROUPAPPLICATIONLIST,operationID:o,userID:n.uid,data:""};n.wsSend(u,t,r)})},n.acceptGroupApplication=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.ACCEPTGROUPAPPLICATION,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.refuseGroupApplication=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.REFUSEGROUPAPPLICATION,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.signalingInvite=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={};i.invitation=e;var s={reqFuncName:exports.RequestFunc.SIGNAL_INGINVITE,operationID:u,userID:n.uid,data:i};n.wsSend(s,r,o)})},n.signalingInviteInGroup=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={};i.invitation=e;var s={reqFuncName:exports.RequestFunc.SIGNALINGINVITEINGROUP,operationID:u,userID:n.uid,data:i};n.wsSend(s,r,o)})},n.signalingAccept=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.SIGNALINGACCEPT,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.signalingReject=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.SIGNALINGREJECT,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.signalingCancel=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.SIGNALINGCANCEL,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.signalingHungUp=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.SIGNALINGHUNGUP,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.getSubDepartment=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.GETSUBDEPARTMENT,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.getDepartmentMember=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.GETDEPARTMENTMEMBER,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.getUserInDepartment=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.GETUSERINDEPARTMENT,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.getDepartmentMemberAndSubDepartment=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.GETDEPARTMENTMEMBERANDSUBDEPARTMENT,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.getDepartmentInfo=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.GETDEPARTMENTINFO,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.searchOrganization=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i=e;i.input=JSON.stringify(i.input);var s={reqFuncName:exports.RequestFunc.SEARCHORGANIZATION,operationID:u,userID:n.uid,data:i};n.wsSend(s,r,o)})},n.resetConversationGroupAtType=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.RESETCONVERSATIONGROUPATTYPE,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.setGroupMemberRoleLevel=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.SETGROUPMEMBERROLELEVEL,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.setGroupVerification=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.SETGROUPVERIFICATION,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.setGlobalRecvMessageOpt=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.SETGLOBALRECVMESSAGEOPT,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.newRevokeMessage=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.NEWREVOKEMESSAGE,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.findMessageList=function(e,t){return new Promise(function(r,o){var u=t||D(n.uid),i={reqFuncName:exports.RequestFunc.FINDMESSAGELIST,operationID:u,userID:n.uid,data:e};n.wsSend(i,r,o)})},n.wsSend=function(e,t,r){var o,u,i;if(null==(o=window)||!o.navigator||window.navigator.onLine)if((null==(u=n.ws)?void 0:u.readyState)===(null==(i=n.ws)?void 0:i.OPEN)){"object"==typeof e.data&&(e.data=JSON.stringify(e.data));var s={oid:e.operationID||D(n.uid),mname:e.reqFuncName,mrsve:t,mrjet:r,flag:!1};n.ws2promise[s.oid]=s;var a=function(e){n.lastTime=(new Date).getTime();var t=JSON.parse(e.data);if(exports.CbEvents[t.event.toUpperCase()])n.emit(t.event,t);else{t.event===exports.RequestFunc.LOGOUT&&n.ws2promise[t.operationID]&&(n.logoutFlag=!0,n.ws.close(),n.ws=void 0);var r=n.ws2promise[t.operationID];r?(0===t.errCode?r.mrsve(t):r.mrjet(t),delete n.ws2promise[t.operationID]):t.event!==exports.RequestFunc.SENDMESSAGE&&t.event!==exports.RequestFunc.SENDMESSAGENOTOSS||n.emit(exports.CbEvents.ONRECVNEWMESSAGEFROMOTHERWEB,t)}};try{"web"==n.platform?(n.ws.send(JSON.stringify(e)),n.ws.onmessage=a):(n.ws.send({data:JSON.stringify(e),success:function(e){"uni"===n.platform&&void 0!==n.ws._callbacks&&void 0!==n.ws._callbacks.message&&(n.ws._callbacks.message=[])}}),n.onceFlag&&(n.ws.onMessage(a),n.onceFlag=!1))}catch(n){return void r({event:e.reqFuncName,errCode:112,errMsg:"no ws conect...",data:"",operationID:e.operationID||""})}e.reqFuncName===exports.RequestFunc.LOGOUT&&(n.onceFlag=!0)}else r({event:e.reqFuncName,errCode:112,errMsg:"ws conecting...",data:"",operationID:e.operationID||""});else r({event:e.reqFuncName,errCode:113,errMsg:"net work error",data:"",operationID:e.operationID||""})},n.getPlatform(),n}t=e,(n=u).prototype=Object.create(t.prototype),n.prototype.constructor=n,o(n,t);var i=u.prototype;return i.login=function(e){var n=this;return new Promise(function(t,r){var o=e.userID,u=e.token,i=e.platformID,s=e.isBatch,a=void 0!==s&&s,E=e.operationID;n.wsUrl=e.url+"?sendID="+o+"&token="+u+"&platformID="+i,n.platformID=i;var c={userID:o,token:u},S={event:exports.RequestFunc.LOGIN,errCode:0,errMsg:"",data:"",operationID:E||""};n.createWs(function(){n.uid=o,n.token=u,n.isBatch=a,n.iLogin(c,E).then(function(e){n.logoutFlag=!1,n.heartbeat(),t(e)}).catch(function(e){S.errCode=e.errCode,S.errMsg=e.errMsg,r(S)})},function(){S.errCode=111,S.errMsg="ws connect close...",n.logoutFlag||Object.values(n.ws2promise).forEach(function(e){return e.mrjet({event:e.mname,errCode:111,errMsg:"ws connect close...",data:"",operationID:e.oid})}),r(S)},function(e){console.log(e),S.errCode=112,S.errMsg="ws connect error...",r(S)}),n.ws||(S.errCode=112,S.errMsg="The current platform is not supported...",r(S))})},i.iLogin=function(e,n){var t=this;return new Promise(function(r,o){var u=n||D(t.uid),i={reqFuncName:exports.RequestFunc.LOGIN,operationID:u,userID:t.uid,data:e,batchMsg:t.isBatch?1:0};t.wsSend(i,r,o)})},i.logout=function(e){var n=this;return new Promise(function(t,r){var o=e||D(n.uid),u={reqFuncName:exports.RequestFunc.LOGOUT,operationID:o,userID:n.uid,data:""};n.wsSend(u,t,r)})},i.getPlatform=function(){var e=typeof WebSocket,n=typeof uni,t=typeof wx;"undefined"===e?("object"===t&&(this.platform="wx"),"object"===n&&(this.platform="uni"),this.platform="unknow"):this.platform="web"},i.createWs=function(e,n,t){var r=this;return console.log("start createWs..."),new Promise(function(o,u){var i;null==(i=r.ws)||i.close(),r.ws=void 0;var s=function(){r.iLogin({userID:r.uid,token:r.token}).then(function(e){r.logoutFlag=!1,console.log("iLogin suc..."),r.heartbeat(),o()})};e&&(s=e);var a=function(){console.log("ws close agin:::"),r.logoutFlag||Object.values(r.ws2promise).forEach(function(e){return e.mrjet({event:e.mname,errCode:111,errMsg:"ws connect close...",data:"",operationID:e.oid})})};n&&(a=n);var E=function(){};if(t&&(E=t),"web"===r.platform)return r.ws=new WebSocket(r.wsUrl),r.ws.onclose=a,r.ws.onopen=s,void(r.ws.onerror=E);var c="uni"===r.platform?uni:wx;r.ws=c.connectSocket({url:r.wsUrl,complete:function(){}}),r.ws.onClose(a),r.ws.onOpen(s),r.ws.onError(E)})},i.reconnect=function(){var e=this;this.onceFlag||(this.onceFlag=!0),this.lock||(this.lock=!0,this.clearTimer(),this.timer=setTimeout(function(){e.createWs(),e.lock=!1},500))},i.clearTimer=function(){this.timer&&clearTimeout(this.timer)},i.heartbeat=function(){var e,n,t,r,o=this;console.log("start heartbeat..."),this.clearTimer(),this.worker&&O(this.worker);try{this.worker=(e=function(){var e,n,t,r;o.logoutFlag?o.worker&&O(o.worker):(null==(e=o.ws)?void 0:e.readyState)===(null==(n=o.ws)?void 0:n.CONNECTING)||(null==(t=o.ws)?void 0:t.readyState)===(null==(r=o.ws)?void 0:r.OPEN)?(new Date).getTime()-o.lastTime<9e3||o.getLoginStatus().catch(function(e){return o.reconnect()}):o.reconnect()},n=new Blob(["(function (e) {\n      setInterval(function () {\n        this.postMessage(null)\n      }, 10000)\n    })()"]),t=window.URL.createObjectURL(n),(r=new Worker(t)).onmessage=e,r)}catch(e){}},u}(t);exports.OptType=void 0,(u=exports.OptType||(exports.OptType={}))[u.Nomal=0]="Nomal",u[u.Mute=1]="Mute",u[u.WithoutNotify=2]="WithoutNotify",exports.AllowType=void 0,(i=exports.AllowType||(exports.AllowType={}))[i.Allowed=0]="Allowed",i[i.NotAllowed=1]="NotAllowed",exports.GroupType=void 0,(s=exports.GroupType||(exports.GroupType={}))[s.NomalGroup=0]="NomalGroup",s[s.SuperGroup=1]="SuperGroup",s[s.WorkingGroup=2]="WorkingGroup",exports.GroupVerificationType=void 0,(a=exports.GroupVerificationType||(exports.GroupVerificationType={}))[a.ApplyNeedInviteNot=0]="ApplyNeedInviteNot",a[a.AllNeed=1]="AllNeed",a[a.AllNot=2]="AllNot",exports.GroupStatus=void 0,(E=exports.GroupStatus||(exports.GroupStatus={}))[E.Nomal=0]="Nomal",E[E.Baned=1]="Baned",E[E.Dismissed=2]="Dismissed",E[E.Muted=3]="Muted",exports.GroupJoinSource=void 0,(c=exports.GroupJoinSource||(exports.GroupJoinSource={}))[c.Invitation=2]="Invitation",c[c.Search=3]="Search",c[c.QrCode=4]="QrCode",exports.GroupRole=void 0,(S=exports.GroupRole||(exports.GroupRole={}))[S.Nomal=1]="Nomal",S[S.Owner=2]="Owner",S[S.Admin=3]="Admin",exports.GroupAtType=void 0,(I=exports.GroupAtType||(exports.GroupAtType={}))[I.AtNormal=0]="AtNormal",I[I.AtMe=1]="AtMe",I[I.AtAll=2]="AtAll",I[I.AtAllAtMe=3]="AtAllAtMe",I[I.AtGroupNotice=4]="AtGroupNotice",exports.MessageStatus=void 0,(d=exports.MessageStatus||(exports.MessageStatus={}))[d.Sending=1]="Sending",d[d.Succeed=2]="Succeed",d[d.Failed=3]="Failed",exports.Platform=void 0,(R=exports.Platform||(exports.Platform={}))[R.iOS=1]="iOS",R[R.Android=2]="Android",R[R.Windows=3]="Windows",R[R.MacOSX=4]="MacOSX",R[R.Web=5]="Web",R[R.Linux=7]="Linux",R[R.Admin=8]="Admin",exports.MessageType=void 0,(A=exports.MessageType||(exports.MessageType={}))[A.TEXTMESSAGE=101]="TEXTMESSAGE",A[A.PICTUREMESSAGE=102]="PICTUREMESSAGE",A[A.VOICEMESSAGE=103]="VOICEMESSAGE",A[A.VIDEOMESSAGE=104]="VIDEOMESSAGE",A[A.FILEMESSAGE=105]="FILEMESSAGE",A[A.ATTEXTMESSAGE=106]="ATTEXTMESSAGE",A[A.MERGERMESSAGE=107]="MERGERMESSAGE",A[A.CARDMESSAGE=108]="CARDMESSAGE",A[A.LOCATIONMESSAGE=109]="LOCATIONMESSAGE",A[A.CUSTOMMESSAGE=110]="CUSTOMMESSAGE",A[A.REVOKEMESSAGE=111]="REVOKEMESSAGE",A[A.HASREADRECEIPTMESSAGE=112]="HASREADRECEIPTMESSAGE",A[A.TYPINGMESSAGE=113]="TYPINGMESSAGE",A[A.QUOTEMESSAGE=114]="QUOTEMESSAGE",A[A.FACEMESSAGE=115]="FACEMESSAGE",A[A.ADVANCETEXTMESSAGE=117]="ADVANCETEXTMESSAGE",A[A.ADVANCEREVOKEMESSAGE=118]="ADVANCEREVOKEMESSAGE",A[A.CUSTOMMSGNOTTRIGGERCONVERSATION=119]="CUSTOMMSGNOTTRIGGERCONVERSATION",A[A.CUSTOMMSGONLINEONLY=120]="CUSTOMMSGONLINEONLY",A[A.FRIENDAPPLICATIONAPPROVED=1201]="FRIENDAPPLICATIONAPPROVED",A[A.FRIENDAPPLICATIONREJECTED=1202]="FRIENDAPPLICATIONREJECTED",A[A.FRIENDAPPLICATIONADDED=1203]="FRIENDAPPLICATIONADDED",A[A.FRIENDADDED=1204]="FRIENDADDED",A[A.FRIENDDELETED=1205]="FRIENDDELETED",A[A.FRIENDREMARKSET=1206]="FRIENDREMARKSET",A[A.BLACKADDED=1207]="BLACKADDED",A[A.BLACKDELETED=1208]="BLACKDELETED",A[A.SELFINFOUPDATED=1303]="SELFINFOUPDATED",A[A.NOTIFICATION=1400]="NOTIFICATION",A[A.GROUPCREATED=1501]="GROUPCREATED",A[A.GROUPINFOUPDATED=1502]="GROUPINFOUPDATED",A[A.JOINGROUPAPPLICATIONADDED=1503]="JOINGROUPAPPLICATIONADDED",A[A.MEMBERQUIT=1504]="MEMBERQUIT",A[A.GROUPAPPLICATIONACCEPTED=1505]="GROUPAPPLICATIONACCEPTED",A[A.GROUPAPPLICATIONREJECTED=1506]="GROUPAPPLICATIONREJECTED",A[A.GROUPOWNERTRANSFERRED=1507]="GROUPOWNERTRANSFERRED",A[A.MEMBERKICKED=1508]="MEMBERKICKED",A[A.MEMBERINVITED=1509]="MEMBERINVITED",A[A.MEMBERENTER=1510]="MEMBERENTER",A[A.GROUPDISMISSED=1511]="GROUPDISMISSED",A[A.GROUPMEMBERMUTED=1512]="GROUPMEMBERMUTED",A[A.GROUPMEMBERCANCELMUTED=1513]="GROUPMEMBERCANCELMUTED",A[A.GROUPMUTED=1514]="GROUPMUTED",A[A.GROUPCANCELMUTED=1515]="GROUPCANCELMUTED",A[A.GROUPMEMBERINFOUPDATED=1516]="GROUPMEMBERINFOUPDATED",A[A.BURNMESSAGECHANGE=1701]="BURNMESSAGECHANGE",exports.SessionType=void 0,(N=exports.SessionType||(exports.SessionType={}))[N.Single=1]="Single",N[N.Group=2]="Group",N[N.SuperGroup=3]="SuperGroup",N[N.Notification=4]="Notification",exports.OpenIMSDK=p,exports.emitter=t,exports.uuid=D;
//# sourceMappingURL=index.js.map
