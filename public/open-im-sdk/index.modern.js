class e{constructor(){this.events=void 0,this.events={}}emit(e,t){return this.events[e]&&this.events[e].forEach(e=>e(t)),this}on(e,t){return this.events[e]?this.events[e].push(t):this.events[e]=[t],this}off(e,t){if(e&&"function"==typeof t){const s=this.events[e],i=s.findIndex(e=>e===t);s.splice(i,1)}else this.events[e]=[];return this}}function t(){return t=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var i in s)Object.prototype.hasOwnProperty.call(s,i)&&(e[i]=s[i])}return e},t.apply(this,arguments)}var s,i;!function(e){e.INITSDK="InitSDK",e.LOGIN="Login",e.LOGOUT="Logout",e.GETLOGINSTATUS="GetLoginStatus",e.GETLOGINUSER="GetLoginUser",e.GETSELFUSERINFO="GetSelfUserInfo",e.CREATETEXTMESSAGE="CreateTextMessage",e.CREATETEXTATMESSAGE="CreateTextAtMessage",e.CREATEADVANCEDTEXTMESSAGE="CreateAdvancedTextMessage",e.CREATEIMAGEMESSAGEFROMBYURL="CreateImageMessageByURL",e.CREATESOUNDMESSAGEBYURL="CreateSoundMessageByURL",e.CREATEVIDEOMESSAGEBYURL="CreateVideoMessageByURL",e.CREATEFILEMESSAGEBYURL="CreateFileMessageByURL",e.CREATEIMAGEMESSAGEFROMFULLPATH="CreateImageMessageFromFullPath",e.CREATESOUNDMESSAGEFROMFULLPATH="CreateSoundMessageFromFullPath",e.CREATEVIDEOMESSAGEFROMFULLPATH="CreateVideoMessageFromFullPath",e.CREATEFILEMESSAGEFROMFULLPATH="CreateFileMessageFromFullPath",e.CREATELOCATIONMESSAGE="CreateLocationMessage",e.CREATECUSTOMMESSAGE="CreateCustomMessage",e.CREATEMERGERMESSAGE="CreateMergerMessage",e.CREATEFORWARDMESSAGE="CreateForwardMessage",e.CREATEQUOTEMESSAGE="CreateQuoteMessage",e.CREATEADVANCEDQUOTEMESSAGE="CreateAdvancedQuoteMessage",e.CREATECARDMESSAGE="CreateCardMessage",e.CREATEFACEMESSAGE="CreateFaceMessage",e.SENDMESSAGE="SendMessage",e.SENDMESSAGENOTOSS="SendMessageNotOss",e.GETHISTORYMESSAGELIST="GetHistoryMessageList",e.GETADVANCEDHISTORYMESSAGELIST="GetAdvancedHistoryMessageList",e.GETHISTORYMESSAGELISTREVERSE="GetHistoryMessageListReverse",e.REVOKEMESSAGE="RevokeMessage",e.SETONECONVERSATIONPRIVATECHAT="SetOneConversationPrivateChat",e.DELETEMESSAGEFROMLOCALSTORAGE="DeleteMessageFromLocalStorage",e.DELETEMESSAGEFROMLOCALANDSVR="DeleteMessageFromLocalAndSvr",e.DELETECONVERSATIONFROMLOCALANDSVR="DeleteConversationFromLocalAndSvr",e.DELETEALLCONVERSATIONFROMLOCAL="DeleteAllConversationFromLocal",e.DELETEALLMSGFROMLOCALANDSVR="DeleteAllMsgFromLocalAndSvr",e.DELETEALLMSGFROMLOCAL="DeleteAllMsgFromLocal",e.MARKSINGLEMESSAGEHASREAD="MarkSingleMessageHasRead",e.INSERTSINGLEMESSAGETOLOCALSTORAGE="InsertSingleMessageToLocalStorage",e.INSERTGROUPMESSAGETOLOCALSTORAGE="InsertGroupMessageToLocalStorage",e.TYPINGSTATUSUPDATE="TypingStatusUpdate",e.MARKC2CMESSAGEASREAD="MarkC2CMessageAsRead",e.MARKMESSAGEASREADBYCONID="MarkMessageAsReadByConID",e.CLEARC2CHISTORYMESSAGE="ClearC2CHistoryMessage",e.CLEARC2CHISTORYMESSAGEFROMLOCALANDSVR="ClearC2CHistoryMessageFromLocalAndSvr",e.CLEARGROUPHISTORYMESSAGE="ClearGroupHistoryMessage",e.CLEARGROUPHISTORYMESSAGEFROMLOCALANDSVR="ClearGroupHistoryMessageFromLocalAndSvr",e.ADDFRIEND="AddFriend",e.SEARCHFRIENDS="SearchFriends",e.GETDESIGNATEDFRIENDSINFO="GetDesignatedFriendsInfo",e.GETRECVFRIENDAPPLICATIONLIST="GetRecvFriendApplicationList",e.GETSENDFRIENDAPPLICATIONLIST="GetSendFriendApplicationList",e.GETFRIENDLIST="GetFriendList",e.SETFRIENDREMARK="SetFriendRemark",e.ADDBLACK="AddBlack",e.GETBLACKLIST="GetBlackList",e.REMOVEBLACK="RemoveBlack",e.CHECKFRIEND="CheckFriend",e.ACCEPTFRIENDAPPLICATION="AcceptFriendApplication",e.REFUSEFRIENDAPPLICATION="RefuseFriendApplication",e.DELETEFRIEND="DeleteFriend",e.GETUSERSINFO="GetUsersInfo",e.SETSELFINFO="SetSelfInfo",e.GETALLCONVERSATIONLIST="GetAllConversationList",e.GETCONVERSATIONLISTSPLIT="GetConversationListSplit",e.GETONECONVERSATION="GetOneConversation",e.GETCONVERSATIONIDBYSESSIONTYPE="GetConversationIDBySessionType",e.GETMULTIPLECONVERSATION="GetMultipleConversation",e.DELETECONVERSATION="DeleteConversation",e.SETCONVERSATIONDRAFT="SetConversationDraft",e.PINCONVERSATION="PinConversation",e.GETTOTALUNREADMSGCOUNT="GetTotalUnreadMsgCount",e.GETCONVERSATIONRECVMESSAGEOPT="GetConversationRecvMessageOpt",e.SETCONVERSATIONRECVMESSAGEOPT="SetConversationRecvMessageOpt",e.SEARCHLOCALMESSAGES="SearchLocalMessages",e.MARKGROUPMESSAGEHASREAD="MarkGroupMessageHasRead",e.MARKGROUPMESSAGEASREAD="MarkGroupMessageAsRead",e.INVITEUSERTOGROUP="InviteUserToGroup",e.KICKGROUPMEMBER="KickGroupMember",e.GETGROUPMEMBERSINFO="GetGroupMembersInfo",e.GETGROUPMEMBERLIST="GetGroupMemberList",e.GETGROUPMEMBERLISTBYJOINTIMEFILTER="GetGroupMemberListByJoinTimeFilter",e.SEARCHGROUPMEMBERS="SearchGroupMembers",e.SETGROUPAPPLYMEMBERFRIEND="SetGroupApplyMemberFriend",e.SETGROUPLOOKMEMBERINFO="SetGroupLookMemberInfo",e.GETJOINEDGROUPLIST="GetJoinedGroupList",e.CREATEGROUP="CreateGroup",e.SETGROUPINFO="SetGroupInfo",e.SETGROUPMEMBERNICKNAME="SetGroupMemberNickname",e.GETGROUPSINFO="GetGroupsInfo",e.JOINGROUP="JoinGroup",e.SEARCHGROUPS="SearchGroups",e.QUITGROUP="QuitGroup",e.DISMISSGROUP="DismissGroup",e.CHANGEGROUPMUTE="ChangeGroupMute",e.CHANGEGROUPMEMBERMUTE="ChangeGroupMemberMute",e.TRANSFERGROUPOWNER="TransferGroupOwner",e.GETSENDGROUPAPPLICATIONLIST="GetSendGroupApplicationList",e.GETRECVGROUPAPPLICATIONLIST="GetRecvGroupApplicationList",e.ACCEPTGROUPAPPLICATION="AcceptGroupApplication",e.REFUSEGROUPAPPLICATION="RefuseGroupApplication",e.SIGNAL_INGINVITE="SignalingInvite",e.SIGNALINGINVITEINGROUP="SignalingInviteInGroup",e.SIGNALINGACCEPT="SignalingAccept",e.SIGNALINGREJECT="SignalingReject",e.SIGNALINGCANCEL="SignalingCancel",e.SIGNALINGHUNGUP="SignalingHungUp",e.GETSUBDEPARTMENT="GetSubDepartment",e.GETDEPARTMENTMEMBER="GetDepartmentMember",e.GETUSERINDEPARTMENT="GetUserInDepartment",e.GETDEPARTMENTMEMBERANDSUBDEPARTMENT="GetDepartmentMemberAndSubDepartment",e.GETDEPARTMENTINFO="GetDepartmentInfo",e.SEARCHORGANIZATION="SearchOrganization",e.RESETCONVERSATIONGROUPATTYPE="ResetConversationGroupAtType",e.SETGROUPMEMBERROLELEVEL="SetGroupMemberRoleLevel",e.SETGROUPVERIFICATION="SetGroupVerification",e.SETGLOBALRECVMESSAGEOPT="SetGlobalRecvMessageOpt",e.NEWREVOKEMESSAGE="NewRevokeMessage",e.FINDMESSAGELIST="FindMessageList"}(s||(s={})),function(e){e.ONCONNECTFAILED="OnConnectFailed",e.ONCONNECTSUCCESS="OnConnectSuccess",e.ONCONNECTING="OnConnecting",e.ONKICKEDOFFLINE="OnKickedOffline",e.ONSELFINFOUPDATED="OnSelfInfoUpdated",e.ONUSERTOKENEXPIRED="OnUserTokenExpired",e.ONPROGRESS="OnProgress",e.ONRECVNEWMESSAGE="OnRecvNewMessage",e.ONRECVNEWMESSAGES="OnRecvNewMessages",e.ONRECVMESSAGEREVOKED="OnRecvMessageRevoked",e.ONRECVC2CREADRECEIPT="OnRecvC2CReadReceipt",e.ONRECVGROUPREADRECEIPT="OnRecvGroupReadReceipt",e.ONCONVERSATIONCHANGED="OnConversationChanged",e.ONNEWCONVERSATION="OnNewConversation",e.ONSYNCSERVERFAILED="OnSyncServerFailed",e.ONSYNCSERVERFINISH="OnSyncServerFinish",e.ONSYNCSERVERSTART="OnSyncServerStart",e.ONTOTALUNREADMESSAGECOUNTCHANGED="OnTotalUnreadMessageCountChanged",e.ONBLACKADDED="OnBlackAdded",e.ONBLACKDELETED="OnBlackDeleted",e.ONFRIENDAPPLICATIONACCEPTED="OnFriendApplicationAccepted",e.ONFRIENDAPPLICATIONADDED="OnFriendApplicationAdded",e.ONFRIENDAPPLICATIONDELETED="OnFriendApplicationDeleted",e.ONFRIENDAPPLICATIONREJECTED="OnFriendApplicationRejected",e.ONFRIENDINFOCHANGED="OnFriendInfoChanged",e.ONFRIENDADDED="OnFriendAdded",e.ONFRIENDDELETED="OnFriendDeleted",e.ONJOINEDGROUPADDED="OnJoinedGroupAdded",e.ONJOINEDGROUPDELETED="OnJoinedGroupDeleted",e.ONGROUPMEMBERADDED="OnGroupMemberAdded",e.ONGROUPMEMBERDELETED="OnGroupMemberDeleted",e.ONGROUPAPPLICATIONADDED="OnGroupApplicationAdded",e.ONGROUPAPPLICATIONDELETED="OnGroupApplicationDeleted",e.ONGROUPINFOCHANGED="OnGroupInfoChanged",e.ONGROUPMEMBERINFOCHANGED="OnGroupMemberInfoChanged",e.ONGROUPAPPLICATIONACCEPTED="OnGroupApplicationAccepted",e.ONGROUPAPPLICATIONREJECTED="OnGroupApplicationRejected",e.ONRECEIVENEWINVITATION="OnReceiveNewInvitation",e.ONINVITEEACCEPTED="OnInviteeAccepted",e.ONINVITEEREJECTED="OnInviteeRejected",e.ONINVITATIONCANCELLED="OnInvitationCancelled",e.ONHANGUP="OnHangUp",e.ONINVITATIONTIMEOUT="OnInvitationTimeout",e.ONINVITEEACCEPTEDBYOTHERDEVICE="OnInviteeAcceptedByOtherDevice",e.ONINVITEEREJECTEDBYOTHERDEVICE="OnInviteeRejectedByOtherDevice",e.ONORGANIZATIONUPDATED="OnOrganizationUpdated",e.ONRECVNEWMESSAGEFROMOTHERWEB="OnRecvNewMessageFromOtherWeb",e.ONNEWRECVMESSAGEREVOKED="OnNewRecvMessageRevoked"}(i||(i={}));const n=e=>{try{e&&e.terminate()}catch(e){console.log(e)}},r=e=>(36*Math.random()).toString(36).slice(2)+(new Date).getTime().toString()+e;class o extends e{constructor(){super(),this.ws=void 0,this.uid=void 0,this.token=void 0,this.platform="web",this.wsUrl="",this.lock=!1,this.logoutFlag=!1,this.ws2promise={},this.onceFlag=!0,this.timer=void 0,this.lastTime=0,this.heartbeatCount=0,this.heartbeatStartTime=0,this.platformID=0,this.isBatch=!1,this.worker=null,this.getLoginStatus=e=>new Promise((t,i)=>{const n=e||r(this.uid);this.wsSend({reqFuncName:s.GETLOGINSTATUS,operationID:n,userID:this.uid,data:""},t,i)}),this.getLoginUser=e=>new Promise((t,i)=>{const n=e||r(this.uid);this.wsSend({reqFuncName:s.GETLOGINUSER,operationID:n,userID:this.uid,data:""},t,i)}),this.getSelfUserInfo=e=>new Promise((t,i)=>{const n=e||r(this.uid);this.wsSend({reqFuncName:s.GETSELFUSERINFO,operationID:n,userID:this.uid,data:""},t,i)}),this.getUsersInfo=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.GETUSERSINFO,operationID:o,userID:this.uid,data:e},i,n)}),this.setSelfInfo=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.SETSELFINFO,operationID:o,userID:this.uid,data:e},i,n)}),this.createTextMessage=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.CREATETEXTMESSAGE,operationID:o,userID:this.uid,data:e},i,n)}),this.createTextAtMessage=(e,i)=>new Promise((n,o)=>{const E=t({},e);E.atUserIDList=JSON.stringify(E.atUserIDList),E.atUsersInfo=JSON.stringify(E.atUsersInfo);const a=i||r(this.uid);this.wsSend({reqFuncName:s.CREATETEXTATMESSAGE,operationID:a,userID:this.uid,data:E},n,o)}),this.createAdvancedTextMessage=(e,i)=>new Promise((n,o)=>{const E=t({},e);E.messageEntityList=JSON.stringify(E.messageEntityList);const a=i||r(this.uid);this.wsSend({reqFuncName:s.CREATEADVANCEDTEXTMESSAGE,operationID:a,userID:this.uid,data:E},n,o)}),this.createImageMessage=(e,i)=>new Promise((n,o)=>{const E=i||r(this.uid);let a=t({},e);a.bigPicture=JSON.stringify(a.bigPicture),a.snapshotPicture=JSON.stringify(a.snapshotPicture),a.sourcePicture=JSON.stringify(a.sourcePicture);const S={reqFuncName:s.CREATEIMAGEMESSAGEFROMBYURL,operationID:E,userID:this.uid,data:JSON.stringify(a)};this.wsSend(S,n,o)}),this.createSoundMessage=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);let E={soundBaseInfo:JSON.stringify(e)};const a={reqFuncName:s.CREATESOUNDMESSAGEBYURL,operationID:o,userID:this.uid,data:JSON.stringify(E)};this.wsSend(a,i,n)}),this.createVideoMessage=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);let E={videoBaseInfo:JSON.stringify(e)};const a={reqFuncName:s.CREATEVIDEOMESSAGEBYURL,operationID:o,userID:this.uid,data:JSON.stringify(E)};this.wsSend(a,i,n)}),this.createFileMessage=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);let E={fileBaseInfo:JSON.stringify(e)};const a={reqFuncName:s.CREATEFILEMESSAGEBYURL,operationID:o,userID:this.uid,data:JSON.stringify(E)};this.wsSend(a,i,n)}),this.createFileMessageFromFullPath=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.CREATEFILEMESSAGEFROMFULLPATH,operationID:o,userID:this.uid,data:e},i,n)}),this.createImageMessageFromFullPath=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.CREATEIMAGEMESSAGEFROMFULLPATH,operationID:o,userID:this.uid,data:e},i,n)}),this.createSoundMessageFromFullPath=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.CREATESOUNDMESSAGEFROMFULLPATH,operationID:o,userID:this.uid,data:e},i,n)}),this.createVideoMessageFromFullPath=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.CREATEVIDEOMESSAGEFROMFULLPATH,operationID:o,userID:this.uid,data:e},i,n)}),this.createMergerMessage=(e,i)=>new Promise((n,o)=>{const E=i||r(this.uid);let a=t({},e);a.messageList=JSON.stringify(e.messageList),a.summaryList=JSON.stringify(e.summaryList),this.wsSend({reqFuncName:s.CREATEMERGERMESSAGE,operationID:E,userID:this.uid,data:a},n,o)}),this.createForwardMessage=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.CREATEFORWARDMESSAGE,operationID:o,userID:this.uid,data:e},i,n)}),this.createFaceMessage=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.CREATEFACEMESSAGE,operationID:o,userID:this.uid,data:e},i,n)}),this.createLocationMessage=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.CREATELOCATIONMESSAGE,operationID:o,userID:this.uid,data:e},i,n)}),this.createCustomMessage=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.CREATECUSTOMMESSAGE,operationID:o,userID:this.uid,data:e},i,n)}),this.createQuoteMessage=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.CREATEQUOTEMESSAGE,operationID:o,userID:this.uid,data:e},i,n)}),this.createAdvancedQuoteMessage=(e,i)=>new Promise((n,o)=>{const E=i||r(this.uid),a=t({},e);a.messageEntityList=JSON.stringify(a.messageEntityList),this.wsSend({reqFuncName:s.CREATEADVANCEDQUOTEMESSAGE,operationID:E,userID:this.uid,data:a},n,o)}),this.createCardMessage=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.CREATECARDMESSAGE,operationID:o,userID:this.uid,data:e},i,n)}),this.sendMessage=(e,i)=>new Promise((n,o)=>{const E=i||r(this.uid),a=t({},e);a.offlinePushInfo=a.offlinePushInfo?JSON.stringify(e.offlinePushInfo):"",this.wsSend({reqFuncName:s.SENDMESSAGE,operationID:E,userID:this.uid,data:a},n,o)}),this.sendMessageNotOss=(e,i)=>new Promise((n,o)=>{const E=i||r(this.uid),a=t({},e);a.offlinePushInfo=a.offlinePushInfo?JSON.stringify(e.offlinePushInfo):"",this.wsSend({reqFuncName:s.SENDMESSAGENOTOSS,operationID:E,userID:this.uid,data:a},n,o)}),this.getHistoryMessageList=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.GETHISTORYMESSAGELIST,operationID:o,userID:this.uid,data:e},i,n)}),this.getAdvancedHistoryMessageList=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.GETADVANCEDHISTORYMESSAGELIST,operationID:o,userID:this.uid,data:e},i,n)}),this.getHistoryMessageListReverse=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.GETHISTORYMESSAGELISTREVERSE,operationID:o,userID:this.uid,data:e},i,n)}),this.revokeMessage=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.REVOKEMESSAGE,operationID:o,userID:this.uid,data:e},i,n)}),this.setOneConversationPrivateChat=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.SETONECONVERSATIONPRIVATECHAT,operationID:o,userID:this.uid,data:e},i,n)}),this.deleteMessageFromLocalStorage=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.DELETEMESSAGEFROMLOCALSTORAGE,operationID:o,userID:this.uid,data:e},i,n)}),this.deleteMessageFromLocalAndSvr=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.DELETEMESSAGEFROMLOCALANDSVR,operationID:o,userID:this.uid,data:e},i,n)}),this.deleteConversationFromLocalAndSvr=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.DELETECONVERSATIONFROMLOCALANDSVR,operationID:o,userID:this.uid,data:e},i,n)}),this.deleteAllConversationFromLocal=e=>new Promise((t,i)=>{const n=e||r(this.uid);this.wsSend({reqFuncName:s.DELETEALLCONVERSATIONFROMLOCAL,operationID:n,userID:this.uid,data:""},t,i)}),this.deleteAllMsgFromLocal=e=>new Promise((t,i)=>{const n=e||r(this.uid);this.wsSend({reqFuncName:s.DELETEALLMSGFROMLOCAL,operationID:n,userID:this.uid,data:""},t,i)}),this.deleteAllMsgFromLocalAndSvr=e=>new Promise((t,i)=>{const n=e||r(this.uid);this.wsSend({reqFuncName:s.DELETEALLMSGFROMLOCALANDSVR,operationID:n,userID:this.uid,data:""},t,i)}),this.markGroupMessageHasRead=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.MARKGROUPMESSAGEHASREAD,operationID:o,userID:this.uid,data:e},i,n)}),this.markGroupMessageAsRead=(e,i)=>new Promise((n,o)=>{const E=i||r(this.uid),a=t({},e);a.msgIDList=JSON.stringify(a.msgIDList),this.wsSend({reqFuncName:s.MARKGROUPMESSAGEASREAD,operationID:E,userID:this.uid,data:a},n,o)}),this.insertSingleMessageToLocalStorage=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.INSERTSINGLEMESSAGETOLOCALSTORAGE,operationID:o,userID:this.uid,data:e},i,n)}),this.insertGroupMessageToLocalStorage=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.INSERTGROUPMESSAGETOLOCALSTORAGE,operationID:o,userID:this.uid,data:e},i,n)}),this.typingStatusUpdate=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.TYPINGSTATUSUPDATE,operationID:o,userID:this.uid,data:e},i,n)}),this.markC2CMessageAsRead=(e,i)=>new Promise((n,o)=>{let E=t({},e);E.msgIDList=JSON.stringify(E.msgIDList);const a=i||r(this.uid);this.wsSend({reqFuncName:s.MARKC2CMESSAGEASREAD,operationID:a,userID:this.uid,data:E},n,o)}),this.markNotifyMessageHasRead=(e,t)=>{this.markMessageAsReadByConID({conversationID:e,msgIDList:[]})},this.markMessageAsReadByConID=(e,i)=>new Promise((n,o)=>{let E=t({},e);E.msgIDList=JSON.stringify(E.msgIDList);const a=i||r(this.uid);this.wsSend({reqFuncName:s.MARKMESSAGEASREADBYCONID,operationID:a,userID:this.uid,data:E},n,o)}),this.clearC2CHistoryMessage=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.CLEARC2CHISTORYMESSAGE,operationID:o,userID:this.uid,data:e},i,n)}),this.clearC2CHistoryMessageFromLocalAndSvr=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.CLEARC2CHISTORYMESSAGEFROMLOCALANDSVR,operationID:o,userID:this.uid,data:e},i,n)}),this.clearGroupHistoryMessage=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.CLEARGROUPHISTORYMESSAGE,operationID:o,userID:this.uid,data:e},i,n)}),this.clearGroupHistoryMessageFromLocalAndSvr=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.CLEARGROUPHISTORYMESSAGEFROMLOCALANDSVR,operationID:o,userID:this.uid,data:e},i,n)}),this.getAllConversationList=e=>new Promise((t,i)=>{const n=e||r(this.uid);this.wsSend({reqFuncName:s.GETALLCONVERSATIONLIST,operationID:n,userID:this.uid,data:""},t,i)}),this.getConversationListSplit=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.GETCONVERSATIONLISTSPLIT,operationID:o,userID:this.uid,data:e},i,n)}),this.getOneConversation=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.GETONECONVERSATION,operationID:o,userID:this.uid,data:e},i,n)}),this.getConversationIDBySessionType=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.GETCONVERSATIONIDBYSESSIONTYPE,operationID:o,userID:this.uid,data:e},i,n)}),this.getMultipleConversation=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.GETMULTIPLECONVERSATION,operationID:o,userID:this.uid,data:e},i,n)}),this.deleteConversation=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.DELETECONVERSATION,operationID:o,userID:this.uid,data:e},i,n)}),this.setConversationDraft=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.SETCONVERSATIONDRAFT,operationID:o,userID:this.uid,data:e},i,n)}),this.pinConversation=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.PINCONVERSATION,operationID:o,userID:this.uid,data:e},i,n)}),this.getTotalUnreadMsgCount=e=>new Promise((t,i)=>{const n=e||r(this.uid);this.wsSend({reqFuncName:s.GETTOTALUNREADMSGCOUNT,operationID:n,userID:this.uid,data:""},t,i)}),this.getConversationRecvMessageOpt=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.GETCONVERSATIONRECVMESSAGEOPT,operationID:o,userID:this.uid,data:e},i,n)}),this.setConversationRecvMessageOpt=(e,i)=>new Promise((n,o)=>{const E=i||r(this.uid),a=t({},e);a.conversationIDList=JSON.stringify(e.conversationIDList),this.wsSend({reqFuncName:s.SETCONVERSATIONRECVMESSAGEOPT,operationID:E,userID:this.uid,data:a},n,o)}),this.searchLocalMessages=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.SEARCHLOCALMESSAGES,operationID:o,userID:this.uid,data:e},i,n)}),this.addFriend=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.ADDFRIEND,operationID:o,userID:this.uid,data:e},i,n)}),this.searchFriends=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.SEARCHFRIENDS,operationID:o,userID:this.uid,data:e},i,n)}),this.getDesignatedFriendsInfo=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.GETDESIGNATEDFRIENDSINFO,operationID:o,userID:this.uid,data:e},i,n)}),this.getRecvFriendApplicationList=e=>new Promise((t,i)=>{const n=e||r(this.uid);this.wsSend({reqFuncName:s.GETRECVFRIENDAPPLICATIONLIST,operationID:n,userID:this.uid,data:""},t,i)}),this.getSendFriendApplicationList=e=>new Promise((t,i)=>{const n=e||r(this.uid);this.wsSend({reqFuncName:s.GETSENDFRIENDAPPLICATIONLIST,operationID:n,userID:this.uid,data:""},t,i)}),this.getFriendList=e=>new Promise((t,i)=>{const n=e||r(this.uid);this.wsSend({reqFuncName:s.GETFRIENDLIST,operationID:n,userID:this.uid,data:""},t,i)}),this.setFriendRemark=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.SETFRIENDREMARK,operationID:o,userID:this.uid,data:e},i,n)}),this.checkFriend=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.CHECKFRIEND,operationID:o,userID:this.uid,data:e},i,n)}),this.acceptFriendApplication=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.ACCEPTFRIENDAPPLICATION,operationID:o,userID:this.uid,data:e},i,n)}),this.refuseFriendApplication=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.REFUSEFRIENDAPPLICATION,operationID:o,userID:this.uid,data:e},i,n)}),this.deleteFriend=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.DELETEFRIEND,operationID:o,userID:this.uid,data:e},i,n)}),this.addBlack=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.ADDBLACK,operationID:o,userID:this.uid,data:e},i,n)}),this.removeBlack=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.REMOVEBLACK,operationID:o,userID:this.uid,data:e},i,n)}),this.getBlackList=e=>new Promise((t,i)=>{const n=e||r(this.uid);this.wsSend({reqFuncName:s.GETBLACKLIST,operationID:n,userID:this.uid,data:""},t,i)}),this.inviteUserToGroup=(e,i)=>new Promise((n,o)=>{const E=i||r(this.uid),a=t({},e);a.userIDList=JSON.stringify(a.userIDList),this.wsSend({reqFuncName:s.INVITEUSERTOGROUP,operationID:E,userID:this.uid,data:a},n,o)}),this.kickGroupMember=(e,i)=>new Promise((n,o)=>{const E=i||r(this.uid),a=t({},e);a.userIDList=JSON.stringify(a.userIDList),this.wsSend({reqFuncName:s.KICKGROUPMEMBER,operationID:E,userID:this.uid,data:a},n,o)}),this.getGroupMembersInfo=(e,i)=>new Promise((n,o)=>{const E=i||r(this.uid),a=t({},e);a.userIDList=JSON.stringify(a.userIDList),this.wsSend({reqFuncName:s.GETGROUPMEMBERSINFO,operationID:E,userID:this.uid,data:a},n,o)}),this.getGroupMemberList=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.GETGROUPMEMBERLIST,operationID:o,userID:this.uid,data:e},i,n)}),this.getGroupMemberListByJoinTimeFilter=(e,i)=>new Promise((n,o)=>{const E=i||r(this.uid),a=t({},e);a.filterUserIDList=JSON.stringify(a.filterUserIDList),this.wsSend({reqFuncName:s.GETGROUPMEMBERLISTBYJOINTIMEFILTER,operationID:E,userID:this.uid,data:a},n,o)}),this.searchGroupMembers=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid),E={reqFuncName:s.SEARCHGROUPMEMBERS,operationID:o,userID:this.uid,data:{searchParam:JSON.stringify(e)}};this.wsSend(E,i,n)}),this.setGroupApplyMemberFriend=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.SETGROUPAPPLYMEMBERFRIEND,operationID:o,userID:this.uid,data:e},i,n)}),this.setGroupLookMemberInfo=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.SETGROUPLOOKMEMBERINFO,operationID:o,userID:this.uid,data:e},i,n)}),this.getJoinedGroupList=e=>new Promise((t,i)=>{const n=e||r(this.uid);this.wsSend({reqFuncName:s.GETJOINEDGROUPLIST,operationID:n,userID:this.uid,data:""},t,i)}),this.createGroup=(e,i)=>new Promise((n,o)=>{const E=i||r(this.uid),a=t({},e);a.groupBaseInfo=JSON.stringify(a.groupBaseInfo),a.memberList=JSON.stringify(a.memberList),this.wsSend({reqFuncName:s.CREATEGROUP,operationID:E,userID:this.uid,data:a},n,o)}),this.setGroupInfo=(e,i)=>new Promise((n,o)=>{const E=i||r(this.uid),a=t({},e);a.groupInfo=JSON.stringify(a.groupInfo),this.wsSend({reqFuncName:s.SETGROUPINFO,operationID:E,userID:this.uid,data:a},n,o)}),this.setGroupMemberNickname=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.SETGROUPMEMBERNICKNAME,operationID:o,userID:this.uid,data:e},i,n)}),this.getGroupsInfo=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.GETGROUPSINFO,operationID:o,userID:this.uid,data:e},i,n)}),this.joinGroup=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.JOINGROUP,operationID:o,userID:this.uid,data:e},i,n)}),this.searchGroups=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.SEARCHGROUPS,operationID:o,userID:this.uid,data:e},i,n)}),this.quitGroup=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.QUITGROUP,operationID:o,userID:this.uid,data:e},i,n)}),this.dismissGroup=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.DISMISSGROUP,operationID:o,userID:this.uid,data:e},i,n)}),this.changeGroupMute=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.CHANGEGROUPMUTE,operationID:o,userID:this.uid,data:e},i,n)}),this.changeGroupMemberMute=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.CHANGEGROUPMEMBERMUTE,operationID:o,userID:this.uid,data:e},i,n)}),this.transferGroupOwner=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.TRANSFERGROUPOWNER,operationID:o,userID:this.uid,data:e},i,n)}),this.getSendGroupApplicationList=e=>new Promise((t,i)=>{const n=e||r(this.uid);this.wsSend({reqFuncName:s.GETSENDGROUPAPPLICATIONLIST,operationID:n,userID:this.uid,data:""},t,i)}),this.getRecvGroupApplicationList=e=>new Promise((t,i)=>{const n=e||r(this.uid);this.wsSend({reqFuncName:s.GETRECVGROUPAPPLICATIONLIST,operationID:n,userID:this.uid,data:""},t,i)}),this.acceptGroupApplication=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.ACCEPTGROUPAPPLICATION,operationID:o,userID:this.uid,data:e},i,n)}),this.refuseGroupApplication=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.REFUSEGROUPAPPLICATION,operationID:o,userID:this.uid,data:e},i,n)}),this.signalingInvite=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid),E={};E.invitation=e,this.wsSend({reqFuncName:s.SIGNAL_INGINVITE,operationID:o,userID:this.uid,data:E},i,n)}),this.signalingInviteInGroup=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid),E={};E.invitation=e,this.wsSend({reqFuncName:s.SIGNALINGINVITEINGROUP,operationID:o,userID:this.uid,data:E},i,n)}),this.signalingAccept=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.SIGNALINGACCEPT,operationID:o,userID:this.uid,data:e},i,n)}),this.signalingReject=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.SIGNALINGREJECT,operationID:o,userID:this.uid,data:e},i,n)}),this.signalingCancel=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.SIGNALINGCANCEL,operationID:o,userID:this.uid,data:e},i,n)}),this.signalingHungUp=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.SIGNALINGHUNGUP,operationID:o,userID:this.uid,data:e},i,n)}),this.getSubDepartment=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.GETSUBDEPARTMENT,operationID:o,userID:this.uid,data:e},i,n)}),this.getDepartmentMember=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.GETDEPARTMENTMEMBER,operationID:o,userID:this.uid,data:e},i,n)}),this.getUserInDepartment=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.GETUSERINDEPARTMENT,operationID:o,userID:this.uid,data:e},i,n)}),this.getDepartmentMemberAndSubDepartment=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.GETDEPARTMENTMEMBERANDSUBDEPARTMENT,operationID:o,userID:this.uid,data:e},i,n)}),this.getDepartmentInfo=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.GETDEPARTMENTINFO,operationID:o,userID:this.uid,data:e},i,n)}),this.searchOrganization=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid),E=e;E.input=JSON.stringify(E.input),this.wsSend({reqFuncName:s.SEARCHORGANIZATION,operationID:o,userID:this.uid,data:E},i,n)}),this.resetConversationGroupAtType=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.RESETCONVERSATIONGROUPATTYPE,operationID:o,userID:this.uid,data:e},i,n)}),this.setGroupMemberRoleLevel=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.SETGROUPMEMBERROLELEVEL,operationID:o,userID:this.uid,data:e},i,n)}),this.setGroupVerification=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.SETGROUPVERIFICATION,operationID:o,userID:this.uid,data:e},i,n)}),this.setGlobalRecvMessageOpt=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.SETGLOBALRECVMESSAGEOPT,operationID:o,userID:this.uid,data:e},i,n)}),this.newRevokeMessage=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.NEWREVOKEMESSAGE,operationID:o,userID:this.uid,data:e},i,n)}),this.findMessageList=(e,t)=>new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.FINDMESSAGELIST,operationID:o,userID:this.uid,data:e},i,n)}),this.wsSend=(e,t,n)=>{var o,E,a;if(null!=(o=window)&&o.navigator&&!window.navigator.onLine)return void n({event:e.reqFuncName,errCode:113,errMsg:"net work error",data:"",operationID:e.operationID||""});if((null==(E=this.ws)?void 0:E.readyState)!==(null==(a=this.ws)?void 0:a.OPEN))return void n({event:e.reqFuncName,errCode:112,errMsg:"ws conecting...",data:"",operationID:e.operationID||""});"object"==typeof e.data&&(e.data=JSON.stringify(e.data));const S={oid:e.operationID||r(this.uid),mname:e.reqFuncName,mrsve:t,mrjet:n,flag:!1};this.ws2promise[S.oid]=S;const I=e=>{this.lastTime=(new Date).getTime();const t=JSON.parse(e.data);if(i[t.event.toUpperCase()])return void this.emit(t.event,t);t.event===s.LOGOUT&&this.ws2promise[t.operationID]&&(this.logoutFlag=!0,this.ws.close(),this.ws=void 0);const n=this.ws2promise[t.operationID];n?(0===t.errCode?n.mrsve(t):n.mrjet(t),delete this.ws2promise[t.operationID]):t.event!==s.SENDMESSAGE&&t.event!==s.SENDMESSAGENOTOSS||this.emit(i.ONRECVNEWMESSAGEFROMOTHERWEB,t)};try{"web"==this.platform?(this.ws.send(JSON.stringify(e)),this.ws.onmessage=I):(this.ws.send({data:JSON.stringify(e),success:e=>{"uni"===this.platform&&void 0!==this.ws._callbacks&&void 0!==this.ws._callbacks.message&&(this.ws._callbacks.message=[])}}),this.onceFlag&&(this.ws.onMessage(I),this.onceFlag=!1))}catch(t){return void n({event:e.reqFuncName,errCode:112,errMsg:"no ws conect...",data:"",operationID:e.operationID||""})}e.reqFuncName===s.LOGOUT&&(this.onceFlag=!0)},this.getPlatform()}login(e){return new Promise((t,i)=>{const{userID:n,token:r,url:o,platformID:E,isBatch:a=!1,operationID:S}=e;this.wsUrl=`${o}?sendID=${n}&token=${r}&platformID=${E}`,this.platformID=E;const I={userID:n,token:r};let d={event:s.LOGIN,errCode:0,errMsg:"",data:"",operationID:S||""};this.createWs(()=>{this.uid=n,this.token=r,this.isBatch=a,this.iLogin(I,S).then(e=>{this.logoutFlag=!1,this.heartbeat(),t(e)}).catch(e=>{d.errCode=e.errCode,d.errMsg=e.errMsg,i(d)})},()=>{d.errCode=111,d.errMsg="ws connect close...",this.logoutFlag||Object.values(this.ws2promise).forEach(e=>e.mrjet({event:e.mname,errCode:111,errMsg:"ws connect close...",data:"",operationID:e.oid})),i(d)},e=>{console.log(e),d.errCode=112,d.errMsg="ws connect error...",i(d)}),this.ws||(d.errCode=112,d.errMsg="The current platform is not supported...",i(d))})}iLogin(e,t){return new Promise((i,n)=>{const o=t||r(this.uid);this.wsSend({reqFuncName:s.LOGIN,operationID:o,userID:this.uid,data:e,batchMsg:this.isBatch?1:0},i,n)})}logout(e){return new Promise((t,i)=>{const n=e||r(this.uid);this.wsSend({reqFuncName:s.LOGOUT,operationID:n,userID:this.uid,data:""},t,i)})}getPlatform(){const e=typeof WebSocket,t=typeof uni,s=typeof wx;"undefined"===e?("object"===s&&(this.platform="wx"),"object"===t&&(this.platform="uni"),this.platform="unknow"):this.platform="web"}createWs(e,t,s){return console.log("start createWs..."),new Promise((i,n)=>{var r;null==(r=this.ws)||r.close(),this.ws=void 0;let o=()=>{this.iLogin({userID:this.uid,token:this.token}).then(e=>{this.logoutFlag=!1,console.log("iLogin suc..."),this.heartbeat(),i()})};e&&(o=e);let E=()=>{console.log("ws close agin:::"),this.logoutFlag||Object.values(this.ws2promise).forEach(e=>e.mrjet({event:e.mname,errCode:111,errMsg:"ws connect close...",data:"",operationID:e.oid}))};t&&(E=t);let a=()=>{};if(s&&(a=s),"web"===this.platform)return this.ws=new WebSocket(this.wsUrl),this.ws.onclose=E,this.ws.onopen=o,void(this.ws.onerror=a);const S="uni"===this.platform?uni:wx;this.ws=S.connectSocket({url:this.wsUrl,complete:()=>{}}),this.ws.onClose(E),this.ws.onOpen(o),this.ws.onError(a)})}reconnect(){this.onceFlag||(this.onceFlag=!0),this.lock||(this.lock=!0,this.clearTimer(),this.timer=setTimeout(()=>{this.createWs(),this.lock=!1},500))}clearTimer(){this.timer&&clearTimeout(this.timer)}heartbeat(){console.log("start heartbeat..."),this.clearTimer();const e=()=>{var e,t,s,i;this.logoutFlag?this.worker&&n(this.worker):(null==(e=this.ws)?void 0:e.readyState)===(null==(t=this.ws)?void 0:t.CONNECTING)||(null==(s=this.ws)?void 0:s.readyState)===(null==(i=this.ws)?void 0:i.OPEN)?(new Date).getTime()-this.lastTime<9e3||this.getLoginStatus().catch(e=>this.reconnect()):this.reconnect()};this.worker&&n(this.worker);try{this.worker=((e,t)=>{const s=function(e){const t=new Blob(["(function (e) {\n      setInterval(function () {\n        this.postMessage(null)\n      }, 10000)\n    })()"]),s=window.URL.createObjectURL(t);return new Worker(s)}();return s.onmessage=e,s})(e)}catch(e){}}}var E,a,S,I,d,u,A,h,N,D,O,R;!function(e){e[e.Nomal=0]="Nomal",e[e.Mute=1]="Mute",e[e.WithoutNotify=2]="WithoutNotify"}(E||(E={})),function(e){e[e.Allowed=0]="Allowed",e[e.NotAllowed=1]="NotAllowed"}(a||(a={})),function(e){e[e.NomalGroup=0]="NomalGroup",e[e.SuperGroup=1]="SuperGroup",e[e.WorkingGroup=2]="WorkingGroup"}(S||(S={})),function(e){e[e.ApplyNeedInviteNot=0]="ApplyNeedInviteNot",e[e.AllNeed=1]="AllNeed",e[e.AllNot=2]="AllNot"}(I||(I={})),function(e){e[e.Nomal=0]="Nomal",e[e.Baned=1]="Baned",e[e.Dismissed=2]="Dismissed",e[e.Muted=3]="Muted"}(d||(d={})),function(e){e[e.Invitation=2]="Invitation",e[e.Search=3]="Search",e[e.QrCode=4]="QrCode"}(u||(u={})),function(e){e[e.Nomal=1]="Nomal",e[e.Owner=2]="Owner",e[e.Admin=3]="Admin"}(A||(A={})),function(e){e[e.AtNormal=0]="AtNormal",e[e.AtMe=1]="AtMe",e[e.AtAll=2]="AtAll",e[e.AtAllAtMe=3]="AtAllAtMe",e[e.AtGroupNotice=4]="AtGroupNotice"}(h||(h={})),function(e){e[e.Sending=1]="Sending",e[e.Succeed=2]="Succeed",e[e.Failed=3]="Failed"}(N||(N={})),function(e){e[e.iOS=1]="iOS",e[e.Android=2]="Android",e[e.Windows=3]="Windows",e[e.MacOSX=4]="MacOSX",e[e.Web=5]="Web",e[e.Linux=7]="Linux",e[e.Admin=8]="Admin"}(D||(D={})),function(e){e[e.TEXTMESSAGE=101]="TEXTMESSAGE",e[e.PICTUREMESSAGE=102]="PICTUREMESSAGE",e[e.VOICEMESSAGE=103]="VOICEMESSAGE",e[e.VIDEOMESSAGE=104]="VIDEOMESSAGE",e[e.FILEMESSAGE=105]="FILEMESSAGE",e[e.ATTEXTMESSAGE=106]="ATTEXTMESSAGE",e[e.MERGERMESSAGE=107]="MERGERMESSAGE",e[e.CARDMESSAGE=108]="CARDMESSAGE",e[e.LOCATIONMESSAGE=109]="LOCATIONMESSAGE",e[e.CUSTOMMESSAGE=110]="CUSTOMMESSAGE",e[e.REVOKEMESSAGE=111]="REVOKEMESSAGE",e[e.HASREADRECEIPTMESSAGE=112]="HASREADRECEIPTMESSAGE",e[e.TYPINGMESSAGE=113]="TYPINGMESSAGE",e[e.QUOTEMESSAGE=114]="QUOTEMESSAGE",e[e.FACEMESSAGE=115]="FACEMESSAGE",e[e.ADVANCETEXTMESSAGE=117]="ADVANCETEXTMESSAGE",e[e.ADVANCEREVOKEMESSAGE=118]="ADVANCEREVOKEMESSAGE",e[e.CUSTOMMSGNOTTRIGGERCONVERSATION=119]="CUSTOMMSGNOTTRIGGERCONVERSATION",e[e.CUSTOMMSGONLINEONLY=120]="CUSTOMMSGONLINEONLY",e[e.FRIENDAPPLICATIONAPPROVED=1201]="FRIENDAPPLICATIONAPPROVED",e[e.FRIENDAPPLICATIONREJECTED=1202]="FRIENDAPPLICATIONREJECTED",e[e.FRIENDAPPLICATIONADDED=1203]="FRIENDAPPLICATIONADDED",e[e.FRIENDADDED=1204]="FRIENDADDED",e[e.FRIENDDELETED=1205]="FRIENDDELETED",e[e.FRIENDREMARKSET=1206]="FRIENDREMARKSET",e[e.BLACKADDED=1207]="BLACKADDED",e[e.BLACKDELETED=1208]="BLACKDELETED",e[e.SELFINFOUPDATED=1303]="SELFINFOUPDATED",e[e.NOTIFICATION=1400]="NOTIFICATION",e[e.GROUPCREATED=1501]="GROUPCREATED",e[e.GROUPINFOUPDATED=1502]="GROUPINFOUPDATED",e[e.JOINGROUPAPPLICATIONADDED=1503]="JOINGROUPAPPLICATIONADDED",e[e.MEMBERQUIT=1504]="MEMBERQUIT",e[e.GROUPAPPLICATIONACCEPTED=1505]="GROUPAPPLICATIONACCEPTED",e[e.GROUPAPPLICATIONREJECTED=1506]="GROUPAPPLICATIONREJECTED",e[e.GROUPOWNERTRANSFERRED=1507]="GROUPOWNERTRANSFERRED",e[e.MEMBERKICKED=1508]="MEMBERKICKED",e[e.MEMBERINVITED=1509]="MEMBERINVITED",e[e.MEMBERENTER=1510]="MEMBERENTER",e[e.GROUPDISMISSED=1511]="GROUPDISMISSED",e[e.GROUPMEMBERMUTED=1512]="GROUPMEMBERMUTED",e[e.GROUPMEMBERCANCELMUTED=1513]="GROUPMEMBERCANCELMUTED",e[e.GROUPMUTED=1514]="GROUPMUTED",e[e.GROUPCANCELMUTED=1515]="GROUPCANCELMUTED",e[e.GROUPMEMBERINFOUPDATED=1516]="GROUPMEMBERINFOUPDATED",e[e.BURNMESSAGECHANGE=1701]="BURNMESSAGECHANGE"}(O||(O={})),function(e){e[e.Single=1]="Single",e[e.Group=2]="Group",e[e.SuperGroup=3]="SuperGroup",e[e.Notification=4]="Notification"}(R||(R={}));export{a as AllowType,i as CbEvents,h as GroupAtType,u as GroupJoinSource,A as GroupRole,d as GroupStatus,S as GroupType,I as GroupVerificationType,N as MessageStatus,O as MessageType,o as OpenIMSDK,E as OptType,D as Platform,s as RequestFunc,R as SessionType,e as emitter,r as uuid};
//# sourceMappingURL=index.modern.js.map
