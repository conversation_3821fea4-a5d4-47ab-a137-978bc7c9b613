!function(e,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports):"function"==typeof define&&define.amd?define(["exports"],n):n((e||self).openImSdk={})}(this,function(e){var n,t,r=function(){function e(){this.events=void 0,this.events={}}var n=e.prototype;return n.emit=function(e,n){return this.events[e]&&this.events[e].forEach(function(e){return e(n)}),this},n.on=function(e,n){return this.events[e]?this.events[e].push(n):this.events[e]=[n],this},n.off=function(e,n){if(e&&"function"==typeof n){var t=this.events[e],r=t.findIndex(function(e){return e===n});t.splice(r,1)}else this.events[e]=[];return this},e}();function u(){return u=Object.assign||function(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e},u.apply(this,arguments)}function i(e,n){return i=Object.setPrototypeOf||function(e,n){return e.__proto__=n,e},i(e,n)}e.RequestFunc=void 0,(n=e.RequestFunc||(e.RequestFunc={})).INITSDK="InitSDK",n.LOGIN="Login",n.LOGOUT="Logout",n.GETLOGINSTATUS="GetLoginStatus",n.GETLOGINUSER="GetLoginUser",n.GETSELFUSERINFO="GetSelfUserInfo",n.CREATETEXTMESSAGE="CreateTextMessage",n.CREATETEXTATMESSAGE="CreateTextAtMessage",n.CREATEADVANCEDTEXTMESSAGE="CreateAdvancedTextMessage",n.CREATEIMAGEMESSAGEFROMBYURL="CreateImageMessageByURL",n.CREATESOUNDMESSAGEBYURL="CreateSoundMessageByURL",n.CREATEVIDEOMESSAGEBYURL="CreateVideoMessageByURL",n.CREATEFILEMESSAGEBYURL="CreateFileMessageByURL",n.CREATEIMAGEMESSAGEFROMFULLPATH="CreateImageMessageFromFullPath",n.CREATESOUNDMESSAGEFROMFULLPATH="CreateSoundMessageFromFullPath",n.CREATEVIDEOMESSAGEFROMFULLPATH="CreateVideoMessageFromFullPath",n.CREATEFILEMESSAGEFROMFULLPATH="CreateFileMessageFromFullPath",n.CREATELOCATIONMESSAGE="CreateLocationMessage",n.CREATECUSTOMMESSAGE="CreateCustomMessage",n.CREATEMERGERMESSAGE="CreateMergerMessage",n.CREATEFORWARDMESSAGE="CreateForwardMessage",n.CREATEQUOTEMESSAGE="CreateQuoteMessage",n.CREATEADVANCEDQUOTEMESSAGE="CreateAdvancedQuoteMessage",n.CREATECARDMESSAGE="CreateCardMessage",n.CREATEFACEMESSAGE="CreateFaceMessage",n.SENDMESSAGE="SendMessage",n.SENDMESSAGENOTOSS="SendMessageNotOss",n.GETHISTORYMESSAGELIST="GetHistoryMessageList",n.GETADVANCEDHISTORYMESSAGELIST="GetAdvancedHistoryMessageList",n.GETHISTORYMESSAGELISTREVERSE="GetHistoryMessageListReverse",n.REVOKEMESSAGE="RevokeMessage",n.SETONECONVERSATIONPRIVATECHAT="SetOneConversationPrivateChat",n.DELETEMESSAGEFROMLOCALSTORAGE="DeleteMessageFromLocalStorage",n.DELETEMESSAGEFROMLOCALANDSVR="DeleteMessageFromLocalAndSvr",n.DELETECONVERSATIONFROMLOCALANDSVR="DeleteConversationFromLocalAndSvr",n.DELETEALLCONVERSATIONFROMLOCAL="DeleteAllConversationFromLocal",n.DELETEALLMSGFROMLOCALANDSVR="DeleteAllMsgFromLocalAndSvr",n.DELETEALLMSGFROMLOCAL="DeleteAllMsgFromLocal",n.MARKSINGLEMESSAGEHASREAD="MarkSingleMessageHasRead",n.INSERTSINGLEMESSAGETOLOCALSTORAGE="InsertSingleMessageToLocalStorage",n.INSERTGROUPMESSAGETOLOCALSTORAGE="InsertGroupMessageToLocalStorage",n.TYPINGSTATUSUPDATE="TypingStatusUpdate",n.MARKC2CMESSAGEASREAD="MarkC2CMessageAsRead",n.MARKMESSAGEASREADBYCONID="MarkMessageAsReadByConID",n.CLEARC2CHISTORYMESSAGE="ClearC2CHistoryMessage",n.CLEARC2CHISTORYMESSAGEFROMLOCALANDSVR="ClearC2CHistoryMessageFromLocalAndSvr",n.CLEARGROUPHISTORYMESSAGE="ClearGroupHistoryMessage",n.CLEARGROUPHISTORYMESSAGEFROMLOCALANDSVR="ClearGroupHistoryMessageFromLocalAndSvr",n.ADDFRIEND="AddFriend",n.SEARCHFRIENDS="SearchFriends",n.GETDESIGNATEDFRIENDSINFO="GetDesignatedFriendsInfo",n.GETRECVFRIENDAPPLICATIONLIST="GetRecvFriendApplicationList",n.GETSENDFRIENDAPPLICATIONLIST="GetSendFriendApplicationList",n.GETFRIENDLIST="GetFriendList",n.SETFRIENDREMARK="SetFriendRemark",n.ADDBLACK="AddBlack",n.GETBLACKLIST="GetBlackList",n.REMOVEBLACK="RemoveBlack",n.CHECKFRIEND="CheckFriend",n.ACCEPTFRIENDAPPLICATION="AcceptFriendApplication",n.REFUSEFRIENDAPPLICATION="RefuseFriendApplication",n.DELETEFRIEND="DeleteFriend",n.GETUSERSINFO="GetUsersInfo",n.SETSELFINFO="SetSelfInfo",n.GETALLCONVERSATIONLIST="GetAllConversationList",n.GETCONVERSATIONLISTSPLIT="GetConversationListSplit",n.GETONECONVERSATION="GetOneConversation",n.GETCONVERSATIONIDBYSESSIONTYPE="GetConversationIDBySessionType",n.GETMULTIPLECONVERSATION="GetMultipleConversation",n.DELETECONVERSATION="DeleteConversation",n.SETCONVERSATIONDRAFT="SetConversationDraft",n.PINCONVERSATION="PinConversation",n.GETTOTALUNREADMSGCOUNT="GetTotalUnreadMsgCount",n.GETCONVERSATIONRECVMESSAGEOPT="GetConversationRecvMessageOpt",n.SETCONVERSATIONRECVMESSAGEOPT="SetConversationRecvMessageOpt",n.SEARCHLOCALMESSAGES="SearchLocalMessages",n.MARKGROUPMESSAGEHASREAD="MarkGroupMessageHasRead",n.MARKGROUPMESSAGEASREAD="MarkGroupMessageAsRead",n.INVITEUSERTOGROUP="InviteUserToGroup",n.KICKGROUPMEMBER="KickGroupMember",n.GETGROUPMEMBERSINFO="GetGroupMembersInfo",n.GETGROUPMEMBERLIST="GetGroupMemberList",n.GETGROUPMEMBERLISTBYJOINTIMEFILTER="GetGroupMemberListByJoinTimeFilter",n.SEARCHGROUPMEMBERS="SearchGroupMembers",n.SETGROUPAPPLYMEMBERFRIEND="SetGroupApplyMemberFriend",n.SETGROUPLOOKMEMBERINFO="SetGroupLookMemberInfo",n.GETJOINEDGROUPLIST="GetJoinedGroupList",n.CREATEGROUP="CreateGroup",n.SETGROUPINFO="SetGroupInfo",n.SETGROUPMEMBERNICKNAME="SetGroupMemberNickname",n.GETGROUPSINFO="GetGroupsInfo",n.JOINGROUP="JoinGroup",n.SEARCHGROUPS="SearchGroups",n.QUITGROUP="QuitGroup",n.DISMISSGROUP="DismissGroup",n.CHANGEGROUPMUTE="ChangeGroupMute",n.CHANGEGROUPMEMBERMUTE="ChangeGroupMemberMute",n.TRANSFERGROUPOWNER="TransferGroupOwner",n.GETSENDGROUPAPPLICATIONLIST="GetSendGroupApplicationList",n.GETRECVGROUPAPPLICATIONLIST="GetRecvGroupApplicationList",n.ACCEPTGROUPAPPLICATION="AcceptGroupApplication",n.REFUSEGROUPAPPLICATION="RefuseGroupApplication",n.SIGNAL_INGINVITE="SignalingInvite",n.SIGNALINGINVITEINGROUP="SignalingInviteInGroup",n.SIGNALINGACCEPT="SignalingAccept",n.SIGNALINGREJECT="SignalingReject",n.SIGNALINGCANCEL="SignalingCancel",n.SIGNALINGHUNGUP="SignalingHungUp",n.GETSUBDEPARTMENT="GetSubDepartment",n.GETDEPARTMENTMEMBER="GetDepartmentMember",n.GETUSERINDEPARTMENT="GetUserInDepartment",n.GETDEPARTMENTMEMBERANDSUBDEPARTMENT="GetDepartmentMemberAndSubDepartment",n.GETDEPARTMENTINFO="GetDepartmentInfo",n.SEARCHORGANIZATION="SearchOrganization",n.RESETCONVERSATIONGROUPATTYPE="ResetConversationGroupAtType",n.SETGROUPMEMBERROLELEVEL="SetGroupMemberRoleLevel",n.SETGROUPVERIFICATION="SetGroupVerification",n.SETGLOBALRECVMESSAGEOPT="SetGlobalRecvMessageOpt",n.NEWREVOKEMESSAGE="NewRevokeMessage",n.FINDMESSAGELIST="FindMessageList",e.CbEvents=void 0,(t=e.CbEvents||(e.CbEvents={})).ONCONNECTFAILED="OnConnectFailed",t.ONCONNECTSUCCESS="OnConnectSuccess",t.ONCONNECTING="OnConnecting",t.ONKICKEDOFFLINE="OnKickedOffline",t.ONSELFINFOUPDATED="OnSelfInfoUpdated",t.ONUSERTOKENEXPIRED="OnUserTokenExpired",t.ONPROGRESS="OnProgress",t.ONRECVNEWMESSAGE="OnRecvNewMessage",t.ONRECVNEWMESSAGES="OnRecvNewMessages",t.ONRECVMESSAGEREVOKED="OnRecvMessageRevoked",t.ONRECVC2CREADRECEIPT="OnRecvC2CReadReceipt",t.ONRECVGROUPREADRECEIPT="OnRecvGroupReadReceipt",t.ONCONVERSATIONCHANGED="OnConversationChanged",t.ONNEWCONVERSATION="OnNewConversation",t.ONSYNCSERVERFAILED="OnSyncServerFailed",t.ONSYNCSERVERFINISH="OnSyncServerFinish",t.ONSYNCSERVERSTART="OnSyncServerStart",t.ONTOTALUNREADMESSAGECOUNTCHANGED="OnTotalUnreadMessageCountChanged",t.ONBLACKADDED="OnBlackAdded",t.ONBLACKDELETED="OnBlackDeleted",t.ONFRIENDAPPLICATIONACCEPTED="OnFriendApplicationAccepted",t.ONFRIENDAPPLICATIONADDED="OnFriendApplicationAdded",t.ONFRIENDAPPLICATIONDELETED="OnFriendApplicationDeleted",t.ONFRIENDAPPLICATIONREJECTED="OnFriendApplicationRejected",t.ONFRIENDINFOCHANGED="OnFriendInfoChanged",t.ONFRIENDADDED="OnFriendAdded",t.ONFRIENDDELETED="OnFriendDeleted",t.ONJOINEDGROUPADDED="OnJoinedGroupAdded",t.ONJOINEDGROUPDELETED="OnJoinedGroupDeleted",t.ONGROUPMEMBERADDED="OnGroupMemberAdded",t.ONGROUPMEMBERDELETED="OnGroupMemberDeleted",t.ONGROUPAPPLICATIONADDED="OnGroupApplicationAdded",t.ONGROUPAPPLICATIONDELETED="OnGroupApplicationDeleted",t.ONGROUPINFOCHANGED="OnGroupInfoChanged",t.ONGROUPMEMBERINFOCHANGED="OnGroupMemberInfoChanged",t.ONGROUPAPPLICATIONACCEPTED="OnGroupApplicationAccepted",t.ONGROUPAPPLICATIONREJECTED="OnGroupApplicationRejected",t.ONRECEIVENEWINVITATION="OnReceiveNewInvitation",t.ONINVITEEACCEPTED="OnInviteeAccepted",t.ONINVITEEREJECTED="OnInviteeRejected",t.ONINVITATIONCANCELLED="OnInvitationCancelled",t.ONHANGUP="OnHangUp",t.ONINVITATIONTIMEOUT="OnInvitationTimeout",t.ONINVITEEACCEPTEDBYOTHERDEVICE="OnInviteeAcceptedByOtherDevice",t.ONINVITEEREJECTEDBYOTHERDEVICE="OnInviteeRejectedByOtherDevice",t.ONORGANIZATIONUPDATED="OnOrganizationUpdated",t.ONRECVNEWMESSAGEFROMOTHERWEB="OnRecvNewMessageFromOtherWeb",t.ONNEWRECVMESSAGEREVOKED="OnNewRecvMessageRevoked";var o,a,s,E,c,S,I,d,R,A,N,O,D=function(e){try{e&&e.terminate()}catch(e){console.log(e)}},T=function(e){return(36*Math.random()).toString(36).slice(2)+(new Date).getTime().toString()+e},G=function(n){var t,r;function o(){var t;return(t=n.call(this)||this).ws=void 0,t.uid=void 0,t.token=void 0,t.platform="web",t.wsUrl="",t.lock=!1,t.logoutFlag=!1,t.ws2promise={},t.onceFlag=!0,t.timer=void 0,t.lastTime=0,t.heartbeatCount=0,t.heartbeatStartTime=0,t.platformID=0,t.isBatch=!1,t.worker=null,t.getLoginStatus=function(n){return new Promise(function(r,u){var i=n||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETLOGINSTATUS,operationID:i,userID:t.uid,data:""},r,u)})},t.getLoginUser=function(n){return new Promise(function(r,u){var i=n||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETLOGINUSER,operationID:i,userID:t.uid,data:""},r,u)})},t.getSelfUserInfo=function(n){return new Promise(function(r,u){var i=n||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETSELFUSERINFO,operationID:i,userID:t.uid,data:""},r,u)})},t.getUsersInfo=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETUSERSINFO,operationID:o,userID:t.uid,data:n},u,i)})},t.setSelfInfo=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.SETSELFINFO,operationID:o,userID:t.uid,data:n},u,i)})},t.createTextMessage=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.CREATETEXTMESSAGE,operationID:o,userID:t.uid,data:n},u,i)})},t.createTextAtMessage=function(n,r){return new Promise(function(i,o){var a=u({},n);a.atUserIDList=JSON.stringify(a.atUserIDList),a.atUsersInfo=JSON.stringify(a.atUsersInfo);var s=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.CREATETEXTATMESSAGE,operationID:s,userID:t.uid,data:a},i,o)})},t.createAdvancedTextMessage=function(n,r){return new Promise(function(i,o){var a=u({},n);a.messageEntityList=JSON.stringify(a.messageEntityList);var s=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.CREATEADVANCEDTEXTMESSAGE,operationID:s,userID:t.uid,data:a},i,o)})},t.createImageMessage=function(n,r){return new Promise(function(i,o){var a=r||T(t.uid),s=u({},n);s.bigPicture=JSON.stringify(s.bigPicture),s.snapshotPicture=JSON.stringify(s.snapshotPicture),s.sourcePicture=JSON.stringify(s.sourcePicture);var E={reqFuncName:e.RequestFunc.CREATEIMAGEMESSAGEFROMBYURL,operationID:a,userID:t.uid,data:JSON.stringify(s)};t.wsSend(E,i,o)})},t.createSoundMessage=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid),a={soundBaseInfo:JSON.stringify(n)},s={reqFuncName:e.RequestFunc.CREATESOUNDMESSAGEBYURL,operationID:o,userID:t.uid,data:JSON.stringify(a)};t.wsSend(s,u,i)})},t.createVideoMessage=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid),a={videoBaseInfo:JSON.stringify(n)},s={reqFuncName:e.RequestFunc.CREATEVIDEOMESSAGEBYURL,operationID:o,userID:t.uid,data:JSON.stringify(a)};t.wsSend(s,u,i)})},t.createFileMessage=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid),a={fileBaseInfo:JSON.stringify(n)},s={reqFuncName:e.RequestFunc.CREATEFILEMESSAGEBYURL,operationID:o,userID:t.uid,data:JSON.stringify(a)};t.wsSend(s,u,i)})},t.createFileMessageFromFullPath=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.CREATEFILEMESSAGEFROMFULLPATH,operationID:o,userID:t.uid,data:n},u,i)})},t.createImageMessageFromFullPath=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.CREATEIMAGEMESSAGEFROMFULLPATH,operationID:o,userID:t.uid,data:n},u,i)})},t.createSoundMessageFromFullPath=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.CREATESOUNDMESSAGEFROMFULLPATH,operationID:o,userID:t.uid,data:n},u,i)})},t.createVideoMessageFromFullPath=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.CREATEVIDEOMESSAGEFROMFULLPATH,operationID:o,userID:t.uid,data:n},u,i)})},t.createMergerMessage=function(n,r){return new Promise(function(i,o){var a=r||T(t.uid),s=u({},n);s.messageList=JSON.stringify(n.messageList),s.summaryList=JSON.stringify(n.summaryList),t.wsSend({reqFuncName:e.RequestFunc.CREATEMERGERMESSAGE,operationID:a,userID:t.uid,data:s},i,o)})},t.createForwardMessage=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.CREATEFORWARDMESSAGE,operationID:o,userID:t.uid,data:n},u,i)})},t.createFaceMessage=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.CREATEFACEMESSAGE,operationID:o,userID:t.uid,data:n},u,i)})},t.createLocationMessage=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.CREATELOCATIONMESSAGE,operationID:o,userID:t.uid,data:n},u,i)})},t.createCustomMessage=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.CREATECUSTOMMESSAGE,operationID:o,userID:t.uid,data:n},u,i)})},t.createQuoteMessage=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.CREATEQUOTEMESSAGE,operationID:o,userID:t.uid,data:n},u,i)})},t.createAdvancedQuoteMessage=function(n,r){return new Promise(function(i,o){var a=r||T(t.uid),s=u({},n);s.messageEntityList=JSON.stringify(s.messageEntityList),t.wsSend({reqFuncName:e.RequestFunc.CREATEADVANCEDQUOTEMESSAGE,operationID:a,userID:t.uid,data:s},i,o)})},t.createCardMessage=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.CREATECARDMESSAGE,operationID:o,userID:t.uid,data:n},u,i)})},t.sendMessage=function(n,r){return new Promise(function(i,o){var a=r||T(t.uid),s=u({},n);s.offlinePushInfo=s.offlinePushInfo?JSON.stringify(n.offlinePushInfo):"",t.wsSend({reqFuncName:e.RequestFunc.SENDMESSAGE,operationID:a,userID:t.uid,data:s},i,o)})},t.sendMessageNotOss=function(n,r){return new Promise(function(i,o){var a=r||T(t.uid),s=u({},n);s.offlinePushInfo=s.offlinePushInfo?JSON.stringify(n.offlinePushInfo):"",t.wsSend({reqFuncName:e.RequestFunc.SENDMESSAGENOTOSS,operationID:a,userID:t.uid,data:s},i,o)})},t.getHistoryMessageList=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETHISTORYMESSAGELIST,operationID:o,userID:t.uid,data:n},u,i)})},t.getAdvancedHistoryMessageList=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETADVANCEDHISTORYMESSAGELIST,operationID:o,userID:t.uid,data:n},u,i)})},t.getHistoryMessageListReverse=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETHISTORYMESSAGELISTREVERSE,operationID:o,userID:t.uid,data:n},u,i)})},t.revokeMessage=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.REVOKEMESSAGE,operationID:o,userID:t.uid,data:n},u,i)})},t.setOneConversationPrivateChat=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.SETONECONVERSATIONPRIVATECHAT,operationID:o,userID:t.uid,data:n},u,i)})},t.deleteMessageFromLocalStorage=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.DELETEMESSAGEFROMLOCALSTORAGE,operationID:o,userID:t.uid,data:n},u,i)})},t.deleteMessageFromLocalAndSvr=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.DELETEMESSAGEFROMLOCALANDSVR,operationID:o,userID:t.uid,data:n},u,i)})},t.deleteConversationFromLocalAndSvr=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.DELETECONVERSATIONFROMLOCALANDSVR,operationID:o,userID:t.uid,data:n},u,i)})},t.deleteAllConversationFromLocal=function(n){return new Promise(function(r,u){var i=n||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.DELETEALLCONVERSATIONFROMLOCAL,operationID:i,userID:t.uid,data:""},r,u)})},t.deleteAllMsgFromLocal=function(n){return new Promise(function(r,u){var i=n||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.DELETEALLMSGFROMLOCAL,operationID:i,userID:t.uid,data:""},r,u)})},t.deleteAllMsgFromLocalAndSvr=function(n){return new Promise(function(r,u){var i=n||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.DELETEALLMSGFROMLOCALANDSVR,operationID:i,userID:t.uid,data:""},r,u)})},t.markGroupMessageHasRead=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.MARKGROUPMESSAGEHASREAD,operationID:o,userID:t.uid,data:n},u,i)})},t.markGroupMessageAsRead=function(n,r){return new Promise(function(i,o){var a=r||T(t.uid),s=u({},n);s.msgIDList=JSON.stringify(s.msgIDList),t.wsSend({reqFuncName:e.RequestFunc.MARKGROUPMESSAGEASREAD,operationID:a,userID:t.uid,data:s},i,o)})},t.insertSingleMessageToLocalStorage=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.INSERTSINGLEMESSAGETOLOCALSTORAGE,operationID:o,userID:t.uid,data:n},u,i)})},t.insertGroupMessageToLocalStorage=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.INSERTGROUPMESSAGETOLOCALSTORAGE,operationID:o,userID:t.uid,data:n},u,i)})},t.typingStatusUpdate=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.TYPINGSTATUSUPDATE,operationID:o,userID:t.uid,data:n},u,i)})},t.markC2CMessageAsRead=function(n,r){return new Promise(function(i,o){var a=u({},n);a.msgIDList=JSON.stringify(a.msgIDList);var s=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.MARKC2CMESSAGEASREAD,operationID:s,userID:t.uid,data:a},i,o)})},t.markNotifyMessageHasRead=function(e,n){t.markMessageAsReadByConID({conversationID:e,msgIDList:[]})},t.markMessageAsReadByConID=function(n,r){return new Promise(function(i,o){var a=u({},n);a.msgIDList=JSON.stringify(a.msgIDList);var s=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.MARKMESSAGEASREADBYCONID,operationID:s,userID:t.uid,data:a},i,o)})},t.clearC2CHistoryMessage=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.CLEARC2CHISTORYMESSAGE,operationID:o,userID:t.uid,data:n},u,i)})},t.clearC2CHistoryMessageFromLocalAndSvr=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.CLEARC2CHISTORYMESSAGEFROMLOCALANDSVR,operationID:o,userID:t.uid,data:n},u,i)})},t.clearGroupHistoryMessage=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.CLEARGROUPHISTORYMESSAGE,operationID:o,userID:t.uid,data:n},u,i)})},t.clearGroupHistoryMessageFromLocalAndSvr=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.CLEARGROUPHISTORYMESSAGEFROMLOCALANDSVR,operationID:o,userID:t.uid,data:n},u,i)})},t.getAllConversationList=function(n){return new Promise(function(r,u){var i=n||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETALLCONVERSATIONLIST,operationID:i,userID:t.uid,data:""},r,u)})},t.getConversationListSplit=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETCONVERSATIONLISTSPLIT,operationID:o,userID:t.uid,data:n},u,i)})},t.getOneConversation=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETONECONVERSATION,operationID:o,userID:t.uid,data:n},u,i)})},t.getConversationIDBySessionType=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETCONVERSATIONIDBYSESSIONTYPE,operationID:o,userID:t.uid,data:n},u,i)})},t.getMultipleConversation=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETMULTIPLECONVERSATION,operationID:o,userID:t.uid,data:n},u,i)})},t.deleteConversation=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.DELETECONVERSATION,operationID:o,userID:t.uid,data:n},u,i)})},t.setConversationDraft=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.SETCONVERSATIONDRAFT,operationID:o,userID:t.uid,data:n},u,i)})},t.pinConversation=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.PINCONVERSATION,operationID:o,userID:t.uid,data:n},u,i)})},t.getTotalUnreadMsgCount=function(n){return new Promise(function(r,u){var i=n||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETTOTALUNREADMSGCOUNT,operationID:i,userID:t.uid,data:""},r,u)})},t.getConversationRecvMessageOpt=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETCONVERSATIONRECVMESSAGEOPT,operationID:o,userID:t.uid,data:n},u,i)})},t.setConversationRecvMessageOpt=function(n,r){return new Promise(function(i,o){var a=r||T(t.uid),s=u({},n);s.conversationIDList=JSON.stringify(n.conversationIDList),t.wsSend({reqFuncName:e.RequestFunc.SETCONVERSATIONRECVMESSAGEOPT,operationID:a,userID:t.uid,data:s},i,o)})},t.searchLocalMessages=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.SEARCHLOCALMESSAGES,operationID:o,userID:t.uid,data:n},u,i)})},t.addFriend=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.ADDFRIEND,operationID:o,userID:t.uid,data:n},u,i)})},t.searchFriends=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.SEARCHFRIENDS,operationID:o,userID:t.uid,data:n},u,i)})},t.getDesignatedFriendsInfo=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETDESIGNATEDFRIENDSINFO,operationID:o,userID:t.uid,data:n},u,i)})},t.getRecvFriendApplicationList=function(n){return new Promise(function(r,u){var i=n||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETRECVFRIENDAPPLICATIONLIST,operationID:i,userID:t.uid,data:""},r,u)})},t.getSendFriendApplicationList=function(n){return new Promise(function(r,u){var i=n||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETSENDFRIENDAPPLICATIONLIST,operationID:i,userID:t.uid,data:""},r,u)})},t.getFriendList=function(n){return new Promise(function(r,u){var i=n||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETFRIENDLIST,operationID:i,userID:t.uid,data:""},r,u)})},t.setFriendRemark=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.SETFRIENDREMARK,operationID:o,userID:t.uid,data:n},u,i)})},t.checkFriend=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.CHECKFRIEND,operationID:o,userID:t.uid,data:n},u,i)})},t.acceptFriendApplication=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.ACCEPTFRIENDAPPLICATION,operationID:o,userID:t.uid,data:n},u,i)})},t.refuseFriendApplication=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.REFUSEFRIENDAPPLICATION,operationID:o,userID:t.uid,data:n},u,i)})},t.deleteFriend=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.DELETEFRIEND,operationID:o,userID:t.uid,data:n},u,i)})},t.addBlack=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.ADDBLACK,operationID:o,userID:t.uid,data:n},u,i)})},t.removeBlack=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.REMOVEBLACK,operationID:o,userID:t.uid,data:n},u,i)})},t.getBlackList=function(n){return new Promise(function(r,u){var i=n||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETBLACKLIST,operationID:i,userID:t.uid,data:""},r,u)})},t.inviteUserToGroup=function(n,r){return new Promise(function(i,o){var a=r||T(t.uid),s=u({},n);s.userIDList=JSON.stringify(s.userIDList),t.wsSend({reqFuncName:e.RequestFunc.INVITEUSERTOGROUP,operationID:a,userID:t.uid,data:s},i,o)})},t.kickGroupMember=function(n,r){return new Promise(function(i,o){var a=r||T(t.uid),s=u({},n);s.userIDList=JSON.stringify(s.userIDList),t.wsSend({reqFuncName:e.RequestFunc.KICKGROUPMEMBER,operationID:a,userID:t.uid,data:s},i,o)})},t.getGroupMembersInfo=function(n,r){return new Promise(function(i,o){var a=r||T(t.uid),s=u({},n);s.userIDList=JSON.stringify(s.userIDList),t.wsSend({reqFuncName:e.RequestFunc.GETGROUPMEMBERSINFO,operationID:a,userID:t.uid,data:s},i,o)})},t.getGroupMemberList=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETGROUPMEMBERLIST,operationID:o,userID:t.uid,data:n},u,i)})},t.getGroupMemberListByJoinTimeFilter=function(n,r){return new Promise(function(i,o){var a=r||T(t.uid),s=u({},n);s.filterUserIDList=JSON.stringify(s.filterUserIDList),t.wsSend({reqFuncName:e.RequestFunc.GETGROUPMEMBERLISTBYJOINTIMEFILTER,operationID:a,userID:t.uid,data:s},i,o)})},t.searchGroupMembers=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid),a={reqFuncName:e.RequestFunc.SEARCHGROUPMEMBERS,operationID:o,userID:t.uid,data:{searchParam:JSON.stringify(n)}};t.wsSend(a,u,i)})},t.setGroupApplyMemberFriend=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.SETGROUPAPPLYMEMBERFRIEND,operationID:o,userID:t.uid,data:n},u,i)})},t.setGroupLookMemberInfo=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.SETGROUPLOOKMEMBERINFO,operationID:o,userID:t.uid,data:n},u,i)})},t.getJoinedGroupList=function(n){return new Promise(function(r,u){var i=n||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETJOINEDGROUPLIST,operationID:i,userID:t.uid,data:""},r,u)})},t.createGroup=function(n,r){return new Promise(function(i,o){var a=r||T(t.uid),s=u({},n);s.groupBaseInfo=JSON.stringify(s.groupBaseInfo),s.memberList=JSON.stringify(s.memberList),t.wsSend({reqFuncName:e.RequestFunc.CREATEGROUP,operationID:a,userID:t.uid,data:s},i,o)})},t.setGroupInfo=function(n,r){return new Promise(function(i,o){var a=r||T(t.uid),s=u({},n);s.groupInfo=JSON.stringify(s.groupInfo),t.wsSend({reqFuncName:e.RequestFunc.SETGROUPINFO,operationID:a,userID:t.uid,data:s},i,o)})},t.setGroupMemberNickname=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.SETGROUPMEMBERNICKNAME,operationID:o,userID:t.uid,data:n},u,i)})},t.getGroupsInfo=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETGROUPSINFO,operationID:o,userID:t.uid,data:n},u,i)})},t.joinGroup=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.JOINGROUP,operationID:o,userID:t.uid,data:n},u,i)})},t.searchGroups=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.SEARCHGROUPS,operationID:o,userID:t.uid,data:n},u,i)})},t.quitGroup=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.QUITGROUP,operationID:o,userID:t.uid,data:n},u,i)})},t.dismissGroup=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.DISMISSGROUP,operationID:o,userID:t.uid,data:n},u,i)})},t.changeGroupMute=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.CHANGEGROUPMUTE,operationID:o,userID:t.uid,data:n},u,i)})},t.changeGroupMemberMute=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.CHANGEGROUPMEMBERMUTE,operationID:o,userID:t.uid,data:n},u,i)})},t.transferGroupOwner=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.TRANSFERGROUPOWNER,operationID:o,userID:t.uid,data:n},u,i)})},t.getSendGroupApplicationList=function(n){return new Promise(function(r,u){var i=n||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETSENDGROUPAPPLICATIONLIST,operationID:i,userID:t.uid,data:""},r,u)})},t.getRecvGroupApplicationList=function(n){return new Promise(function(r,u){var i=n||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETRECVGROUPAPPLICATIONLIST,operationID:i,userID:t.uid,data:""},r,u)})},t.acceptGroupApplication=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.ACCEPTGROUPAPPLICATION,operationID:o,userID:t.uid,data:n},u,i)})},t.refuseGroupApplication=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.REFUSEGROUPAPPLICATION,operationID:o,userID:t.uid,data:n},u,i)})},t.signalingInvite=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid),a={};a.invitation=n,t.wsSend({reqFuncName:e.RequestFunc.SIGNAL_INGINVITE,operationID:o,userID:t.uid,data:a},u,i)})},t.signalingInviteInGroup=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid),a={};a.invitation=n,t.wsSend({reqFuncName:e.RequestFunc.SIGNALINGINVITEINGROUP,operationID:o,userID:t.uid,data:a},u,i)})},t.signalingAccept=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.SIGNALINGACCEPT,operationID:o,userID:t.uid,data:n},u,i)})},t.signalingReject=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.SIGNALINGREJECT,operationID:o,userID:t.uid,data:n},u,i)})},t.signalingCancel=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.SIGNALINGCANCEL,operationID:o,userID:t.uid,data:n},u,i)})},t.signalingHungUp=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.SIGNALINGHUNGUP,operationID:o,userID:t.uid,data:n},u,i)})},t.getSubDepartment=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETSUBDEPARTMENT,operationID:o,userID:t.uid,data:n},u,i)})},t.getDepartmentMember=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETDEPARTMENTMEMBER,operationID:o,userID:t.uid,data:n},u,i)})},t.getUserInDepartment=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETUSERINDEPARTMENT,operationID:o,userID:t.uid,data:n},u,i)})},t.getDepartmentMemberAndSubDepartment=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETDEPARTMENTMEMBERANDSUBDEPARTMENT,operationID:o,userID:t.uid,data:n},u,i)})},t.getDepartmentInfo=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETDEPARTMENTINFO,operationID:o,userID:t.uid,data:n},u,i)})},t.searchOrganization=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid),a=n;a.input=JSON.stringify(a.input),t.wsSend({reqFuncName:e.RequestFunc.SEARCHORGANIZATION,operationID:o,userID:t.uid,data:a},u,i)})},t.resetConversationGroupAtType=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.RESETCONVERSATIONGROUPATTYPE,operationID:o,userID:t.uid,data:n},u,i)})},t.setGroupMemberRoleLevel=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.SETGROUPMEMBERROLELEVEL,operationID:o,userID:t.uid,data:n},u,i)})},t.setGroupVerification=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.SETGROUPVERIFICATION,operationID:o,userID:t.uid,data:n},u,i)})},t.setGlobalRecvMessageOpt=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.SETGLOBALRECVMESSAGEOPT,operationID:o,userID:t.uid,data:n},u,i)})},t.newRevokeMessage=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.NEWREVOKEMESSAGE,operationID:o,userID:t.uid,data:n},u,i)})},t.findMessageList=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.FINDMESSAGELIST,operationID:o,userID:t.uid,data:n},u,i)})},t.wsSend=function(n,r,u){var i,o,a;if(null==(i=window)||!i.navigator||window.navigator.onLine)if((null==(o=t.ws)?void 0:o.readyState)===(null==(a=t.ws)?void 0:a.OPEN)){"object"==typeof n.data&&(n.data=JSON.stringify(n.data));var s={oid:n.operationID||T(t.uid),mname:n.reqFuncName,mrsve:r,mrjet:u,flag:!1};t.ws2promise[s.oid]=s;var E=function(n){t.lastTime=(new Date).getTime();var r=JSON.parse(n.data);if(e.CbEvents[r.event.toUpperCase()])t.emit(r.event,r);else{r.event===e.RequestFunc.LOGOUT&&t.ws2promise[r.operationID]&&(t.logoutFlag=!0,t.ws.close(),t.ws=void 0);var u=t.ws2promise[r.operationID];u?(0===r.errCode?u.mrsve(r):u.mrjet(r),delete t.ws2promise[r.operationID]):r.event!==e.RequestFunc.SENDMESSAGE&&r.event!==e.RequestFunc.SENDMESSAGENOTOSS||t.emit(e.CbEvents.ONRECVNEWMESSAGEFROMOTHERWEB,r)}};try{"web"==t.platform?(t.ws.send(JSON.stringify(n)),t.ws.onmessage=E):(t.ws.send({data:JSON.stringify(n),success:function(e){"uni"===t.platform&&void 0!==t.ws._callbacks&&void 0!==t.ws._callbacks.message&&(t.ws._callbacks.message=[])}}),t.onceFlag&&(t.ws.onMessage(E),t.onceFlag=!1))}catch(e){return void u({event:n.reqFuncName,errCode:112,errMsg:"no ws conect...",data:"",operationID:n.operationID||""})}n.reqFuncName===e.RequestFunc.LOGOUT&&(t.onceFlag=!0)}else u({event:n.reqFuncName,errCode:112,errMsg:"ws conecting...",data:"",operationID:n.operationID||""});else u({event:n.reqFuncName,errCode:113,errMsg:"net work error",data:"",operationID:n.operationID||""})},t.getPlatform(),t}r=n,(t=o).prototype=Object.create(r.prototype),t.prototype.constructor=t,i(t,r);var a=o.prototype;return a.login=function(n){var t=this;return new Promise(function(r,u){var i=n.userID,o=n.token,a=n.platformID,s=n.isBatch,E=void 0!==s&&s,c=n.operationID;t.wsUrl=n.url+"?sendID="+i+"&token="+o+"&platformID="+a,t.platformID=a;var S={userID:i,token:o},I={event:e.RequestFunc.LOGIN,errCode:0,errMsg:"",data:"",operationID:c||""};t.createWs(function(){t.uid=i,t.token=o,t.isBatch=E,t.iLogin(S,c).then(function(e){t.logoutFlag=!1,t.heartbeat(),r(e)}).catch(function(e){I.errCode=e.errCode,I.errMsg=e.errMsg,u(I)})},function(){I.errCode=111,I.errMsg="ws connect close...",t.logoutFlag||Object.values(t.ws2promise).forEach(function(e){return e.mrjet({event:e.mname,errCode:111,errMsg:"ws connect close...",data:"",operationID:e.oid})}),u(I)},function(e){console.log(e),I.errCode=112,I.errMsg="ws connect error...",u(I)}),t.ws||(I.errCode=112,I.errMsg="The current platform is not supported...",u(I))})},a.iLogin=function(n,t){var r=this;return new Promise(function(u,i){var o=t||T(r.uid);r.wsSend({reqFuncName:e.RequestFunc.LOGIN,operationID:o,userID:r.uid,data:n,batchMsg:r.isBatch?1:0},u,i)})},a.logout=function(n){var t=this;return new Promise(function(r,u){var i=n||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.LOGOUT,operationID:i,userID:t.uid,data:""},r,u)})},a.getPlatform=function(){var e=typeof WebSocket,n=typeof uni,t=typeof wx;"undefined"===e?("object"===t&&(this.platform="wx"),"object"===n&&(this.platform="uni"),this.platform="unknow"):this.platform="web"},a.createWs=function(e,n,t){var r=this;return console.log("start createWs..."),new Promise(function(u,i){var o;null==(o=r.ws)||o.close(),r.ws=void 0;var a=function(){r.iLogin({userID:r.uid,token:r.token}).then(function(e){r.logoutFlag=!1,console.log("iLogin suc..."),r.heartbeat(),u()})};e&&(a=e);var s=function(){console.log("ws close agin:::"),r.logoutFlag||Object.values(r.ws2promise).forEach(function(e){return e.mrjet({event:e.mname,errCode:111,errMsg:"ws connect close...",data:"",operationID:e.oid})})};n&&(s=n);var E=function(){};if(t&&(E=t),"web"===r.platform)return r.ws=new WebSocket(r.wsUrl),r.ws.onclose=s,r.ws.onopen=a,void(r.ws.onerror=E);var c="uni"===r.platform?uni:wx;r.ws=c.connectSocket({url:r.wsUrl,complete:function(){}}),r.ws.onClose(s),r.ws.onOpen(a),r.ws.onError(E)})},a.reconnect=function(){var e=this;this.onceFlag||(this.onceFlag=!0),this.lock||(this.lock=!0,this.clearTimer(),this.timer=setTimeout(function(){e.createWs(),e.lock=!1},500))},a.clearTimer=function(){this.timer&&clearTimeout(this.timer)},a.heartbeat=function(){var e,n,t,r,u=this;console.log("start heartbeat..."),this.clearTimer(),this.worker&&D(this.worker);try{this.worker=(e=function(){var e,n,t,r;u.logoutFlag?u.worker&&D(u.worker):(null==(e=u.ws)?void 0:e.readyState)===(null==(n=u.ws)?void 0:n.CONNECTING)||(null==(t=u.ws)?void 0:t.readyState)===(null==(r=u.ws)?void 0:r.OPEN)?(new Date).getTime()-u.lastTime<9e3||u.getLoginStatus().catch(function(e){return u.reconnect()}):u.reconnect()},n=new Blob(["(function (e) {\n      setInterval(function () {\n        this.postMessage(null)\n      }, 10000)\n    })()"]),t=window.URL.createObjectURL(n),(r=new Worker(t)).onmessage=e,r)}catch(e){}},o}(r);e.OptType=void 0,(o=e.OptType||(e.OptType={}))[o.Nomal=0]="Nomal",o[o.Mute=1]="Mute",o[o.WithoutNotify=2]="WithoutNotify",e.AllowType=void 0,(a=e.AllowType||(e.AllowType={}))[a.Allowed=0]="Allowed",a[a.NotAllowed=1]="NotAllowed",e.GroupType=void 0,(s=e.GroupType||(e.GroupType={}))[s.NomalGroup=0]="NomalGroup",s[s.SuperGroup=1]="SuperGroup",s[s.WorkingGroup=2]="WorkingGroup",e.GroupVerificationType=void 0,(E=e.GroupVerificationType||(e.GroupVerificationType={}))[E.ApplyNeedInviteNot=0]="ApplyNeedInviteNot",E[E.AllNeed=1]="AllNeed",E[E.AllNot=2]="AllNot",e.GroupStatus=void 0,(c=e.GroupStatus||(e.GroupStatus={}))[c.Nomal=0]="Nomal",c[c.Baned=1]="Baned",c[c.Dismissed=2]="Dismissed",c[c.Muted=3]="Muted",e.GroupJoinSource=void 0,(S=e.GroupJoinSource||(e.GroupJoinSource={}))[S.Invitation=2]="Invitation",S[S.Search=3]="Search",S[S.QrCode=4]="QrCode",e.GroupRole=void 0,(I=e.GroupRole||(e.GroupRole={}))[I.Nomal=1]="Nomal",I[I.Owner=2]="Owner",I[I.Admin=3]="Admin",e.GroupAtType=void 0,(d=e.GroupAtType||(e.GroupAtType={}))[d.AtNormal=0]="AtNormal",d[d.AtMe=1]="AtMe",d[d.AtAll=2]="AtAll",d[d.AtAllAtMe=3]="AtAllAtMe",d[d.AtGroupNotice=4]="AtGroupNotice",e.MessageStatus=void 0,(R=e.MessageStatus||(e.MessageStatus={}))[R.Sending=1]="Sending",R[R.Succeed=2]="Succeed",R[R.Failed=3]="Failed",e.Platform=void 0,(A=e.Platform||(e.Platform={}))[A.iOS=1]="iOS",A[A.Android=2]="Android",A[A.Windows=3]="Windows",A[A.MacOSX=4]="MacOSX",A[A.Web=5]="Web",A[A.Linux=7]="Linux",A[A.Admin=8]="Admin",e.MessageType=void 0,(N=e.MessageType||(e.MessageType={}))[N.TEXTMESSAGE=101]="TEXTMESSAGE",N[N.PICTUREMESSAGE=102]="PICTUREMESSAGE",N[N.VOICEMESSAGE=103]="VOICEMESSAGE",N[N.VIDEOMESSAGE=104]="VIDEOMESSAGE",N[N.FILEMESSAGE=105]="FILEMESSAGE",N[N.ATTEXTMESSAGE=106]="ATTEXTMESSAGE",N[N.MERGERMESSAGE=107]="MERGERMESSAGE",N[N.CARDMESSAGE=108]="CARDMESSAGE",N[N.LOCATIONMESSAGE=109]="LOCATIONMESSAGE",N[N.CUSTOMMESSAGE=110]="CUSTOMMESSAGE",N[N.REVOKEMESSAGE=111]="REVOKEMESSAGE",N[N.HASREADRECEIPTMESSAGE=112]="HASREADRECEIPTMESSAGE",N[N.TYPINGMESSAGE=113]="TYPINGMESSAGE",N[N.QUOTEMESSAGE=114]="QUOTEMESSAGE",N[N.FACEMESSAGE=115]="FACEMESSAGE",N[N.ADVANCETEXTMESSAGE=117]="ADVANCETEXTMESSAGE",N[N.ADVANCEREVOKEMESSAGE=118]="ADVANCEREVOKEMESSAGE",N[N.CUSTOMMSGNOTTRIGGERCONVERSATION=119]="CUSTOMMSGNOTTRIGGERCONVERSATION",N[N.CUSTOMMSGONLINEONLY=120]="CUSTOMMSGONLINEONLY",N[N.FRIENDAPPLICATIONAPPROVED=1201]="FRIENDAPPLICATIONAPPROVED",N[N.FRIENDAPPLICATIONREJECTED=1202]="FRIENDAPPLICATIONREJECTED",N[N.FRIENDAPPLICATIONADDED=1203]="FRIENDAPPLICATIONADDED",N[N.FRIENDADDED=1204]="FRIENDADDED",N[N.FRIENDDELETED=1205]="FRIENDDELETED",N[N.FRIENDREMARKSET=1206]="FRIENDREMARKSET",N[N.BLACKADDED=1207]="BLACKADDED",N[N.BLACKDELETED=1208]="BLACKDELETED",N[N.SELFINFOUPDATED=1303]="SELFINFOUPDATED",N[N.NOTIFICATION=1400]="NOTIFICATION",N[N.GROUPCREATED=1501]="GROUPCREATED",N[N.GROUPINFOUPDATED=1502]="GROUPINFOUPDATED",N[N.JOINGROUPAPPLICATIONADDED=1503]="JOINGROUPAPPLICATIONADDED",N[N.MEMBERQUIT=1504]="MEMBERQUIT",N[N.GROUPAPPLICATIONACCEPTED=1505]="GROUPAPPLICATIONACCEPTED",N[N.GROUPAPPLICATIONREJECTED=1506]="GROUPAPPLICATIONREJECTED",N[N.GROUPOWNERTRANSFERRED=1507]="GROUPOWNERTRANSFERRED",N[N.MEMBERKICKED=1508]="MEMBERKICKED",N[N.MEMBERINVITED=1509]="MEMBERINVITED",N[N.MEMBERENTER=1510]="MEMBERENTER",N[N.GROUPDISMISSED=1511]="GROUPDISMISSED",N[N.GROUPMEMBERMUTED=1512]="GROUPMEMBERMUTED",N[N.GROUPMEMBERCANCELMUTED=1513]="GROUPMEMBERCANCELMUTED",N[N.GROUPMUTED=1514]="GROUPMUTED",N[N.GROUPCANCELMUTED=1515]="GROUPCANCELMUTED",N[N.GROUPMEMBERINFOUPDATED=1516]="GROUPMEMBERINFOUPDATED",N[N.BURNMESSAGECHANGE=1701]="BURNMESSAGECHANGE",e.SessionType=void 0,(O=e.SessionType||(e.SessionType={}))[O.Single=1]="Single",O[O.Group=2]="Group",O[O.SuperGroup=3]="SuperGroup",O[O.Notification=4]="Notification",e.OpenIMSDK=G,e.emitter=r,e.uuid=T});
//# sourceMappingURL=index.umd.js.map
