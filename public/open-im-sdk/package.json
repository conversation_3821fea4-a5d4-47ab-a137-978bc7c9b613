{"name": "open-im-sdk", "version": "2.3.0-beta.2", "description": "OpenIM SDK for Web and MiniProgram", "main": "index.js", "module": "index.esm.js", "types": "index.d.ts", "unpkg": "index.umd.js", "jsdelivr": "index.umd.js", "exports": {".": {"import": "./index.esm.js", "require": "./index.js"}}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/OpenIMSDK/Open-IM-SDK-Web.git"}, "bugs": {"url": "https://github.com/OpenIMSDK/Open-IM-SDK-Web/issues"}, "homepage": "https://github.com/OpenIMSDK/Open-IM-SDK-Web#readme", "keywords": ["IM", "Cha<PERSON>", "Online", "OpenIM", "SDK"], "author": "blooming", "license": "ISC", "devDependencies": {"microbundle": "^0.13.3", "typescript": "^4.4.3"}}