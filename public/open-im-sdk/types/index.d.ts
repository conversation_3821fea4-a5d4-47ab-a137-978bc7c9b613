import { RequestFunc } from "../constants";
export declare type InitConfig = {
    userID: string;
    token: string;
    url: string;
    platformID: number;
    isBatch?: boolean;
    operationID?: string;
};
export declare type WsParams = {
    reqFuncName: RequestFunc;
    operationID: string;
    userID: string | undefined;
    data: any;
};
export declare type WsResponse = {
    event: RequestFunc;
    errCode: number;
    errMsg: string;
    data: any;
    operationID: string;
};
export declare type LoginParams = {
    userID: string;
    token: string;
};
export declare type AdvancedMsgParams = {
    text: string;
    messageEntityList?: MessageEntity[];
};
export declare type MessageEntity = {
    type: string;
    offset: number;
    length: number;
    url?: string;
    info?: string;
};
export declare type AtMsgParams = {
    text: string;
    atUserIDList: string[];
    atUsersInfo?: AtUsersInfoItem[];
    message?: string;
};
export declare type AtUsersInfoItem = {
    atUserID: string;
    groupNickname: string;
};
export declare type ImageMsgParams = {
    sourcePicture: PicBaseInfo;
    bigPicture: PicBaseInfo;
    snapshotPicture: PicBaseInfo;
};
export declare type PicBaseInfo = {
    uuid: string;
    type: string;
    size: number;
    width: number;
    height: number;
    url: string;
};
export declare type SoundMsgParams = {
    uuid: string;
    soundPath: string;
    sourceUrl: string;
    dataSize: number;
    duration: number;
};
export declare type VideoMsgParams = {
    videoPath: string;
    duration: number;
    videoType: string;
    snapshotPath: string;
    videoUUID: string;
    videoUrl: string;
    videoSize: number;
    snapshotUUID: string;
    snapshotSize: number;
    snapshotUrl: string;
    snapshotWidth: number;
    snapshotHeight: number;
};
export declare type FileMsgParams = {
    filePath: string;
    fileName: string;
    uuid: string;
    sourceUrl: string;
    fileSize: number;
};
export declare type FileMsgFullParams = {
    fileFullPath: string;
    fileName: string;
};
export declare type VideoMsgFullParams = {
    videoFullPath: string;
    videoType: string;
    duration: number;
    snapshotFullPath: string;
};
export declare type SouondMsgFullParams = {
    soundPath: string;
    duration: number;
};
export declare type MergerMsgParams = {
    messageList: MessageItem[];
    title: string;
    summaryList: string[];
};
export declare type FaceMessageParams = {
    index: number;
    data: string;
};
export declare type LocationMsgParams = {
    description: string;
    longitude: number;
    latitude: number;
};
export declare type CustomMsgParams = {
    data: string;
    extension: string;
    description: string;
};
export declare type QuoteMsgParams = {
    text: string;
    message: string;
};
export declare type AdvancedQuoteMsgParams = {
    text: string;
    message: string;
    messageEntityList: MessageEntity[];
};
export declare type SendMsgParams = {
    recvID: string;
    groupID: string;
    offlinePushInfo?: OfflinePush;
    message: string;
};
export declare type GetHistoryMsgParams = {
    userID: string;
    groupID: string;
    count: number;
    startClientMsgID: string;
    conversationID?: string;
};
export declare type GetAdvancedHistoryMsgParams = {
    userID: string;
    groupID: string;
    count: number;
    startClientMsgID: string;
    conversationID?: string;
    lastMinSeq: number;
};
export declare type setPrvParams = {
    conversationID: string;
    isPrivate: boolean;
};
export declare type InsertSingleMsgParams = {
    message: string;
    recvID: string;
    sendID: string;
};
export declare type InsertGroupMsgParams = {
    message: string;
    groupID: string;
    sendID: string;
};
export declare type TypingUpdateParams = {
    recvID: string;
    msgTip: string;
};
export declare type MarkC2CParams = {
    userID: string;
    msgIDList: string[];
};
export declare type MarkNotiParams = {
    conversationID: string;
    msgIDList: string[];
};
export declare type SplitParams = {
    offset: number;
    count: number;
};
export declare type GetOneCveParams = {
    sourceID: string;
    sessionType: number;
};
export declare type SetDraftParams = {
    conversationID: string;
    draftText: string;
};
export declare type PinCveParams = {
    conversationID: string;
    isPinned: boolean;
};
export declare type isRecvParams = {
    conversationIDList: string[];
    opt: OptType;
};
export declare enum OptType {
    Nomal = 0,
    Mute = 1,
    WithoutNotify = 2
}
export declare type SearchLocalParams = {
    conversationID: string;
    keywordList: string[];
    keywordListMatchType?: number;
    senderUserIDList?: string[];
    messageTypeList?: MessageType[];
    searchTimePosition?: number;
    searchTimePeriod?: number;
    pageIndex?: number;
    count?: number;
};
export declare type AddFriendParams = {
    toUserID: string;
    reqMsg: string;
};
export declare type SearchFriendParams = {
    keywordList: string[];
    isSearchUserID: boolean;
    isSearchNickname: boolean;
    isSearchRemark: boolean;
};
export declare type AccessFriendParams = {
    toUserID: string;
    handleMsg: string;
};
export declare type RemarkFriendParams = {
    toUserID: string;
    remark: string;
};
export declare type InviteGroupParams = {
    groupID: string;
    reason: string;
    userIDList: string[];
};
export declare type GroupMsgReadParams = {
    groupID: string;
    msgIDList: string[];
};
export declare type GetGroupMemberParams = {
    groupID: string;
    filter: number;
    offset: number;
    count: number;
};
export declare type GetGroupMemberByTimeParams = {
    groupID: string;
    filterUserIDList: string[];
    offset: number;
    count: number;
    joinTimeBegin: number;
    joinTimeEnd: number;
};
export declare type SearchGroupMemberParams = {
    groupID: string;
    keywordList: string[];
    isSearchUserID: boolean;
    isSearchMemberNickname: boolean;
    offset: number;
    count: number;
};
export declare type SetMemberAuthParams = {
    rule: AllowType;
    groupID: string;
};
export declare type CreateGroupParams = {
    groupBaseInfo: GroupInitInfo;
    memberList: Member[];
};
export declare type GroupInitInfo = {
    groupType: GroupType;
    groupName: string;
    introduction?: string;
    notification?: string;
    faceURL?: string;
    ex?: string;
};
export declare type Member = {
    userID: string;
    roleLevel: number;
};
export declare type GroupInfoParams = {
    groupID: string;
    groupInfo: GroupBaseInfo;
};
export declare type MemberNameParams = {
    groupID: string;
    userID: string;
    GroupMemberNickname: string;
};
export declare type GroupBaseInfo = Partial<Omit<GroupInitInfo, "groupType">>;
export declare type JoinGroupParams = {
    groupID: string;
    reqMsg: string;
    joinSource: GroupJoinSource;
};
export declare type SearchGroupParams = {
    keywordList: string[];
    isSearchGroupID: boolean;
    isSearchGroupName: boolean;
};
export declare type ChangeGroupMuteParams = {
    groupID: string;
    isMute: boolean;
};
export declare type ChangeGroupMemberMuteParams = {
    groupID: string;
    userID: string;
    mutedSeconds: number;
};
export declare type TransferGroupParams = {
    groupID: string;
    newOwnerUserID: string;
};
export declare type AccessGroupParams = {
    groupID: string;
    fromUserID: string;
    handleMsg: string;
};
export declare type SetGroupRoleParams = {
    groupID: string;
    userID: string;
    roleLevel: GroupRole;
};
export declare type SetGroupVerificationParams = {
    verification: GroupVerificationType;
    groupID: string;
};
export declare type RtcInvite = {
    inviterUserID: string;
    inviteeUserIDList: string[];
    groupID: string;
    roomID: string;
    timeout: number;
    mediaType: string;
    sessionType: number;
    platformID: number;
};
export declare type RtcActionParams = {
    opUserID: string;
    invitation: RtcInvite;
};
export declare type Ws2Promise = {
    oid: string;
    mname: string;
    mrsve: (value: WsResponse | PromiseLike<WsResponse>) => void;
    mrjet: (reason?: any) => void;
    flag: boolean;
};
export declare type GroupApplicationItem = {
    createTime: number;
    creatorUserID: string;
    ex: string;
    gender: number;
    groupFaceURL: string;
    groupID: string;
    groupName: string;
    groupType: number;
    handleResult: number;
    handleUserID: string;
    handledMsg: string;
    handledTime: number;
    introduction: string;
    memberCount: number;
    nickname: string;
    notification: string;
    ownerUserID: string;
    reqMsg: string;
    reqTime: number;
    status: number;
    userFaceURL: string;
    userID: string;
};
export declare type FriendApplicationItem = {
    createTime: number;
    ex: string;
    fromFaceURL: string;
    fromGender: number;
    fromNickname: string;
    fromUserID: string;
    handleMsg: string;
    handleResult: number;
    handleTime: number;
    handlerUserID: string;
    reqMsg: string;
    toFaceURL: string;
    toGender: number;
    toNickname: string;
    toUserID: string;
};
export declare type TotalUserStruct = {
    blackInfo: BlackItem | null;
    friendInfo: FriendItem | null;
    publicInfo: PublicUserItem | null;
};
export declare type PublicUserItem = {
    gender: number;
    nickname: string;
    userID: string;
    faceURL: string;
    ex: string;
};
export declare type FullUserItem = {
    birth: number;
    createTime: number;
    email: string;
    ex: string;
    faceURL: string;
    gender: number;
    nickname: string;
    phoneNumber: string;
    userID: string;
};
export declare type PartialUserItem = Partial<Omit<FullUserItem, "userID">> & {
    userID: string;
};
export declare type FriendItem = {
    addSource: number;
    birth: number;
    createTime: number;
    email: string;
    ex: string;
    faceURL: string;
    userID: string;
    gender: number;
    nickname: string;
    operatorUserID: string;
    ownerUserID: string;
    phoneNumber: string;
    remark: string;
};
export declare type FriendRelationItem = {
    result: number;
    userID: string;
};
export declare type BlackItem = {
    addSource: number;
    userID: string;
    createTime: number;
    ex: string;
    faceURL: string;
    gender: number;
    nickname: string;
    operatorUserID: string;
    ownerUserID: string;
};
export declare type GroupItem = {
    groupID: string;
    groupName: string;
    notification: string;
    notificationUserID: string;
    notificationUpdateTime: number;
    introduction: string;
    faceURL: string;
    ownerUserID: string;
    createTime: number;
    memberCount: number;
    status: GroupStatus;
    creatorUserID: string;
    groupType: number;
    needVerification: GroupVerificationType;
    ex: string;
    applyMemberFriend: AllowType;
    lookMemberInfo: AllowType;
};
export declare enum AllowType {
    Allowed = 0,
    NotAllowed = 1
}
export declare enum GroupType {
    NomalGroup = 0,
    SuperGroup = 1,
    WorkingGroup = 2
}
export declare enum GroupVerificationType {
    ApplyNeedInviteNot = 0,
    AllNeed = 1,
    AllNot = 2
}
export declare enum GroupStatus {
    Nomal = 0,
    Baned = 1,
    Dismissed = 2,
    Muted = 3
}
export declare type GroupMemberItem = {
    groupID: string;
    userID: string;
    nickname: string;
    faceURL: string;
    roleLevel: GroupRole;
    muteEndTime: number;
    joinTime: number;
    joinSource: GroupJoinSource;
    inviterUserID: string;
    operatorUserID: string;
    ex: string;
};
export declare enum GroupJoinSource {
    Invitation = 2,
    Search = 3,
    QrCode = 4
}
export declare enum GroupRole {
    Nomal = 1,
    Owner = 2,
    Admin = 3
}
export declare type ConversationItem = {
    conversationID: string;
    conversationType: SessionType;
    userID: string;
    groupID: string;
    showName: string;
    faceURL: string;
    recvMsgOpt: OptType;
    unreadCount: number;
    groupAtType: GroupAtType;
    latestMsg: string;
    latestMsgSendTime: number;
    draftText: string;
    draftTextTime: number;
    isPinned: boolean;
    isNotInGroup: boolean;
    isPrivateChat: boolean;
    attachedInfo: string;
    ex: string;
};
export declare enum GroupAtType {
    AtNormal = 0,
    AtMe = 1,
    AtAll = 2,
    AtAllAtMe = 3,
    AtGroupNotice = 4
}
export declare type MessageItem = {
    clientMsgID: string;
    serverMsgID: string;
    createTime: number;
    sendTime: number;
    sessionType: SessionType;
    sendID: string;
    recvID: string;
    msgFrom: number;
    contentType: MessageType;
    platformID: Platform;
    senderNickname: string;
    senderFaceUrl: string;
    groupID: string;
    content: string;
    seq: number;
    isRead: boolean;
    status: MessageStatus;
    offlinePush: OfflinePush;
    attachedInfo: string;
    attachedInfoElem: AttachedInfoElem;
    ex: string;
    pictureElem: PictureElem;
    soundElem: SoundElem;
    videoElem: VideoElem;
    fileElem: FileElem;
    faceElem: FaceElem;
    mergeElem: MergeElem;
    atElem: AtElem;
    locationElem: LocationElem;
    customElem: CustomElem;
    quoteElem: QuoteElem;
    notificationElem: NotificationElem;
    progress?: number;
    downloadProgress?: number;
    downloaded?: boolean;
};
export declare enum MessageStatus {
    Sending = 1,
    Succeed = 2,
    Failed = 3
}
export declare enum Platform {
    iOS = 1,
    Android = 2,
    Windows = 3,
    MacOSX = 4,
    Web = 5,
    Linux = 7,
    Admin = 8
}
export declare enum MessageType {
    TEXTMESSAGE = 101,
    PICTUREMESSAGE = 102,
    VOICEMESSAGE = 103,
    VIDEOMESSAGE = 104,
    FILEMESSAGE = 105,
    ATTEXTMESSAGE = 106,
    MERGERMESSAGE = 107,
    CARDMESSAGE = 108,
    LOCATIONMESSAGE = 109,
    CUSTOMMESSAGE = 110,
    REVOKEMESSAGE = 111,
    HASREADRECEIPTMESSAGE = 112,
    TYPINGMESSAGE = 113,
    QUOTEMESSAGE = 114,
    FACEMESSAGE = 115,
    ADVANCETEXTMESSAGE = 117,
    ADVANCEREVOKEMESSAGE = 118,
    CUSTOMMSGNOTTRIGGERCONVERSATION = 119,
    CUSTOMMSGONLINEONLY = 120,
    FRIENDAPPLICATIONAPPROVED = 1201,
    FRIENDAPPLICATIONREJECTED = 1202,
    FRIENDAPPLICATIONADDED = 1203,
    FRIENDADDED = 1204,
    FRIENDDELETED = 1205,
    FRIENDREMARKSET = 1206,
    BLACKADDED = 1207,
    BLACKDELETED = 1208,
    SELFINFOUPDATED = 1303,
    NOTIFICATION = 1400,
    GROUPCREATED = 1501,
    GROUPINFOUPDATED = 1502,
    JOINGROUPAPPLICATIONADDED = 1503,
    MEMBERQUIT = 1504,
    GROUPAPPLICATIONACCEPTED = 1505,
    GROUPAPPLICATIONREJECTED = 1506,
    GROUPOWNERTRANSFERRED = 1507,
    MEMBERKICKED = 1508,
    MEMBERINVITED = 1509,
    MEMBERENTER = 1510,
    GROUPDISMISSED = 1511,
    GROUPMEMBERMUTED = 1512,
    GROUPMEMBERCANCELMUTED = 1513,
    GROUPMUTED = 1514,
    GROUPCANCELMUTED = 1515,
    GROUPMEMBERINFOUPDATED = 1516,
    BURNMESSAGECHANGE = 1701
}
export declare enum SessionType {
    Single = 1,
    Group = 2,
    SuperGroup = 3,
    Notification = 4
}
export declare type NotificationElem = {
    detail: string;
    defaultTips: string;
};
export declare type AtElem = {
    text: string;
    atUserList: string[];
    atUsersInfo?: AtUsersInfoItem[];
    quoteMessage?: string;
    isAtSelf?: boolean;
};
export declare type CustomElem = {
    data: string;
    description: string;
    extension: string;
};
export declare type FileElem = {
    filePath: string;
    uuid: string;
    sourceUrl: string;
    fileName: string;
    fileSize: number;
};
export declare type FaceElem = {
    index: number;
    data: string;
};
export declare type LocationElem = {
    description: string;
    longitude: number;
    latitude: number;
};
export declare type MergeElem = {
    title: string;
    abstractList: string[];
    multiMessage: MessageItem[];
};
export declare type OfflinePush = {
    title: string;
    desc: string;
    ex: string;
    iOSPushSound: string;
    iOSBadgeCount: boolean;
};
export declare type PictureElem = {
    sourcePath: string;
    sourcePicture: Picture;
    bigPicture: Picture;
    snapshotPicture: Picture;
};
export declare type AttachedInfoElem = {
    groupHasReadInfo: GroupHasReadInfo;
    isPrivateChat: boolean;
    hasReadTime: number;
    notSenderNotificationPush: boolean;
};
export declare type GroupHasReadInfo = {
    hasReadCount: number;
    hasReadUserIDList: string[];
    groupMemberCount: number;
};
export declare type Picture = {
    uuid: string;
    type: string;
    size: number;
    width: number;
    height: number;
    url: string;
};
export declare type QuoteElem = {
    text: string;
    quoteMessage: MessageItem;
};
export declare type SoundElem = {
    uuid: string;
    soundPath: string;
    sourceUrl: string;
    dataSize: number;
    duration: number;
};
export declare type VideoElem = {
    videoPath: string;
    videoUUID: string;
    videoUrl: string;
    videoType: string;
    videoSize: number;
    duration: number;
    snapshotPath: string;
    snapshotUUID: string;
    snapshotSize: number;
    snapshotUrl: string;
    snapshotWidth: number;
    snapshotHeight: number;
};
export declare type SearchLocalLogData = {
    conversationID: string;
    conversationType: number;
    faceURL: string;
    messageCount: number;
    messageList: MessageItem[];
    showName: string;
    sendTime?: string;
    latestMsg?: string;
};
export declare type GetSubDepParams = {
    departmentID: string;
    offset: number;
    count: number;
};
export declare type SearchInOrzParams = {
    input: SearchInputType;
    offset: number;
    count: number;
};
export declare type SearchInputType = {
    keyWord: string;
    isSearchUserName: boolean;
    isSearchEnglishName: boolean;
    isSearchPosition: boolean;
    isSearchUserID: boolean;
    isSearchMobile: boolean;
    isSearchEmail: boolean;
    isSearchTelephone: boolean;
};
export interface DepartmentItem {
    attachedInfo: string;
    createTime: number;
    departmentID: string;
    departmentType: number;
    ex: string;
    faceURL: string;
    memberNum: number;
    name: string;
    order: number;
    parentID: string;
    subDepartmentNum: number;
}
export interface DepartmentMemberItem {
    userID: string;
    nickname: string;
    englishName: string;
    faceURL: string;
    gender: number;
    mobile: string;
    telephone: string;
    birth: number;
    email: string;
    departmentID: string;
    order: number;
    position: string;
    leader: number;
    status: number;
    createTime: number;
    ex: string;
    attachedInfo: string;
}
export interface DepartmentSearchResult {
    departmentList: DepartmentItem[];
    departmentMemberList: DepartmentMemberSearchItem[];
}
export interface DepartmentMemberSearchItem extends DepartmentMemberItem {
    departmentName: string;
    parentDepartmentList: {
        name: string;
        departmentID: string;
    };
}
export declare type FindMessageParams = {
    conversationID: string;
    clientMsgIDList: string[];
};
