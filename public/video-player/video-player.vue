<template>
  <view>
    <!-- #ifdef APP -->
    <view>
      <video-player-app
        ref="videoPlayerRef"
        :src="src"
        :videoId="videoId"
        :isPlay="isPlay"
        :width="width"
        :height="height"
        :autoplay="autoplay" />
    </view>
    <!-- #endif -->
    <!-- #ifndef APP -->
    <video-player-napp
      ref="videoPlayerRef"
      :src="src"
      :width="width"
      :height="height"
      :videoId="videoId"
      :isPlay="isPlay"
      :autoplay="autoplay" />
    <!-- #endif -->
  </view>
</template>
<script>
import videoPlayerApp from "./video-player-app.vue";
import videoPlayerNapp from "./video-player-napp.vue";
export default {
  components: {
    videoPlayerApp,
    videoPlayerNapp,
  },
  props: {
    // 视频地址
    videoId: {
      type: String,
      default: "myVideo",
    },
    // 视频地址
    src: {
      type: String,
    },
    // 自动播放
    autoplay: {
      type: Boolean,
      default: true,
    },
    // 封面
    poster: {
      type: String,
    },
    // 步长，表示占比，取值必须大于0且为整数
    step: {
      type: Number,
      default: 1,
    },
    // 初始播放进度，表示占比
    progress: {
      type: Number,
    },
    // 播放速率
    playbackRates: {
      type: Array,
      default: () => [0.8, 1, 1.25, 1.5, 2],
    },
    // 是否播放，控制自动暂停或播放的
    isPlay: {
      type: Boolean,
      default: true,
    },
    // 视频宽度
    width: {
      type: String,
      default: "100%",
    },
    // 视频高度
    // #ifdef APP
    height: {
      type: String,
      default: "390px",
    },
    // #endif
    // #ifndef APP
    height: {
      type: String,
      default: "748rpx",
    },
    // #endif
  },
};
</script>
