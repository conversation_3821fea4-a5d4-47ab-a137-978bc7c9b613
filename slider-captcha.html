<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>滑块验证</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .demo-container {
            background: white;
            border-radius: 12px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .demo-btn {
            background: #1976d2;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .demo-btn:hover {
            background: #1565c0;
        }

        .result-message {
            margin-top: 20px;
            padding: 10px;
            border-radius: 6px;
            font-weight: 500;
            display: none;
        }

        .result-message.success {
            background: #e8f5e8;
            color: #4caf50;
            border: 1px solid #4caf50;
        }

        .result-message.error {
            background: #ffeaea;
            color: #f44336;
            border: 1px solid #f44336;
        }

        .slider-captcha {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .captcha-modal {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            overflow: hidden;
            width: 340px;
        }

        .captcha-header {
            padding: 15px 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .captcha-title {
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }

        .captcha-close {
            cursor: pointer;
            font-size: 20px;
            color: #999;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            background: none;
        }

        .captcha-close:hover {
            color: #666;
        }

        .captcha-content {
            padding: 20px;
        }

        .image-container {
            position: relative;
            width: 300px;
            height: 200px;
            margin: 0 auto 20px;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            overflow: hidden;
        }

        .background-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .puzzle-piece {
            position: absolute;
            width: 60px;
            height: 60px;
            background: rgba(0, 0, 0, 0.3);
            border: 2px solid #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }

        .slider-piece {
            position: absolute;
            width: 60px;
            height: 60px;
            border: 2px solid #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
            background-size: 300px 200px;
            transition: left 0.3s ease;
        }

        .slider-container {
            position: relative;
            margin-bottom: 15px;
        }

        .slider-track {
            position: relative;
            width: 100%;
            height: 40px;
            background: #f1f3f4;
            border-radius: 20px;
            overflow: hidden;
        }

        .slider-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #999;
            font-size: 14px;
            pointer-events: none;
        }

        .slider-button {
            position: absolute;
            top: 0;
            width: 40px;
            height: 40px;
            background: #1976d2;
            border-radius: 20px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s;
            user-select: none;
        }

        .slider-button:hover {
            background: #1565c0;
        }

        .slider-button:active {
            background: #0d47a1;
        }

        .slider-icon {
            color: white;
            font-size: 12px;
            font-weight: bold;
            letter-spacing: 1px;
        }

        .captcha-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .refresh-btn,
        .close-btn {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #f1f3f4;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 16px;
            color: #666;
            transition: background-color 0.2s;
            border: none;
        }

        .refresh-btn:hover,
        .close-btn:hover {
            background: #e0e0e0;
        }

        .success-animation {
            animation: successPulse 0.5s ease-in-out;
        }

        @keyframes successPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h2>滑块验证演示</h2>
        <button class="demo-btn" onclick="showCaptcha()">显示滑块验证</button>
        <div id="resultMessage" class="result-message"></div>
    </div>

    <!-- 滑块验证组件 -->
    <div id="sliderCaptcha" class="slider-captcha">
        <div class="captcha-modal">
            <div class="captcha-header">
                <span class="captcha-title">拖动下方滑块完成拼图</span>
                <button class="captcha-close" onclick="closeCaptcha()">×</button>
            </div>
            
            <div class="captcha-content">
                <div class="image-container">
                    <img id="backgroundImage" class="background-image" />
                    <div id="puzzlePiece" class="puzzle-piece"></div>
                    <div id="sliderPiece" class="slider-piece"></div>
                </div>
                
                <div class="slider-container">
                    <div class="slider-track">
                        <div id="sliderText" class="slider-text">拖动滑块完成验证</div>
                        <div id="sliderButton" class="slider-button">
                            <div class="slider-icon">|||</div>
                        </div>
                    </div>
                </div>
                
                <div class="captcha-footer">
                    <button class="refresh-btn" onclick="refreshCaptcha()">🔄</button>
                    <button class="close-btn" onclick="closeCaptcha()">×</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        class SliderCaptcha {
            constructor() {
                this.puzzleX = 0;
                this.puzzleY = 0;
                this.sliderPosition = 0;
                this.isDragging = false;
                this.startX = 0;
                this.imageWidth = 300;
                this.imageHeight = 200;
                this.pieceSize = 60;
                this.tolerance = 10;
                this.images = [
                    'https://picsum.photos/300/200?random=1',
                    'https://picsum.photos/300/200?random=2',
                    'https://picsum.photos/300/200?random=3',
                    'https://picsum.photos/300/200?random=4',
                    'https://picsum.photos/300/200?random=5',
                    'https://picsum.photos/300/200?random=6',
                    'https://picsum.photos/300/200?random=7',
                    'https://picsum.photos/300/200?random=8'
                ];
                
                this.init();
                this.bindEvents();
            }

            init() {
                this.generateRandomImage();
                this.generatePuzzlePosition();
                this.resetSlider();
            }

            generateRandomImage() {
                const randomIndex = Math.floor(Math.random() * this.images.length);
                const imageUrl = this.images[randomIndex];
                document.getElementById('backgroundImage').src = imageUrl;
                document.getElementById('sliderPiece').style.backgroundImage = `url(${imageUrl})`;
            }

            generatePuzzlePosition() {
                this.puzzleX = Math.random() * (this.imageWidth - this.pieceSize - 50) + 50;
                this.puzzleY = Math.random() * (this.imageHeight - this.pieceSize - 20) + 20;
                
                const puzzlePiece = document.getElementById('puzzlePiece');
                puzzlePiece.style.left = this.puzzleX + 'px';
                puzzlePiece.style.top = this.puzzleY + 'px';
                
                const sliderPiece = document.getElementById('sliderPiece');
                sliderPiece.style.top = this.puzzleY + 'px';
                sliderPiece.style.backgroundPosition = `-${this.puzzleX}px -${this.puzzleY}px`;
            }

            resetSlider() {
                this.sliderPosition = 0;
                document.getElementById('sliderButton').style.left = '0px';
                document.getElementById('sliderPiece').style.left = '0px';
                document.getElementById('sliderText').style.display = 'block';
            }

            bindEvents() {
                const sliderButton = document.getElementById('sliderButton');
                
                sliderButton.addEventListener('mousedown', (e) => this.startDrag(e));
                sliderButton.addEventListener('touchstart', (e) => this.startDrag(e));
                
                document.addEventListener('mousemove', (e) => this.onDrag(e));
                document.addEventListener('mouseup', () => this.endDrag());
                document.addEventListener('touchmove', (e) => this.onDrag(e));
                document.addEventListener('touchend', () => this.endDrag());
            }

            startDrag(e) {
                this.isDragging = true;
                this.startX = e.type === 'mousedown' ? e.clientX : e.touches[0].clientX;
                document.getElementById('sliderText').style.display = 'none';
                e.preventDefault();
            }

            onDrag(e) {
                if (!this.isDragging) return;
                
                const currentX = e.type === 'mousemove' ? e.clientX : e.touches[0].clientX;
                const deltaX = currentX - this.startX;
                const maxSlide = this.imageWidth - this.pieceSize;
                
                this.sliderPosition = Math.max(0, Math.min(deltaX, maxSlide));
                
                document.getElementById('sliderButton').style.left = this.sliderPosition + 'px';
                document.getElementById('sliderPiece').style.left = this.sliderPosition + 'px';
                
                e.preventDefault();
            }

            endDrag() {
                if (!this.isDragging) return;
                
                this.isDragging = false;
                this.checkResult();
            }

            checkResult() {
                const difference = Math.abs(this.sliderPosition - this.puzzleX);
                
                if (difference <= this.tolerance) {
                    this.onSuccess();
                } else {
                    this.onFailure();
                }
            }

            onSuccess() {
                document.getElementById('sliderPiece').classList.add('success-animation');
                showResult('验证成功！', 'success');
                setTimeout(() => {
                    closeCaptcha();
                }, 1000);
            }

            onFailure() {
                setTimeout(() => {
                    this.resetSlider();
                }, 300);
            }

            refresh() {
                this.init();
            }
        }

        let captchaInstance = null;

        function showCaptcha() {
            document.getElementById('sliderCaptcha').style.display = 'flex';
            if (!captchaInstance) {
                captchaInstance = new SliderCaptcha();
            } else {
                captchaInstance.refresh();
            }
            hideResult();
        }

        function closeCaptcha() {
            document.getElementById('sliderCaptcha').style.display = 'none';
            if (!document.getElementById('resultMessage').textContent.includes('成功')) {
                showResult('验证已取消', 'error');
            }
        }

        function refreshCaptcha() {
            if (captchaInstance) {
                captchaInstance.refresh();
            }
        }

        function showResult(message, type) {
            const resultElement = document.getElementById('resultMessage');
            resultElement.textContent = message;
            resultElement.className = `result-message ${type}`;
            resultElement.style.display = 'block';
        }

        function hideResult() {
            document.getElementById('resultMessage').style.display = 'none';
        }

        // 点击遮罩层关闭
        document.getElementById('sliderCaptcha').addEventListener('click', function(e) {
            if (e.target === this) {
                closeCaptcha();
            }
        });
    </script>
</body>
</html>
