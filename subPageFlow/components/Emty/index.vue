<template>
  <view class="emty text-center text-gray" v-if="show">
    <view class="emty-icon" v-if="isShowIcon">
      <image src="/static/public/img/no-item.png" mode="widthFix"></image>
    </view>
    <text>{{ msg }}</text>
  </view>
</template>

<script>
export default {
  props: {
    msg: {
      type: String,
      default: "没有相关信息～",
    },
    show: {
      type: Boolean,
      default: false,
    },
    isShowIcon: {
      type: Boolean,
      default: true,
    },
  },
};
</script>

<style lang="scss" scoped>
.emty {
  font-size: 28rpx;
  padding: 20rpx 0;
  margin-top: 100rpx;
  display: flex;
  justify-content: center;
  align-items: center;
	flex-direction: column;
  .emty-icon {
		width: 400rpx;
		margin-bottom: 20rpx;
  }
}
</style>
