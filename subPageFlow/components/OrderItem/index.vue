<template>
  <view class="order-item">
    <view class="flow-index-content-item">
      <view class="flow-index-content-item-title">
        <view class="left" v-if="order.status != 5 && order.status != 4">
          <text class="iconfont icon-lishi2"></text>
          <text class="time" :style="{ color: theme.tabbarSelectedColor }"
            >{{ calculateDeliveryTime() }}分钟内</text
          >
          <text>送达</text>
        </view>
        <view v-else>
          <text>{{ order.status == 4 ? '已送达' : '已完成' }}</text>
        </view>
        <view class="right" :style="{ color: theme.tabbarSelectedColor }">
          <text class="price">{{ order.fulfillPrice || 0 }}</text>
          <text>元</text>
        </view>
      </view>
      <!-- 内容 -->
      <view class="flow-index-content-item-content">
        <view class="flow-index-content-item-content-item">
          <view class="left">
            <view class="t">
              <view
                class="distance"
                :style="{ color: theme.tabbarSelectedColor }"
                >{{ calculateDistance(order.shopCenterLat, order.shopCenterLng, this.lat, this.lng) }}</view
              >
              <view>km</view>
            </view>
            <view class="iconfont icon-xiajiantou"></view>
            <view class="iconfont icon-xiajiantou"></view>
            <view class="iconfont icon-xiajiantou"></view>
            <view class="t">
              <view
                class="distance"
                :style="{ color: theme.tabbarSelectedColor }"
                >{{ calculateDistance(order.userLat, order.userLng, this.lat, this.lng) }}</view
              >
              <view>km</view>
            </view>
          </view>
          <view class="middle">
            <view class="title">
              <text>{{ order.shopName }}</text>
            </view>
            <view class="address">
              <view class="address-title">用户地址</view>
              <view class="address-detail">{{ order.address }}</view>
            </view>
          </view>
          <view class="right">
            <image @click.stop="goOrderDetail" :src="jiantou" mode="widthFix" />
          </view>
        </view>
        <!-- 中 -->
         <view class="middle-line" v-if="isShowBottom">
          <view class="middle-line-item">
              <text>订单ID：</text>
              <text>{{ order.orderId }}</text>
          </view>
          <view class="middle-line-item">
              <text>订单时间：</text>
              <text>{{ order.createTime }}</text>
          </view>
         </view>
        <!-- 底部 -->
        <view class="bottom" v-if="isShowBottom">
          <view>
            <text :style="{ color: theme.tabbarSelectedColor }">{{
              orderStatusText()
            }}</text>
          </view>
          <view
            class="btn"
            v-if="order.status != 5"
            :style="{ backgroundColor: theme.tabbarSelectedColor }"
            @click.stop="goShop"
          >
            {{ orderStatusSubmitText() }}
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import api from "@/utils/api";
import { calculateDistance } from "@/utils/util";
export default {
  name: "OrderItem",
  props: {
    order: Object,
    isShowBottom: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      theme: getApp().globalData.theme,
      jiantou: require("../../img/jiantou.png"),
      lng: 0,
      lat: 0,
    };
  },
  mounted() {
    this.getLocation();
  },
  methods: {
    getLocation() {
      uni.getLocation({
        type: 'gcj02',
        success: (res) => {
          console.log('getLocation', res);
          this.lng = res.longitude;
          this.lat = res.latitude;
        },
      });
    },
    calculateDistance(lat1, lng1, lat2, lng2) {
      return calculateDistance(lat1, lng1, lat2, lng2);
    },
    /**
     * 计算预计送达时间（分钟）
     * 根据商家到用户的距离，按照每小时10km的速度计算
     */
    calculateDeliveryTime() {
      const distance = calculateDistance(
        this.order.shopCenterLat,
        this.order.shopCenterLng,
        this.order.userLat,
        this.order.userLng
      );
      // 每小时10km，转换为每分钟的公里数
      const speedKmPerMinute = 10 / 60;
      // 计算所需分钟数，向上取整
      const minutes = Math.ceil(distance / speedKmPerMinute);
      // 返回预计送达时间，最少为5分钟
      return Math.max(5, minutes);
    },
    orderStatusText() {
      switch (Number(this.order.status)) {
        case 1:
          return "已接单，正在赶往商家";
        case 2:
          return "已到店，等待取货";
        case 3:
          return "已取货，正在配送中";
        case 4:
          return "已送达";
        default:
          return "";
      }
    },
    orderStatusSubmitText() {
      switch (Number(this.order.status)) {
        case 1:
          return "我已到店";
        case 2:
          return "我已取货";
        case 3:
          return "我已送达";
        case 4:
          return "已送达";
        case 6:
          return "已退款";
        default:
          return "";
      }
    },
    /**
     * 导航
     */
    goOrderDetail() {
      if(Number(this.order.status) == 5) return
      uni.showActionSheet({
        itemList: ["导航到商家", "导航到用户"],
        success: ({ tapIndex }) => {
          if (tapIndex === 0) {
            console.log('导航到商家', this.order);
            uni.openLocation({
              latitude: Number(this.order.shopCenterLat),
              longitude: Number(this.order.shopCenterLng),
              name: this.order.shopName,
              address: this.order.shopName,
            });
          } else {
            console.log('导航到用户', this.order);
            uni.openLocation({
              latitude: Number(this.order.userLat),
              longitude: Number(this.order.userLng),
              name: '用户地址',
              address: this.order.address,
            });
          }
        },
        fail: (error) => {},
      });
    },
    /**
     * 到店
     */
    goShop () {
      if(Number(this.order.status) == 6) {
        uni.showToast({
          title: "订单已退款",
          icon: "none",
          mask: true,
        });
        return
      }
      if(Number(this.order.status) == 4) {
        uni.showToast({
          title: "订单已送达",
          icon: "none",
          mask: true,
        });
        return
      }
      uni.showModal({
        title: "提示",
        content: `是否${this.orderStatusSubmitText().split("我")[1]}？`,
        success: (res) => {
          if (res.confirm) {
            api
              .deliveryOrder({
                id: this.order.id,
                status: Number(this.order.status)+1,
              })
              .then((res) => {
                console.log("deliveryOrder", res);
                uni.showToast({
                  title: this.orderStatusSubmitText().split("我")[1],
                  icon: "success",
                  mask: true,
                });
                this.$emit("goShop", this.order.id);
              });
          }
        },
      });
    },
  },
};
</script>

<style scoped lang="scss">
.order-item {
  .middle-line {
      .middle-line-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: #333;
        font-size: 28rpx;
        margin-bottom: 20rpx;

      }
    }
  .flow-index-content-item {
    background-color: #fff;
    border-radius: 15rpx;
    margin: 20rpx;
    padding: 30rpx;

    .flow-index-content-item-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-bottom: 20rpx;
      .left {
        .iconfont {
          font-size: 24rpx;
          color: #000;
        }
        .time {
          margin: 0 10rpx;
        }
        color: #000;
      }
      .right {
        .price {
          font-size: 34rpx;
          margin-right: 5rpx;
        }
      }
    }

    .flow-index-content-item-content {
      .flow-index-content-item-content-item {
        border-top: 1px solid #f6f6f6;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .left {
          color: #bcb9b9;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          text-align: center;
          .t {
            margin: 12rpx 0;
            .distance {
              font-size: 30rpx;
              color: orange;
              font-weight: 700;
            }
          }
          .iconfont {
            font-size: 16rpx;
            color: #bcb9b9;
            &:nth-child(3) {
              margin-top: -5rpx;
            }
            &:nth-child(4) {
              margin-top: -5rpx;
            }
          }
        }
        .middle {
          max-width: 400rpx;
          height: 260rpx;
          display: flex;
          flex-direction: column;
          .title {
            font-size: 35rpx;
            color: #000;
            font-weight: 700;
            margin-top: 28rpx;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .address {
            margin-top: 46rpx;
            .address-title {
              font-size: 35rpx;
              color: #000;
              font-weight: 700;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
            .address-detail {
              font-size: 28rpx;
              color: #000;
              // 两行
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }
        .right {
          image {
            width: 100rpx;
            margin-top: 10rpx;
          }
        }
      }
      .bottom {
        border-top: 1px solid #f6f6f6;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #ee6b2d;
        padding-top: 30rpx;
        .btn {
          width: 200rpx;
          height: 80rpx;
          border-radius: 10rpx;
          text-align: center;
          line-height: 80rpx;
          color: #fff;
          transition: opacity 0.3s;
          &:active {
            opacity: 0.7;
          }
        }
      }
    }
  }
}
</style>
