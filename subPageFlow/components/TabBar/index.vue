<template>
  <view class="tab-bar">
    <view
      class="tab-bar-item"
      v-for="(item, index) in list"
      :key="index"
      @click="handleClick(item,index)"
      :class="{ active: item.active }"
    >
      <text
        class="iconfont"
        :style="{
          color: item.active ? theme.tabbarSelectedColor : theme.tabbarColor,
        }"
        :class="item.icon"
      ></text>
      <text
        :style="{
          color: item.active ? theme.tabbarSelectedColor : theme.tabbarColor,
        }"
        >{{ item.name }}</text
      >
    </view>
  </view>
</template>

<script>
export default {
  name: "TabBar",
  data() {
    return {
      active: 0,
      theme: getApp().globalData.theme,
      list: [
        {
          name: "大厅",
          id: "home",
          path: "",
          active: true,
          icon: "icon-zhengwudating",
        },
        {
          name: "订单",
          id: "order",
          path: "",
          active: false,
          icon: "icon-dingdan",
        },
        {
          name: "已完成",
          id: "order-done",
          path: "",
          active: false,
          icon: "icon-yiwan<PERSON>",
        },
        {
          name: "我的",
          id: "my",
          path: "/subPageFlow/pages/my/index",
          active: false,
          icon: "icon-wode",
        },
      ],
    };
  },
  methods: {
    handleClick(item, index) {
      if (item.id === "my") {
        uni.navigateTo({
          url: item.path,
        });
        return;
      }
      this.list.forEach((item) => {
        item.active = false;
      });
      item.active = true;
      this.active = index;
      this.$emit("change", index);
    },
  },
};
</script>

<style lang="scss" scoped>
.tab-bar {
  position: fixed;
  bottom: 0;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
  padding: 10rpx 0;
  border-top: 1px solid #ebebeb;
}
.tab-bar-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  text-align: center;
  padding: 10rpx 0;
  .iconfont {
    font-size: 38rpx;
  }
  &.active {
    text,
    .iconfont {
      color: #39b54a;
    }
  }
}
</style>
