<template>
  <view class="bill">
    <!-- 顶部nav -->
    <cu-custom :isBack="true" :bgColor="'bg-' + theme.backgroundColor">
      <block slot="backText">返回</block>
      <block slot="content">余额明细</block>
    </cu-custom>

    <!-- 内容 -->
    <view class="bill-content">
      <view class="bill-tabs">
        <view
          class="bill-tabs-item"
          :class="{ active: type == 1 }"
          @click="changeType(1)"
          >余额明细</view
        >
        <view
          class="bill-tabs-item"
          :class="{ active: type == 2 }"
          @click="changeType(2)"
          >提现记录</view
        >
      </view>
      <!-- 列表 -->
      <view class="bill-list">
        <view class="bill-list-item" v-for="(item, index) in billList" :key="index">
          <view class="bill-list-item-left">
            余额明细
          </view>
          <view class="bill-list-item-right">
            + {{ item }}
          </view>
        </view>
      </view>
    </view>
    <Empty :show="!billList.length" msg="没有相关信息" isShowIcon />
  </view>
</template>

<script>
import Empty from "../../components/Emty/index.vue";
export default {
  components: {
    Empty,
  },
  data() {
    return {
      theme: getApp().globalData.theme,
      type: 1, //1:余额明细，2:提现记录
      billList: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
      pageData: {
        page: 1,
        pageSize: 10,
      },
    };
  },
  onLoad(options) {
    this.type = options.type;
  },
  methods: {
    changeType(type) {
      this.type = type;
    },
  },
};
</script>

<style lang="scss" scoped>
.bill {
  .bill-content {
    .bill-tabs {
      display: flex;
      justify-content: space-between;
      padding: 20rpx;
      background-color: #fff;
      .bill-tabs-item {
        width: 50%;
        text-align: center;
        padding: 10rpx;
      }
      .bill-tabs-item.active {
        color: #ff6203;
        position: relative;
        &::after {
          content: "";
          display: block;
          width: 20%;
          height: 4rpx;
          background-color: #ff6203;
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          animation: active 0.3s ease-in-out forwards;
        }
      }
      @keyframes active {
        0% {
          width: 0;
          opacity: 0;
        }
        100% {
          width: 20%;
          opacity: 1;
        }
      }
    }

    .bill-list {
      margin-top: 20rpx;
      .bill-list-item {
        display: flex;
        justify-content: space-between;
        padding: 20rpx;
        background-color: #fff;
        border-bottom: 1rpx solid #f5f5f5;
        border-radius: 10rpx;
        .bill-list-item-left {
          color: #999;
        }
        .bill-list-item-right {
          color: #ff6203;
        }
      }
    }
  }
}
</style>
