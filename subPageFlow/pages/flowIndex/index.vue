<template>
	<view class="flow-index-container">
		<!-- 顶部nav -->
		<cu-custom :isBack="true" :bgColor="'bg-'+theme.backgroundColor" ref="cuCustom">
			<block slot="backText">返回</block>
			<block slot="content">{{tabBarIndex == 0 ? '接单大厅' : tabBarIndex == 1 ? '我的配送单' : '我的配送单'}}</block>
		</cu-custom>

		<!-- 搜索框 -->
		<view class="cu-bar search" :style="{ top: CustomBar + 'px' }">
			<view class="search-form round">
				<text class="cuIcon-search"></text>
				<input v-model="searchKeyword" class="text-dm text-gray" @input="inputHandel" :focus="false" type="text"
					placeholder="搜索商家" confirm-type="search" @confirm="searchHandle"></input>
			</view>
		</view>
		<!-- 内容区域 -->
		<view class="flow-index-content">
			<view @click="handleClickOrder(item)" v-for="item in list" :key="item">
				<OrderItem :order="item" :isShowBottom="true"  @goShop="goShop"/>
			</view>
		</view>

		<!-- 加载占位 -->
		<view class="cu-load bg-gray" :class="{loading: isLoading}"></view>
		<!-- emty占位 -->
		<Emty :show="!list.length" msg="没有相关信息～"></Emty>
		<!-- 底部tabbar -->
		<TabBar @change="handleChangeTabBar"/>
	</view>
</template>

<script>
import Emty from '../../components/Emty/index.vue'
import TabBar from '../../components/TabBar/index.vue'
import OrderItem from '../../components/OrderItem/index.vue'
import api from 'utils/api'
	export default {
  components: {
    Emty,
    TabBar,
    OrderItem,
	},
		data() {
			return {
				theme: {}, //全局颜色变量
				searchKeyword: '', //搜索的关键词
				list: [], //列表数据
				listScource: [], //列表数据源
				pageData: {
					pageSize: 10,
					total: 0
				}, //分页信息
				isLoading: false, //是否正在加载中
				tabBarIndex: 0, //tabbar当前的索引
				jiantou: require('../../img/jiantou.png'),
				CustomBar: this.CustomBar,
				userInfo: uni.getStorageSync('user_info')
			}
	},
	mounted() {
		console.log(this.$refs.cuCustom);
		
	},
	methods: {
		goShop(id) {
			console.log('goShop', id);
			// 更新列表
			this.getOrderInfoFulfillOrders()
		},
			/**
			 * 小程序配送订单页面（非完成的所有订单）
			 */
			getOrderInfoFulfillOrders() {
				api.getOrderInfoFulfillOrders({
					id: this.userInfo.id
					}).then(res => {
						console.log('getOrderInfoFulfillOrders', res);
						this.list = res.data
						this.listScource = res.data
					})
			},
			/**
			 * 小程序配送订单页面（已完成的所有订单）
			 */
			getOrderInfoFulfillOrdersEnd() {
				api.getOrderInfoFulfillOrdersEnd({
					id: this.userInfo.id
				}).then(res => {
					console.log('getOrderInfoFulfillOrdersEnd', res);
					this.list = res.data
					this.listScource = res.data
				})
		},
			/**
			 * 获取大厅订单
			 */
			getOrderInfoHall() {
				// api.getOrderInfoHall({
				// 	id: this.userInfo.id
				// }).then(res => {
				// 	console.log('getOrderInfoHall', res);
				// })
				console.log('获取大厅订单');
			},
			/**
			 * 初始化数据
			 */
			initData() {
				const app = getApp()
				this.theme = app.globalData.theme
				this.getOrderInfoHall()
				console.log('initData', this.theme)
				console.log('userInfo', this.userInfo);
			},
			// 搜索函数
			searchHandle() {
				console.log('serchHandel', this.searchKeyword);
				// 筛选
				this.list = this.listScource.filter(item => item.shopName.includes(this.searchKeyword))
		},
		inputHandel() {
			// 筛选
			if(this.searchKeyword == '') {
				this.list = this.listScource
			} 
		},
			// 切换tabbar的事件
			handleChangeTabBar(index) {
				console.log('handleChangeTabBar', index);
				this.tabBarIndex = index;
				if (index == 0) {
					uni.setNavigationBarTitle({
						title: '接单大厅'
					})
					this.list = []
				} else if (index == 1) {
					uni.setNavigationBarTitle({
						title: '我的配送单'
					})
					this.getOrderInfoFulfillOrders()
				} else if (index == 2) { 
					uni.setNavigationBarTitle({
						title: '我的配送单'
					})
				this.getOrderInfoFulfillOrdersEnd()
				}
			},
			// 点击订单
			handleClickOrder(item) {
				console.log('handleClickOrder', item);
				uni.navigateTo({
					url: `/subPageFlow/pages/orderDetail/index?id=${item.orderId}&createTime=${item.createTime}`
				})
			}
		},
		onLoad() {
			if (!uni.getStorageSync('user_info') || !uni.getStorageSync('user_info').id) {
					uni.reLaunch({
						url: '/pages/login/index?reUrl=' + encodeURIComponent('/subPageFlow/pages/flowIndex/index')
					});
				return;
			}
			this.initData()
		},
		// 下拉刷新
		onPullDownRefresh() {
			this.initData()
			uni.stopPullDownRefresh()
			if(this.tabBarIndex == 0) {
				this.getOrderInfoHall()
			} else if(this.tabBarIndex == 1) {
				this.getOrderInfoFulfillOrders()
			} else if(this.tabBarIndex == 2) {
				this.getOrderInfoFulfillOrdersEnd()
			}
		}
	}
</script>

<style scoped lang="scss">
	.flow-index-container {
		.search {
			background-color: #fff;
			position: sticky;
			top: 120rpx;
			left: 0;
			z-index: 100;
			box-shadow: 0 0 10rpx 0 rgba(0, 0, 0, 0.1);
		}

		.emty {
			margin-top: 100rpx;
		}

		.flow-index-content {
		
		}
		padding-bottom: 120rpx;
	}
</style>