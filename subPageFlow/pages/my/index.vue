<template>
  <view class="my">
    <view class="my-header">
      <!-- 顶部nav -->
      <cu-custom :isBack="true" :bgColor="'bg-' + theme.backgroundColor">
        <block slot="backText">返回</block>
        <block slot="content">个人中心</block>
      </cu-custom>

      <!-- 内容 -->
      <view class="my-content">
        <view
          class="my-bg"
          :style="{ backgroundColor: theme.tabbarSelectedColor }"
        >
          <!-- 用户信息 -->
          <view class="my-user">
            <view class="my-user-avatar">
              <image
                src="/static/myImg/renwutouxiang.png"
                mode="widthFix"
              ></image>
            </view>
            <view class="my-user-name">
              <view class="my-user-name-text">{{ deliveryOrderPsyInfo.couriersName || '--' }}</view>
              <view class="my-user-phone-text">用户ID：{{ userInfo.id || deliveryOrderPsyInfo.uid || '--' }}</view>
            </view>
          </view>
          <!-- 卡片 -->
          <view class="my-card" style="justify-content: center;">
            <view class="my-card-item">
              <view class="my-card-item-title">累计配送</view>
              <view class="my-card-item-value">{{ deliveryOrderPsyInfo.totalCount || 0 }}份</view>
            </view>
            <!-- <view class="my-card-item">
              <view class="my-card-item-title">总收入</view>
              <view class="my-card-item-value">10元</view>
            </view>
            <view class="my-card-item">
              <view class="my-card-item-title">好评率</view>
              <view class="my-card-item-value">100%</view>
            </view> -->
          </view>

          <!-- 我的钱包 -->
          <view class="my-cell" @click="goMyWallet" v-if="false">
            <view class="my-cell-item">
              <view class="my-cell-item-title">
                <image :src="qianbao" mode="widthFix" />
                <text>我的钱包</text>
              </view>
              <view class="my-cell-item-value">
                <text :style="{ color: theme.tabbarSelectedColor }"
                  >余额：0.00元</text
                >
                <view class="iconfont icon-right"></view>
              </view>
            </view>
          </view>

          <!-- 账单明细 -->
          <view class="my-cell" @click="goBill" v-if="false">
            <view class="my-cell-item">
              <view class="my-cell-item-title">
                <image :src="zhangdan" mode="widthFix" />
                <text>账单明细</text>
              </view>
              <view class="my-cell-item-value">
                <view class="iconfont icon-right"></view>
              </view>
            </view>
          </view>

          <!-- 接单状态 -->
          <view class="my-cell" v-if="false">
            <view class="my-cell-item">
              <view class="my-cell-item-title">
                <image :src="jiedan" mode="widthFix" />
                <text>接单状态</text>
              </view>
              <view class="my-cell-item-value">
                <!-- <view class="iconfont icon-right"></view> -->
              </view>
            </view>
          </view>

          <!-- 提现设置 -->
          <view class="my-cell" @click="goWithdrawalSetting" v-if="false">
            <view class="my-cell-item">
              <view class="my-cell-item-title">
                <image :src="tixian" mode="widthFix" />
                <text>提现设置</text>
              </view>
              <view class="my-cell-item-value">
                <view class="iconfont icon-right"></view>
              </view>
            </view>
          </view>

          <!-- 退出登录 -->
          <!-- <view class="my-cell" @click="handleLogout">
            <view class="my-cell-item">
              <view class="my-cell-item-title">
                <image :src="tuichu" mode="widthFix" />
                <text>退出登录</text>
              </view>
              <view class="my-cell-item-value">
                <view class="iconfont icon-right"></view>
              </view>
            </view>
          </view> -->
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import api from "utils/api";
export default {
  onShow() {
    this.userInfoGet();
  },
  data() {
    return {
      theme: getApp().globalData.theme,
      qianbao: require("../../img/qianbao.png"),
      zhangdan: require("../../img/zhangdan.png"),
      jiedan: require("../../img/jiedan.png"),
      tixian: require("../../img/tixian.png"),
      tuichu: require("../../img/tuichu.png"),
      userInfo: {},
      deliveryOrderPsyInfo: {},
    };
  },
  methods: {
    /**
     * 获取配送员信息
     */
    getDeliveryOrderPsyInfo() {
      api.getDeliveryOrderPsyInfo({
        id: this.userInfo.id,
      }).then((res) => {
        this.deliveryOrderPsyInfo = res.data;
      });
    },
    //获取商城用户信息
    userInfoGet() {
      if (this.userInfo.id) {
        this.getDeliveryOrderPsyInfo();
      } else {
        api.userInfoGet().then((res) => {
          this.userInfo = res.data;
          this.getDeliveryOrderPsyInfo();
        });
      }
    },
    /**
     * 退出登录
     */
    handleLogout() {
      uni.showModal({
        title: "提示",
        content: "确定退出登录吗？",
        showCancel: true,
        success: ({ confirm, cancel }) => {
          if (confirm) {
            console.log("退出登录");
          } else {
            console.log("取消");
          }
        },
      });
    },
    /**
     * 我的钱包
     */
    goMyWallet() {
      uni.navigateTo({
        url: "/subPageFlow/pages/myWallet/index",
      });
    },
    /**
     * 账单明细
     */
    goBill() {
      uni.navigateTo({
        url: "/subPageFlow/pages/bill/index",
      });
    },
    /**
     * 提现设置
     */
    goWithdrawalSetting() {
      uni.navigateTo({
        url: "/subPageFlow/pages/withdrawalSetting/index",
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.my {
  .my-header {
    height: 200rpx;
    background-color: #fff;
  }
  .my-content {
    .my-bg {
      width: 100%;
      height: 260rpx;
      background-color: #fff;
      padding: 40rpx 20rpx;
      .my-user {
        display: flex;
        align-items: center;
        .my-user-avatar {
          width: 100rpx;
          height: 100rpx;
          border-radius: 50%;
          overflow: hidden;
        }
        .my-user-name {
          display: flex;
          flex-direction: column;
          font-size: 32rpx;
          margin-left: 20rpx;
          color: #fff;
          .my-user-name-text {
            margin-bottom: 10rpx;
          }
          .my-user-phone-text {
            font-size: 26rpx;
          }
        }
      }
      .my-card {
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: #fff;
        border-radius: 20rpx;
        padding: 40rpx 50rpx;
        margin-top: 20rpx;
        .my-card-item {
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;
          .my-card-item-title {
            font-size: 24rpx;
            color: #333;
          }
          .my-card-item-value {
            font-size: 32rpx;
            font-weight: bold;
            margin-top: 10rpx;
            color: #000;
          }
        }
      }
      .my-cell {
        background-color: #fff;
        border-radius: 15rpx;
        padding: 20rpx 40rpx;
        margin-top: 25rpx;
        transition: all 0.3s ease;
        &:active {
          opacity: 0.7;
        }
        .my-cell-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          .my-cell-item-title {
            display: flex;
            align-items: center;
            font-size: 32rpx;
            text {
              font-weight: 700;
            }
            image {
              width: 60rpx;
              height: 60rpx;
              margin-right: 10rpx;
            }
          }
          .my-cell-item-value {
            display: flex;
            align-items: center;
            .iconfont {
              font-size: 30rpx;
              margin-left: 10rpx;
            }
          }
        }
      }
    }
  }
}
</style>
