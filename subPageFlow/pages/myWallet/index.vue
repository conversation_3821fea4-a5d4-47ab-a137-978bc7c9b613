<template>
  <view class="my-wallet">
    <!-- 顶部nav -->
    <cu-custom :isBack="true" :bgColor="'bg-' + theme.backgroundColor">
      <block slot="backText">返回</block>
      <block slot="content">余额提现</block>
    </cu-custom>

    <!-- 内容 -->
    <view class="my-wallet-content">
      <!-- 钱包卡片 -->
      <view
        class="wallet-card"
        :style="{ backgroundColor: theme.tabbarSelectedColor }"
      >
        <view class="wallet-card-title"> 可提现余额 </view>
        <view class="wallet-card-value">
          <text>¥</text>
          <text class="p">100</text>
        </view>
        <view class="wallet-card-btn" @click="withdrawalRecord">
          <text>
            <text>提现记录</text>
            <text class="iconfont icon-right"></text>
          </text>
        </view>
      </view>
      <!-- 提现区域 -->
      <view class="withdrawal-area">
        <view class="withdrawal-area-title"> 提现金额（元） </view>
        <view class="withdrawal-area-value">
          <text class="t">¥</text>
          <input
            type="number"
            placeholder="请输入提现金额"
            v-model="withdrawalAmount"
          />
        </view>
        <view class="bottom-text"> 最低提现金额10元 </view>
      </view>
      <!-- 提现方式 -->
      <view class="withdrawal-method">
        <view class="withdrawal-method-title"> 选择提现方式： </view>
        <view class="withdrawal-method-value">
          <radio-group @change="radioChange">
            <label v-for="item in radioList" :key="item.value">
              <view class="radio-item">
                <radio
                  :value="item.value"
                  :checked="item.checked"
                  color="#FF6203"
                  style="transform: scale(0.8)"
                />
              </view>
              <view
                class="radio-name"
                :style="{
                  color: item.checked ? theme.tabbarSelectedColor : '#999',
                }"
                >{{ item.name }}</view
              >
            </label>
          </radio-group>
        </view>
      </view>
      <!-- 提现按钮 -->
      <view
        class="withdrawal-btn"
        :style="{ backgroundColor: theme.tabbarSelectedColor }"
        @click="withdrawal"
      >
        <text>立即提现</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      theme: getApp().globalData.theme,
      radioList: [
        {
          value: 1,
          name: "微信",
          checked: true,
        },
        {
          value: 2,
          name: "支付宝",
          checked: false,
        },
        {
          value: 3,
          name: "银行卡",
          checked: false,
        },
      ],
      radioValue: 1,
      withdrawalAmount: "",
    };
  },
  methods: {
    radioChange(e) {
      console.log(e);
      const val = e.detail.value;
      this.radioList.forEach((item) => {
        item.checked = item.value == val;
      });
      this.radioValue = val;
    },
    /**
     * 提现记录
     */
    withdrawalRecord() {
      uni.navigateTo({
        url: "/subPageFlow/pages/bill/index?type=2",//1:余额明细，2:提现记录
      });
    },
    /**
     * 提现
     */
    withdrawal() {
      if (!this.withdrawalAmount) {
        uni.showToast({
          title: "请输入提现金额",
          icon: "none",
        });
        return;
      }
      if (this.withdrawalAmount < 10) {
        uni.showToast({
          title: "提现金额不能小于10元",
          icon: "none",
        });
        return;
      }
      console.log(this.withdrawalAmount);
    },
  },
};
</script>

<style lang="scss" scoped>
.my-wallet {
  .my-wallet-content {
    padding: 20rpx;
    .wallet-card {
      position: relative;
      background-color: orangered;
      // 右上角圆角大一点
      border-radius: 10rpx;
      border-top-right-radius: 60rpx;
      padding: 40rpx 20rpx 60rpx 40rpx;
      color: #fff;

      .wallet-card-title {
        font-size: 24rpx;
        opacity: 0.8;
      }
      .wallet-card-value {
        font-size: 25rpx;
        font-weight: bold;
        margin-top: 20rpx;
        .p {
          font-size: 70rpx;
        }
      }
      .wallet-card-btn {
        position: absolute;
        right: 0;
        top: 85rpx;
        font-size: 24rpx;
        background-color: rgba(255, 255, 255, 0.3);
        padding: 10rpx 20rpx;
        padding-right: 4rpx;
        border-radius: 32rpx;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        transition: opacity 0.3s;
        &:active {
          opacity: 0.8;
        }
        .iconfont {
          font-size: 20rpx;
          font-weight: bold;
        }
      }
    }

    .withdrawal-area {
      background-color: #fff;
      border-radius: 10rpx;
      padding: 20rpx;
      padding-left: 40rpx;
      margin-top: 20rpx;
      .withdrawal-area-title {
        color: #999;
        padding-bottom: 20rpx;
      }
      .withdrawal-area-value {
        display: flex;
        align-items: center;
        font-size: 36rpx;
        font-weight: bold;
        padding-left: 15rpx;
        margin-bottom: 30rpx;
        margin-top: 10rpx;
        .t {
          font-size: 50rpx;
          margin-right: 30rpx;
        }
      }
      .bottom-text {
        color: #999;
        font-size: 24rpx;
        margin-top: 20rpx;
        border-top: 1rpx solid #eee;
        padding-top: 20rpx;
      }
    }

    .withdrawal-method {
      background-color: #fff;
      border-radius: 10rpx;
      padding: 20rpx;
      padding-left: 40rpx;
      margin-top: 20rpx;
      display: flex;
      align-items: center;
      .withdrawal-method-title {
        color: #000;
        padding-bottom: 20rpx;
        font-size: 28rpx;
      }
      .withdrawal-method-value {
        radio-group {
          display: flex;
          label {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            margin-left: 20rpx;
          }
        }
      }
    }
    .withdrawal-btn {
      background-color: #FF6203;
      border-radius: 45rpx;
      padding: 20rpx;
      padding-left: 40rpx;
      margin-top: 40rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #fff;
      font-size: 28rpx;
      font-weight: bold;
      transition: opacity 0.3s;
      &:active {
        opacity: 0.7;
      }
    }
  }
}
</style>
