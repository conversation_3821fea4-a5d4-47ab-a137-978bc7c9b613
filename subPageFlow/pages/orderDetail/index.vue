<template>
  <view class="order-detail">
    <!-- 顶部nav -->
    <cu-custom :isBack="true" :bgColor="'bg-' + theme.backgroundColor">
      <block slot="backText">返回</block>
      <block slot="content">订单详情</block>
    </cu-custom>

    <view class="order-detail-content">
      <view class="order-detail-content-map">
        <map
          id="myMap"
          :key="keyMaP"
          style="width: 100%; height: 35vh"
          :latitude="latitude"
          show-location
          :longitude="longitude"
          :markers="markers"
          @markertap="markertap"
          @callouttap="callouttap"
          @labeltap="labeltap"
          @click="mapClickHandeler"
          @regionchange="regionchange"
        ></map>
      </view>
      <!-- 订单内容 -->
      <view class="order-detail-content-info">
        <OrderItem :order="order" :isShowBottom="false" />

        <view class="info">
          <!-- 商品清单 -->
          <view class="order-item">
            <view class="item">
              <view class="title">商品清单（1）</view>
              <view class="list">
                <view class="list-item">
                  <text> {{ order.spuName }}</text>
                  <text>¥ {{ order.salesPrice }} x{{ order.quantity }}</text>
                </view>
                <!-- <view class="list-item">
                  <text>消毒棉片 11和</text>
                  <text>¥ 32.00 x1</text>
                </view> -->
              </view>
            </view>
          </view>

          <!-- 配送信息 -->
          <view class="order-item">
            <view class="item">
              <view class="title">配送信息</view>
              <view class="list">
                <view class="list-item" v-if="isUser">
                  <text>{{ isDocket ? '医师' : '配送员' }}</text>
                  <text style="font-weight: 700"
                    >{{ order.couriersName }} ({{ order.couriersPhone }})</text
                  >
                </view>
                <view class="list-item">
                  <text>配送状态</text>
                  <text>{{ isDocket ? orderStatusTextDocket() : orderStatusText() }}</text>
                </view>
                <!-- <view class="list-item">
                  <text>接单时间</text>
                  <text>2021-01-01 12:00:00</text>
                </view> -->
              </view>
            </view>
          </view>

          <!-- 订单信息 -->
          <view class="order-item">
            <view class="item">
              <view class="title">订单信息</view>
              <view class="list">
                <view class="list-item">
                  <text>订单编号：</text>
                  <text>{{ order.orderId }}</text>
                </view>
                <view class="list-item">
                  <text>订单时间：</text>
                  <text>{{ createTime || order.orderCreateTime }}</text>
                </view>
                <!-- <view class="list-item">
                  <text>支付时间：</text>
                  <text>2021-01-01 12:00:00</text>
                </view> -->
                <view class="list-item">
                  <text>支付方式：</text>
                  <text>微信支付</text>
                </view>
                <view class="list-item">
                  <text>商品金额：</text>
                  <text class="p" :style="{ color: theme.tabbarSelectedColor }"
                    >¥ {{ order.salesPrice * order.quantity }}</text
                  >
                </view>
                <view class="list-item">
                  <text>实付款：</text>
                  <text class="p" :style="{ color: theme.tabbarSelectedColor }"
                    >¥ {{ ((order.salesPrice * order.quantity) + Number(order.fulfillPrice) || 0).toFixed(2) }}</text
                  >
                </view>
                <view class="list-item">
                  <text>{{ rederText }}</text>
                  <text
                    class="p"
                    :style="{ color: theme.tabbarSelectedColor }"
                    >¥ {{ Number(order.fulfillPrice) || 0 }}</text
                  >
                </view>
                <!-- <view class="list-item">
                  <text>备注：</text>
                  <text class="p" :style="{ color: theme.tabbarSelectedColor }"
                    >{{ order.remark }}</text
                  >
                </view> -->
              </view>
            </view>
          </view>
        </view>
      </view>
      <!-- 底部工具栏 -->
      <view class="order-detail-content-tool" v-if="!isUser">
        <view class="left">
          <view class="order-detail-content-tool-item" @click="callCustomer">
            <text class="iconfont icon-lianxi1"></text>
            <text>联系顾客</text>
          </view>
          <view class="order-detail-content-tool-item" @click="callShop">
            <text class="iconfont icon-dianhua"></text>
            <text>联系商家</text>
          </view>
        </view>
        <view
          class="btn"
          :style="{ backgroundColor: theme.tabbarSelectedColor }"
          @click="goShop"
        >
          <!-- <text>我已到店</text> -->
          {{ orderStatusSubmitText() }}
        </view>
      </view>
      <view
        class="order-detail-content-tool order-detail-content-tool-user"
        v-else
      >
        <view class="left">
          <view class="order-detail-content-tool-item" @click="callDelivery">
            <text class="iconfont icon-lianxi1"></text>
            <text>{{ isDocket ? '联系医师' : '联系配送员' }}</text>
          </view>
          <view class="order-detail-content-tool-item" @click="callShop">
            <text class="iconfont icon-dianhua"></text>
            <text>联系商家</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
const IMAGE_WIDTH = 43 / 2;
const IMAGE_HEIGHT = 54 / 2;
import OrderItem from "../../components/OrderItem/index.vue";
import api from "@/utils/api";
export default {
  name: "OrderDetail",
  components: {
    OrderItem,
  },
  data() {
    return {
      orderId: "",
      createTime: "",
      theme: getApp().globalData.theme,
      latitude: 39.90872,
      longitude: 116.39748,
      markers: [
        {
          id: 1,
          latitude: 39.90872,
          longitude: 116.39748,
          width: IMAGE_WIDTH, // 标记点图标宽度
          height: IMAGE_HEIGHT, // 标记点图标高度
          iconPath: "/static/myImg/maker.png",
          customCallout: {
            anchorY: 0,
            anchorX: 20,
            display: "ALWAYS",
          },
        },
      ],
      keyMaP: 0,
      order: {},
      isUser: false, //是否事用户查看
    };
  },
  computed: {
		rederText () {
			let text = '配送费'
			if (
				this.order
				&& this.order.spuName
				&& this.order.spuName.includes('上门')
			) {
				text = '交通费'
			} else {
				text = '配送费'
			}
			return text
    },
    // 是否是上门订单
    isDocket() {
      if (
				this.order
				&& this.order.spuName
				&& this.order.spuName.includes('上门')
			) {
				return true
			}
			return false
    },
	},
  methods: {
    orderStatusSubmitText() {
      switch (Number(this.order.status)) {
        case 1:
          return "我已到店";
        case 2:
          return "我已取货";
        case 3:
          return "我已送达";
        case 4:
          return "已送达";
        case 5:
          return "已完成";
        case 6:
          return "已退款";
        default:
          return "";
      }
    },
    orderStatusText() {
      switch (Number(this.order.status)) {
        case 1:
          return "已接单，骑手正在赶往商家";
        case 2:
          return "骑手已到店，等待取货";
        case 3:
          return "骑手已取货，正在配送中";
        case 4:
          return "骑手已送达";
        case 5:
          return "已完成";
        default:
          return "";
      }
    },
    orderStatusTextDocket() {
      switch (Number(this.order.status)) {
        case 1:
          return "已接单，医师正在赶往商家";
        case 2:
          return "医师已到店，等待取货";
        case 3:
          return "医师已取货，正在配送中";
        case 4:
          return "医师已送达";
        case 5:
          return "已完成";
        default:
          return "";
      }
    },
    /**
     * 获取订单详情
     */
    getOrderDetail() {
      api
        .getDeliveryOrderInfo({
          id: this.orderId,
        })
        .then((res) => {
          console.log("getDeliveryOrderInfo", res);
          this.order = res.data;
          // this.order = {
          //   ...this.order, // 复制原有的 order 属性
          //   spuName: '新的上门服务名称'
          // };
          this.latitude = this.order.shopCenterLat;
          this.longitude = this.order.shopCenterLng;
          this.markers = [
            {
              id: 1,
              latitude: this.order.shopCenterLat,
              longitude: this.order.shopCenterLng,
              width: IMAGE_WIDTH, // 标记点图标宽度
              height: IMAGE_HEIGHT, // 标记点图标高度
              iconPath: "/static/myImg/maker.png",
              customCallout: {
                anchorY: 0,
                anchorX: 20,
                display: "ALWAYS",
              },
            },
          ];
        });
    },
    /**
     * 地图点击
     */
    mapClickHandeler(e) {
      console.log("mapClickHandeler", e);
    },
    /**
     * 标记点点击
     */
    markertap(e) {
      console.log("markertap", e);
    },
    /**
     * 气泡点击
     */
    callouttap(e) {
      console.log("callouttap", e);
    },
    /**
     * 标签点击
     */
    labeltap(e) {
      console.log("labeltap", e);
    },
    /**
     * 区域变化
     */
    regionchange(e) {
      console.log("regionchange", e);
    },
    /**
     * 联系顾客
     */
    callCustomer() {
      console.log("联系顾客");
      uni.makePhoneCall({
        phoneNumber: this.order.couriersPhone,
      });
    },
    /**
     * 联系配送员
     */
    callDelivery() {
      console.log("联系配送员");
      uni.makePhoneCall({
        phoneNumber: this.order.couriersPhone,
      });
    },
    /**
     * 联系商家
     */
    callShop() {
      console.log("联系商家");
      uni.makePhoneCall({
        phoneNumber: this.order.shopPhone,
      });
      // uni.navigateTo({
      //   url: '/pages/customer-service/customer-service-list/index?shopId=' + this.id
      // });
    },
    /**
     * 到店
     */
    goShop () {
      if (Number(this.order.status) == 6) {
        uni.showToast({
          title: "订单已退款",
          icon: "none",
          mask: true,
        });
        return;
      }
      if (Number(this.order.status) == 5) {
        uni.showToast({
          title: "订单已完成",
          icon: "none",
          mask: true,
        });
        return;
      }
      if (Number(this.order.status) == 4) {
        uni.showToast({
          title: "订单已送达",
          icon: "none",
          mask: true,
        });
        return;
      }
      uni.showModal({
        title: "提示",
        content: `是否${this.orderStatusSubmitText().split("我")[1]}？`,
        success: (res) => {
          if (res.confirm) {
            api
              .deliveryOrder({
                id: this.order.id,
                status: Number(this.order.status) + 1,
              })
              .then((res) => {
                console.log("deliveryOrder", res);
                this.getOrderDetail();
                uni.showToast({
                  title: this.orderStatusSubmitText().split("我")[1],
                  icon: "success",
                  mask: true,
                });
              });
          }
        },
      });
    },
  },
  onLoad(options) {
    this.orderId = options.id;
    this.createTime = options.createTime;
    this.getOrderDetail();
    console.log("orderId", this.orderId);
    if (options.isUser == 1) {
      this.isUser = true;
    }
  },
};
</script>

<style lang="scss" scoped>
.order-detail {
  padding-bottom: 120rpx;
  .order-detail-header {
    height: 80rpx;
    background-color: #fff;
  }

  .order-detail-content-info {
    > .info {
      padding: 20rpx;
    }
    .order-item {
      background-color: #fff;
      margin-bottom: 20rpx;
      padding: 20rpx;
      border-radius: 20rpx;
      .item {
        .title {
          font-size: 30rpx;
          font-weight: 700;
        }
        .list {
          margin-top: 30rpx;
          .list-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 28rpx;
            margin-top: 15rpx;
            .p {
              color: orangered;
            }
          }
        }
      }
    }
  }

  .order-detail-content-tool {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 100rpx;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .left {
      display: flex;
      align-items: center;
      .order-detail-content-tool-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 170rpx;
        font-size: 24rpx;
        transition: all 0.3s ease;
        &:active {
          opacity: 0.7;
        }
        &:nth-child(1) {
          border-right: 1px solid #ccc;
        }
        .iconfont {
          font-size: 40rpx;
        }
      }
    }
    .btn {
      flex: 1;
      height: 100%;
      background-color: #000;
      color: #fff;
      text-align: center;
      display: flex;
      justify-content: center;
      align-items: center;
      transition: all 0.3s ease;
      &:active {
        opacity: 0.7;
      }
    }
  }
  .order-detail-content-tool-user {
    .left {
      width: 100%;
      .order-detail-content-tool-item {
        width: 50%;
        &:nth-child(1) {
          border-right: 1px solid #ccc;
        }
      }
    }
  }
}
</style>
