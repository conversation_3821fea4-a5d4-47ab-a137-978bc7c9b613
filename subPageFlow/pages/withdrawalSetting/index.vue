<template>
  <view class="withdrawal-setting">
    <!-- 顶部nav -->
    <cu-custom :isBack="true" :bgColor="'bg-' + theme.backgroundColor">
      <block slot="backText">返回</block>
      <block slot="content">提现设置</block>
    </cu-custom>

    <!-- 内容 -->
    <view class="withdrawal-setting-content">
      <view class="withdrawal-setting-content-item">
        <view class="withdrawal-setting-content-item-title">微信号</view>
        <view class="withdrawal-setting-content-item-input">
          <input type="text" placeholder="请输入微信号" v-model="form.wechat" />
        </view>
      </view>
      <view class="withdrawal-setting-content-item">
        <view class="withdrawal-setting-content-item-title">支付宝账号</view>
        <view class="withdrawal-setting-content-item-input">
          <input
            type="text"
            placeholder="请输入支付宝账号"
            v-model="form.alipay"
          />
        </view>
      </view>
      <view class="withdrawal-setting-content-item-card">
        <view class="son-item">
          <view class="son-item-title">开户行</view>
          <view class="son-item-input">
            <picker @change="bindPickerChange" :value="index" :range="bankList">
              <view class="picker">
                {{ index == null ? "请选择开户行" : bankList[index] }}
              </view>
            </picker>
          </view>
        </view>
        <view class="son-item">
          <view class="son-item-title">持卡人姓名</view>
          <view class="son-item-input">
            <input
              type="text"
              placeholder="请输入持卡人姓名"
              v-model="form.name"
            />
          </view>
        </view>
        <view class="son-item">
          <view class="son-item-title">银行卡号</view>
          <view class="son-item-input">
            <input
              type="text"
              placeholder="请输入银行卡号"
              v-model="form.card"
            />
          </view>
        </view>
      </view>
      <view class="withdrawal-setting-content-item">
        <view class="withdrawal-setting-content-item-title">短信验证码</view>
        <view class="withdrawal-setting-content-item-input">
          <input
            type="text"
            placeholder="请输入短信验证码"
            v-model="form.code"
          />
          <text class="get-code" @click="getCode" :style="{ color: theme.tabbarSelectedColor }"
            >{{ codeTime != 60 ? codeTime + 's后获取' : '获取验证码' }}</text
          >
        </view>
      </view>
      <!-- 保存 -->
      <view
        class="save-btn"
        @click="save"
        :style="{ backgroundColor: theme.tabbarSelectedColor }"
      >
        <text>保存</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      theme: getApp().globalData.theme,
      bankList: ["中国银行", "中国工商银行", "中国建设银行", "中国农业银行"],
      index: null,
      form: {
        wechat: "",
        alipay: "",
        name: "",
        card: "",
        code: "",
      },
      codeTime: 60,
      codeTimer: null,
    };
  },
  onUnload() {
    clearInterval(this.codeTimer);
    this.codeTime = 60;
  },
  beforeDestroy() {
    this.codeTime = 60;
    clearInterval(this.codeTimer);
  },
  methods: {
    bindPickerChange(e) {
      this.index = e.detail.value;
    },
    /**
     * 校验表单
     */
    verify() {
      return new Promise((resolve, reject) => {
        if (!this.form.wechat) {
          reject("请输入微信号");
        }
        if (!this.form.alipay) {
          reject("请输入支付宝账号");
        }
        if (this.index == null) {
          reject("请选择开户行");
        }
        if (!this.form.name) {
          reject("请输入持卡人姓名");
        }
        if (!this.form.card) {
          reject("请输入银行卡号");
        }
        if (!this.form.code) {
          reject("请输入短信验证码");
        }
        resolve();
      });
    },
    /**
     * 获取验证码
     */
    getCode () {
      if (this.codeTime != 60) {
        return;
      }
      this.codeTime--;
      this.codeTimer = setInterval(() => {
        this.codeTime--;
        if (this.codeTime <= 0) {
          clearInterval(this.codeTimer);
          this.codeTime = 60;
        }
      }, 1000);
      console.log("获取验证码");

    },
    /**
     * 保存
     */
    save() {
      this.verify().then(() => {
        console.log("保存");
      }).catch((err) => {
        uni.showToast({
          title: err,
          icon: "none",
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.withdrawal-setting {
  .withdrawal-setting-content {
    padding: 20rpx;
    font-size: 26rpx;
    .withdrawal-setting-content-item {
      display: flex;
      padding: 25rpx;
      background-color: #fff;
      border-radius: 10rpx;
      margin-bottom: 20rpx;
      .withdrawal-setting-content-item-title {
        color: #000;
        margin-right: 50rpx;
        width: 180rpx;
      }
      .withdrawal-setting-content-item-input {
        display: flex;
        align-items: center;
        .get-code {
          color: #ff6203;
          transition: all 0.3s ease;
          &:active {
            opacity: 0.7;
          }
        }
      }
      input {
        color: #999;
      }
    }

    .withdrawal-setting-content-item-card {
      padding: 0 20rpx;
      background-color: #fff;
      border-radius: 10rpx;
      margin-bottom: 20rpx;

      .son-item {
        display: flex;
        align-items: center;
        border-bottom: 1rpx solid #f5f5f5;
        padding: 25rpx;
        &:last-child {
          border-bottom: none;
        }
        .son-item-title {
          margin-right: 50rpx;
          width: 180rpx;
        }
      }
    }

    .save-btn {
      width: 100%;
      height: 80rpx;
      border-radius: 50rpx;
      text-align: center;
      line-height: 80rpx;
      color: #fff;
      margin-top: 60rpx;
      transition: all 0.3s ease;
      &:active {
        opacity: 0.7;
      }
    }
  }
}
</style>
