// 简易深拷贝
export function deepCopy(val) {
    return JSON.parse(JSON.stringify((val)))
}

// 跳转页面
export function go(url, type = 1) {
    if(type === 1) {
        uni.navigateTo({
            url: url
        })
    } else if(type === 2) {
        uni.redirectTo({
            url: url
        })
    } else if(type === 3) {
        uni.reLaunch({
            url: url
        })
    } else if(type === 4) {
        uni.switchTab({
            url: url
        })
    } else if(type === 5) {
        uni.navigateBack({
            delta: url
        })
    }
}

// 获取二维码进入的参数
export function getQueryString(options) {
    if(options && options.scene) {
        return decodeURIComponent(options.scene).split('&');
    } else {
        return ""
    }
}

// 判断状态码并返回数据
export function checkStatus(code, data, status=0) {
    if(code === status) {
        return data
    }
}

// 判断对象是否为空
export function isEmptyObj(obj) {
    return obj && Object.keys(obj).length > 0;
}

// 判断数组是否为空
export function isEmptyArr(arr) {
    return arr && Array.isArray(arr) && arr.length > 0;
}

// 设置缓存
export function setStorage(key, value) {
    uni.setStorageSync(key, value)
}

// 获取缓存
export function getStorage(key) {
    return uni.getStorageSync(key)
}

// 删除指定缓存
export function removeStorage(key) {
    uni.removeStorageSync(key)
}

// 清空缓存
export function clearStorage() {
    uni.clearStorageSync()
}